'use client';

import React, { useState, useEffect } from 'react';
import { AIProvider, ProviderConfig, DEFAULT_MODELS } from '@/lib/ai-config';
import { useAIConfig } from '@/lib/ai-config/context';

interface ProviderFormProps {
  provider?: ProviderConfig;
  onSave: (config: Partial<ProviderConfig>) => Promise<void>;
  onCancel: () => void;
}

const ProviderForm: React.FC<ProviderFormProps> = ({ provider, onSave, onCancel }) => {
  const { getDefaultConfig, testProvider } = useAIConfig();
  const [formData, setFormData] = useState<Partial<ProviderConfig>>({
    name: '',
    provider: AIProvider.OPENAI,
    apiKey: '',
    model: '',
    temperature: 0.7,
    maxTokens: 4000,
    isDefault: false,
  });
  const [isLoading, setIsLoading] = useState(false);
  const [isTesting, setIsTesting] = useState(false);
  const [testResult, setTestResult] = useState<{ success: boolean; error?: string } | null>(null);
  const [errors, setErrors] = useState<string[]>([]);

  useEffect(() => {
    if (provider) {
      setFormData(provider);
    } else {
      // Set default values for new provider
      const defaultConfig = getDefaultConfig(AIProvider.OPENAI);
      setFormData({
        name: '',
        provider: AIProvider.OPENAI,
        apiKey: '',
        model: DEFAULT_MODELS[AIProvider.OPENAI][0],
        ...defaultConfig,
        isDefault: false,
      });
    }
  }, [provider, getDefaultConfig]);

  const handleProviderChange = (newProvider: AIProvider) => {
    const defaultConfig = getDefaultConfig(newProvider);
    const defaultModel = DEFAULT_MODELS[newProvider][0] || '';
    
    setFormData(prev => ({
      ...prev,
      provider: newProvider,
      model: defaultModel,
      ...defaultConfig,
    }));
  };

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
    setErrors([]);
    setTestResult(null);
  };

  const handleTestConnection = async () => {
    if (!formData.apiKey || !formData.model) {
      setErrors(['API key and model are required for testing']);
      return;
    }

    setIsTesting(true);
    setTestResult(null);
    
    try {
      const testConfig = formData as ProviderConfig;
      const result = await testProvider(testConfig);
      setTestResult(result);
    } catch (error) {
      setTestResult({
        success: false,
        error: error.message || 'Test failed',
      });
    } finally {
      setIsTesting(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setErrors([]);

    try {
      await onSave(formData);
    } catch (error) {
      setErrors([error.message || 'Failed to save provider']);
    } finally {
      setIsLoading(false);
    }
  };

  const availableModels = DEFAULT_MODELS[formData.provider as AIProvider] || [];

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {errors.length > 0 && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="text-red-700">
            <ul className="list-disc list-inside space-y-1">
              {errors.map((error, index) => (
                <li key={index}>{error}</li>
              ))}
            </ul>
          </div>
        </div>
      )}

      {/* Basic Information */}
      <div className="grid grid-cols-1 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Provider Name
          </label>
          <input
            type="text"
            value={formData.name || ''}
            onChange={(e) => handleInputChange('name', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="My OpenAI Provider"
            required
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Provider Type
          </label>
          <select
            value={formData.provider}
            onChange={(e) => handleProviderChange(e.target.value as AIProvider)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value={AIProvider.OPENAI}>OpenAI</option>
            <option value={AIProvider.ANTHROPIC}>Anthropic</option>
            <option value={AIProvider.GOOGLE}>Google</option>
            <option value={AIProvider.COHERE}>Cohere</option>
            <option value={AIProvider.CUSTOM_OPENAI}>Custom OpenAI-Compatible</option>
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            API Key
          </label>
          <input
            type="password"
            value={formData.apiKey || ''}
            onChange={(e) => handleInputChange('apiKey', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="sk-..."
            required
          />
        </div>

        {formData.provider === AIProvider.CUSTOM_OPENAI && (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Base URL
            </label>
            <input
              type="url"
              value={(formData as any).baseUrl || ''}
              onChange={(e) => handleInputChange('baseUrl', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="https://api.example.com/v1"
              required
            />
          </div>
        )}

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Model
          </label>
          <select
            value={formData.model || ''}
            onChange={(e) => handleInputChange('model', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            required
          >
            <option value="">Select a model</option>
            {availableModels.map((model) => (
              <option key={model} value={model}>
                {model}
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* Model Parameters */}
      <div className="border-t pt-6">
        <h4 className="text-lg font-medium text-gray-900 mb-4">Model Parameters</h4>
        <div className="grid grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Temperature ({formData.temperature})
            </label>
            <input
              type="range"
              min="0"
              max="2"
              step="0.1"
              value={formData.temperature || 0.7}
              onChange={(e) => handleInputChange('temperature', parseFloat(e.target.value))}
              className="w-full"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Max Tokens
            </label>
            <input
              type="number"
              min="1"
              max="100000"
              value={formData.maxTokens || 4000}
              onChange={(e) => handleInputChange('maxTokens', parseInt(e.target.value))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
        </div>
      </div>

      {/* Test Connection */}
      <div className="border-t pt-6">
        <div className="flex items-center justify-between mb-4">
          <h4 className="text-lg font-medium text-gray-900">Test Connection</h4>
          <button
            type="button"
            onClick={handleTestConnection}
            disabled={isTesting || !formData.apiKey || !formData.model}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isTesting ? 'Testing...' : 'Test Connection'}
          </button>
        </div>

        {testResult && (
          <div className={`p-4 rounded-md ${testResult.success ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'}`}>
            <div className={testResult.success ? 'text-green-700' : 'text-red-700'}>
              {testResult.success ? '✓ Connection successful!' : `✗ Connection failed: ${testResult.error}`}
            </div>
          </div>
        )}
      </div>

      {/* Default Provider */}
      <div className="border-t pt-6">
        <label className="flex items-center">
          <input
            type="checkbox"
            checked={formData.isDefault || false}
            onChange={(e) => handleInputChange('isDefault', e.target.checked)}
            className="mr-2"
          />
          <span className="text-sm font-medium text-gray-700">
            Set as default provider
          </span>
        </label>
      </div>

      {/* Actions */}
      <div className="flex justify-end space-x-4 pt-6 border-t">
        <button
          type="button"
          onClick={onCancel}
          className="px-4 py-2 text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300"
        >
          Cancel
        </button>
        <button
          type="submit"
          disabled={isLoading}
          className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isLoading ? 'Saving...' : 'Save Provider'}
        </button>
      </div>
    </form>
  );
};

export default ProviderForm;
