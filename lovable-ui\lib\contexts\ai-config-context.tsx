"use client";

import React, { createContext, useContext, useReducer, useEffect, ReactNode } from 'react';
import { 
  AIConfiguration, 
  ProviderConfig, 
  ModelParameters, 
  DEFAULT_AI_CONFIG,
  TestConnectionResult,
  ValidationResult
} from '../types/ai-config';

// Action Types
type AIConfigAction =
  | { type: 'SET_CONFIG'; payload: AIConfiguration }
  | { type: 'ADD_PROVIDER'; payload: ProviderConfig }
  | { type: 'UPDATE_PROVIDER'; payload: { id: string; config: Partial<ProviderConfig> } }
  | { type: 'REMOVE_PROVIDER'; payload: string }
  | { type: 'SET_DEFAULT_PROVIDER'; payload: string }
  | { type: 'SET_DEFAULT_MODEL'; payload: string }
  | { type: 'UPDATE_DEFAULT_PARAMETERS'; payload: Partial<ModelParameters> }
  | { type: 'TOGGLE_PROVIDER'; payload: string }
  | { type: 'RESET_CONFIG' };

// State Type
interface AIConfigState {
  config: AIConfiguration;
  isLoading: boolean;
  error: string | null;
}

// Context Type
interface AIConfigContextType {
  state: AIConfigState;
  actions: {
    setConfig: (config: AIConfiguration) => void;
    addProvider: (provider: ProviderConfig) => void;
    updateProvider: (id: string, updates: Partial<ProviderConfig>) => void;
    removeProvider: (id: string) => void;
    setDefaultProvider: (providerId: string) => void;
    setDefaultModel: (modelId: string) => void;
    updateDefaultParameters: (parameters: Partial<ModelParameters>) => void;
    toggleProvider: (id: string) => void;
    resetConfig: () => void;
    saveConfig: () => Promise<void>;
    loadConfig: () => Promise<void>;
    testConnection: (providerId: string) => Promise<TestConnectionResult>;
    validateProvider: (provider: ProviderConfig) => ValidationResult;
  };
}

// Reducer
function aiConfigReducer(state: AIConfigState, action: AIConfigAction): AIConfigState {
  switch (action.type) {
    case 'SET_CONFIG':
      return {
        ...state,
        config: action.payload,
        error: null,
      };

    case 'ADD_PROVIDER':
      return {
        ...state,
        config: {
          ...state.config,
          providers: [...state.config.providers, action.payload],
        },
      };

    case 'UPDATE_PROVIDER':
      return {
        ...state,
        config: {
          ...state.config,
          providers: state.config.providers.map(provider =>
            provider.id === action.payload.id
              ? { ...provider, ...action.payload.config }
              : provider
          ),
        },
      };

    case 'REMOVE_PROVIDER':
      const updatedProviders = state.config.providers.filter(p => p.id !== action.payload);
      return {
        ...state,
        config: {
          ...state.config,
          providers: updatedProviders,
          // Reset default provider if it was removed
          defaultProvider: state.config.defaultProvider === action.payload 
            ? updatedProviders[0]?.id 
            : state.config.defaultProvider,
        },
      };

    case 'SET_DEFAULT_PROVIDER':
      return {
        ...state,
        config: {
          ...state.config,
          defaultProvider: action.payload,
        },
      };

    case 'SET_DEFAULT_MODEL':
      return {
        ...state,
        config: {
          ...state.config,
          defaultModel: action.payload,
        },
      };

    case 'UPDATE_DEFAULT_PARAMETERS':
      return {
        ...state,
        config: {
          ...state.config,
          defaultParameters: {
            ...state.config.defaultParameters,
            ...action.payload,
          },
        },
      };

    case 'TOGGLE_PROVIDER':
      return {
        ...state,
        config: {
          ...state.config,
          providers: state.config.providers.map(provider =>
            provider.id === action.payload
              ? { ...provider, enabled: !provider.enabled }
              : provider
          ),
        },
      };

    case 'RESET_CONFIG':
      return {
        ...state,
        config: DEFAULT_AI_CONFIG,
        error: null,
      };

    default:
      return state;
  }
}

// Initial State
const initialState: AIConfigState = {
  config: DEFAULT_AI_CONFIG,
  isLoading: false,
  error: null,
};

// Context
const AIConfigContext = createContext<AIConfigContextType | undefined>(undefined);

// Storage Key
const STORAGE_KEY = 'ai-config';

// Provider Component
interface AIConfigProviderProps {
  children: ReactNode;
}

export function AIConfigProvider({ children }: AIConfigProviderProps) {
  const [state, dispatch] = useReducer(aiConfigReducer, initialState);

  // Load config on mount
  useEffect(() => {
    loadConfig();
  }, []);

  // Save config whenever it changes
  useEffect(() => {
    if (state.config !== DEFAULT_AI_CONFIG) {
      saveConfig();
    }
  }, [state.config]);

  const saveConfig = async () => {
    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(state.config));
    } catch (error) {
      console.error('Failed to save AI config:', error);
    }
  };

  const loadConfig = async () => {
    try {
      const saved = localStorage.getItem(STORAGE_KEY);
      if (saved) {
        const config = JSON.parse(saved);
        dispatch({ type: 'SET_CONFIG', payload: config });
      }
    } catch (error) {
      console.error('Failed to load AI config:', error);
    }
  };

  const testConnection = async (providerId: string): Promise<TestConnectionResult> => {
    const provider = state.config.providers.find(p => p.id === providerId);
    if (!provider) {
      return { success: false, error: 'Provider not found' };
    }

    try {
      const response = await fetch('/api/ai/test-connection', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ provider }),
      });

      return await response.json();
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Connection test failed' 
      };
    }
  };

  const validateProvider = (provider: ProviderConfig): ValidationResult => {
    const errors: string[] = [];

    if (!provider.name.trim()) {
      errors.push('Provider name is required');
    }

    if (!provider.apiKey.trim()) {
      errors.push('API key is required');
    }

    if (provider.provider === 'custom') {
      const customProvider = provider as any;
      if (!customProvider.baseURL?.trim()) {
        errors.push('Base URL is required for custom providers');
      }
      if (!customProvider.modelName?.trim()) {
        errors.push('Model name is required for custom providers');
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  };

  const actions = {
    setConfig: (config: AIConfiguration) => dispatch({ type: 'SET_CONFIG', payload: config }),
    addProvider: (provider: ProviderConfig) => dispatch({ type: 'ADD_PROVIDER', payload: provider }),
    updateProvider: (id: string, updates: Partial<ProviderConfig>) => 
      dispatch({ type: 'UPDATE_PROVIDER', payload: { id, config: updates } }),
    removeProvider: (id: string) => dispatch({ type: 'REMOVE_PROVIDER', payload: id }),
    setDefaultProvider: (providerId: string) => dispatch({ type: 'SET_DEFAULT_PROVIDER', payload: providerId }),
    setDefaultModel: (modelId: string) => dispatch({ type: 'SET_DEFAULT_MODEL', payload: modelId }),
    updateDefaultParameters: (parameters: Partial<ModelParameters>) => 
      dispatch({ type: 'UPDATE_DEFAULT_PARAMETERS', payload: parameters }),
    toggleProvider: (id: string) => dispatch({ type: 'TOGGLE_PROVIDER', payload: id }),
    resetConfig: () => dispatch({ type: 'RESET_CONFIG' }),
    saveConfig,
    loadConfig,
    testConnection,
    validateProvider,
  };

  return (
    <AIConfigContext.Provider value={{ state, actions }}>
      {children}
    </AIConfigContext.Provider>
  );
}

// Hook
export function useAIConfig() {
  const context = useContext(AIConfigContext);
  if (context === undefined) {
    throw new Error('useAIConfig must be used within an AIConfigProvider');
  }
  return context;
}
