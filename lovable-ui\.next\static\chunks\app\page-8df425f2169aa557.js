(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[931],{56:function(e,t,r){Promise.resolve().then(r.bind(r,6774))},6774:function(e,t,r){"use strict";r.r(t),r.d(t,{default:function(){return c}});var n=r(7437),s=r(8059),a=r.n(s),i=r(2265),o=r(6463),l=r(552);function c(){let e=(0,o.useRouter)(),[t,r]=(0,i.useState)(""),s=()=>{t.trim()&&e.push("/generate?prompt=".concat(encodeURIComponent(t)))};return(0,n.jsxs)("main",{className:"jsx-24f01c58ae8ac726 min-h-screen relative overflow-hidden bg-black",children:[(0,n.jsx)(l.Z,{}),(0,n.jsx)("div",{style:{backgroundImage:"url('/gradient.png')"},className:"jsx-24f01c58ae8ac726 absolute inset-0 z-0 bg-cover bg-center"}),(0,n.jsx)("div",{className:"jsx-24f01c58ae8ac726 relative z-10 flex flex-col items-center justify-center min-h-screen px-4 sm:px-6 lg:px-8",children:(0,n.jsxs)("div",{className:"jsx-24f01c58ae8ac726 max-w-4xl mx-auto text-center",children:[(0,n.jsx)("h1",{className:"jsx-24f01c58ae8ac726 text-4xl sm:text-4xl md:text-4xl font-bold text-white mb-6",children:"Build something with Lovable-clone"}),(0,n.jsx)("h3",{className:"jsx-24f01c58ae8ac726 text-xl sm:text-xl text-gray-300 mb-12 max-w-2xl mx-auto",children:"BUILT WITH CLAUDE CODE"}),(0,n.jsx)("p",{className:"jsx-24f01c58ae8ac726 text-xl sm:text-xl text-gray-300 mb-12 max-w-2xl mx-auto",children:"Turn your ideas into production-ready code in minutes. Powered by Claude's advanced AI capabilities."}),(0,n.jsxs)("div",{className:"jsx-24f01c58ae8ac726 relative max-w-2xl mx-auto",children:[(0,n.jsxs)("div",{className:"jsx-24f01c58ae8ac726 relative flex items-center bg-black rounded-2xl border border-gray-800 shadow-2xl px-2",children:[(0,n.jsx)("textarea",{placeholder:"Ask Lovable to create a prototype...",value:t,onChange:e=>r(e.target.value),onKeyDown:e=>{"Enter"!==e.key||e.shiftKey||(e.preventDefault(),s())},rows:3,className:"jsx-24f01c58ae8ac726 flex-1 px-5 py-4 bg-transparent text-white placeholder-gray-500 focus:outline-none text-lg resize-none min-h-[120px] max-h-[300px]"}),(0,n.jsx)("button",{onClick:s,disabled:!t.trim(),className:"jsx-24f01c58ae8ac726 flex-shrink-0 mr-3 p-3 bg-gray-800 hover:bg-gray-700 text-white rounded-xl focus:outline-none focus:ring-2 focus:ring-gray-600 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 group",children:(0,n.jsx)("svg",{fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",className:"jsx-24f01c58ae8ac726 h-5 w-5 group-hover:scale-110 transition-transform",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 10l7-7m0 0l7 7m-7-7v18",className:"jsx-24f01c58ae8ac726"})})})]}),(0,n.jsxs)("div",{className:"jsx-24f01c58ae8ac726 mt-8 flex flex-wrap justify-center gap-3",children:[(0,n.jsx)("button",{onClick:()=>r("Create a modern blog website with markdown support"),className:"jsx-24f01c58ae8ac726 px-4 py-2 text-sm text-gray-400 bg-gray-800/50 backdrop-blur-sm rounded-full hover:bg-gray-700/50 transition-colors border border-gray-700",children:"Blog website"}),(0,n.jsx)("button",{onClick:()=>r("Build a portfolio website with project showcase"),className:"jsx-24f01c58ae8ac726 px-4 py-2 text-sm text-gray-400 bg-gray-800/50 backdrop-blur-sm rounded-full hover:bg-gray-700/50 transition-colors border border-gray-700",children:"Portfolio site"}),(0,n.jsx)("button",{onClick:()=>r("Create an e-commerce product catalog with shopping cart"),className:"jsx-24f01c58ae8ac726 px-4 py-2 text-sm text-gray-400 bg-gray-800/50 backdrop-blur-sm rounded-full hover:bg-gray-700/50 transition-colors border border-gray-700",children:"E-commerce"}),(0,n.jsx)("button",{onClick:()=>r("Build a dashboard with charts and data visualization"),className:"jsx-24f01c58ae8ac726 px-4 py-2 text-sm text-gray-400 bg-gray-800/50 backdrop-blur-sm rounded-full hover:bg-gray-700/50 transition-colors border border-gray-700",children:"Dashboard"})]})]})]})}),(0,n.jsx)(a(),{id:"24f01c58ae8ac726",children:"@-webkit-keyframes blob{0%{-webkit-transform:translate(0px,0px)scale(1);transform:translate(0px,0px)scale(1)}33%{-webkit-transform:translate(30px,-50px)scale(1.1);transform:translate(30px,-50px)scale(1.1)}66%{-webkit-transform:translate(-20px,20px)scale(.9);transform:translate(-20px,20px)scale(.9)}100%{-webkit-transform:translate(0px,0px)scale(1);transform:translate(0px,0px)scale(1)}}@-moz-keyframes blob{0%{-moz-transform:translate(0px,0px)scale(1);transform:translate(0px,0px)scale(1)}33%{-moz-transform:translate(30px,-50px)scale(1.1);transform:translate(30px,-50px)scale(1.1)}66%{-moz-transform:translate(-20px,20px)scale(.9);transform:translate(-20px,20px)scale(.9)}100%{-moz-transform:translate(0px,0px)scale(1);transform:translate(0px,0px)scale(1)}}@-o-keyframes blob{0%{-o-transform:translate(0px,0px)scale(1);transform:translate(0px,0px)scale(1)}33%{-o-transform:translate(30px,-50px)scale(1.1);transform:translate(30px,-50px)scale(1.1)}66%{-o-transform:translate(-20px,20px)scale(.9);transform:translate(-20px,20px)scale(.9)}100%{-o-transform:translate(0px,0px)scale(1);transform:translate(0px,0px)scale(1)}}@keyframes blob{0%{-webkit-transform:translate(0px,0px)scale(1);-moz-transform:translate(0px,0px)scale(1);-o-transform:translate(0px,0px)scale(1);transform:translate(0px,0px)scale(1)}33%{-webkit-transform:translate(30px,-50px)scale(1.1);-moz-transform:translate(30px,-50px)scale(1.1);-o-transform:translate(30px,-50px)scale(1.1);transform:translate(30px,-50px)scale(1.1)}66%{-webkit-transform:translate(-20px,20px)scale(.9);-moz-transform:translate(-20px,20px)scale(.9);-o-transform:translate(-20px,20px)scale(.9);transform:translate(-20px,20px)scale(.9)}100%{-webkit-transform:translate(0px,0px)scale(1);-moz-transform:translate(0px,0px)scale(1);-o-transform:translate(0px,0px)scale(1);transform:translate(0px,0px)scale(1)}}.animate-blob.jsx-24f01c58ae8ac726{-webkit-animation:blob 7s infinite;-moz-animation:blob 7s infinite;-o-animation:blob 7s infinite;animation:blob 7s infinite}.animation-delay-2000.jsx-24f01c58ae8ac726{-webkit-animation-delay:2s;-moz-animation-delay:2s;-o-animation-delay:2s;animation-delay:2s}.animation-delay-4000.jsx-24f01c58ae8ac726{-webkit-animation-delay:4s;-moz-animation-delay:4s;-o-animation-delay:4s;animation-delay:4s}"})]})}},552:function(e,t,r){"use strict";r.d(t,{Z:function(){return s}});var n=r(7437);function s(){return(0,n.jsxs)("nav",{className:"absolute top-0 left-0 right-0 z-20 flex items-center justify-between px-6 py-4",children:[(0,n.jsxs)("div",{className:"flex items-center gap-10",children:[(0,n.jsxs)("a",{href:"/",className:"flex items-center gap-2 text-2xl font-semibold text-white hover:opacity-90 transition-opacity",children:[(0,n.jsx)("span",{className:"inline-block w-6 h-6 rounded-sm bg-gradient-to-br from-orange-400 via-pink-500 to-blue-500"}),"Lovable"]}),(0,n.jsxs)("div",{className:"hidden md:flex items-center gap-8 text-sm text-gray-300",children:[(0,n.jsx)("a",{href:"#",className:"hover:text-white transition-colors",children:"Community"}),(0,n.jsx)("a",{href:"#",className:"hover:text-white transition-colors",children:"Enterprise"}),(0,n.jsx)("a",{href:"#",className:"hover:text-white transition-colors",children:"Learn"}),(0,n.jsx)("a",{href:"#",className:"hover:text-white transition-colors",children:"Shipped"})]})]}),(0,n.jsxs)("div",{className:"flex items-center gap-4 text-sm",children:[(0,n.jsxs)("a",{href:"/settings",className:"text-gray-300 hover:text-white transition-colors flex items-center gap-1",title:"AI Settings",children:[(0,n.jsxs)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:[(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"}),(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"})]}),"Settings"]}),(0,n.jsx)("a",{href:"#",className:"text-gray-300 hover:text-white transition-colors",children:"Log in"}),(0,n.jsx)("a",{href:"#",className:"px-4 py-2 bg-white text-black rounded-lg font-semibold hover:bg-gray-100 transition-colors",children:"Get started"})]})]})}r(2265)},6463:function(e,t,r){"use strict";var n=r(1169);r.o(n,"useRouter")&&r.d(t,{useRouter:function(){return n.useRouter}}),r.o(n,"useSearchParams")&&r.d(t,{useSearchParams:function(){return n.useSearchParams}})},357:function(e,t,r){"use strict";var n,s;e.exports=(null==(n=r.g.process)?void 0:n.env)&&"object"==typeof(null==(s=r.g.process)?void 0:s.env)?r.g.process:r(8081)},6810:function(){},8081:function(e){!function(){var t={229:function(e){var t,r,n,s=e.exports={};function a(){throw Error("setTimeout has not been defined")}function i(){throw Error("clearTimeout has not been defined")}function o(e){if(t===setTimeout)return setTimeout(e,0);if((t===a||!t)&&setTimeout)return t=setTimeout,setTimeout(e,0);try{return t(e,0)}catch(r){try{return t.call(null,e,0)}catch(r){return t.call(this,e,0)}}}!function(){try{t="function"==typeof setTimeout?setTimeout:a}catch(e){t=a}try{r="function"==typeof clearTimeout?clearTimeout:i}catch(e){r=i}}();var l=[],c=!1,u=-1;function d(){c&&n&&(c=!1,n.length?l=n.concat(l):u=-1,l.length&&h())}function h(){if(!c){var e=o(d);c=!0;for(var t=l.length;t;){for(n=l,l=[];++u<t;)n&&n[u].run();u=-1,t=l.length}n=null,c=!1,function(e){if(r===clearTimeout)return clearTimeout(e);if((r===i||!r)&&clearTimeout)return r=clearTimeout,clearTimeout(e);try{r(e)}catch(t){try{return r.call(null,e)}catch(t){return r.call(this,e)}}}(e)}}function f(e,t){this.fun=e,this.array=t}function p(){}s.nextTick=function(e){var t=Array(arguments.length-1);if(arguments.length>1)for(var r=1;r<arguments.length;r++)t[r-1]=arguments[r];l.push(new f(e,t)),1!==l.length||c||o(h)},f.prototype.run=function(){this.fun.apply(null,this.array)},s.title="browser",s.browser=!0,s.env={},s.argv=[],s.version="",s.versions={},s.on=p,s.addListener=p,s.once=p,s.off=p,s.removeListener=p,s.removeAllListeners=p,s.emit=p,s.prependListener=p,s.prependOnceListener=p,s.listeners=function(e){return[]},s.binding=function(e){throw Error("process.binding is not supported")},s.cwd=function(){return"/"},s.chdir=function(e){throw Error("process.chdir is not supported")},s.umask=function(){return 0}}},r={};function n(e){var s=r[e];if(void 0!==s)return s.exports;var a=r[e]={exports:{}},i=!0;try{t[e](a,a.exports,n),i=!1}finally{i&&delete r[e]}return a.exports}n.ab="//";var s=n(229);e.exports=s}()},3398:function(e,t,r){"use strict";var n=r(357);r(6810);var s=r(2265),a=s&&"object"==typeof s&&"default"in s?s:{default:s},i=void 0!==n&&n.env&&!0,o=function(e){return"[object String]"===Object.prototype.toString.call(e)},l=function(){function e(e){var t=void 0===e?{}:e,r=t.name,n=void 0===r?"stylesheet":r,s=t.optimizeForSpeed,a=void 0===s?i:s;c(o(n),"`name` must be a string"),this._name=n,this._deletedRulePlaceholder="#"+n+"-deleted-rule____{}",c("boolean"==typeof a,"`optimizeForSpeed` must be a boolean"),this._optimizeForSpeed=a,this._serverSheet=void 0,this._tags=[],this._injected=!1,this._rulesCount=0;var l="undefined"!=typeof window&&document.querySelector('meta[property="csp-nonce"]');this._nonce=l?l.getAttribute("content"):null}var t=e.prototype;return t.setOptimizeForSpeed=function(e){c("boolean"==typeof e,"`setOptimizeForSpeed` accepts a boolean"),c(0===this._rulesCount,"optimizeForSpeed cannot be when rules have already been inserted"),this.flush(),this._optimizeForSpeed=e,this.inject()},t.isOptimizeForSpeed=function(){return this._optimizeForSpeed},t.inject=function(){var e=this;if(c(!this._injected,"sheet already injected"),this._injected=!0,"undefined"!=typeof window&&this._optimizeForSpeed){this._tags[0]=this.makeStyleTag(this._name),this._optimizeForSpeed="insertRule"in this.getSheet(),this._optimizeForSpeed||(i||console.warn("StyleSheet: optimizeForSpeed mode not supported falling back to standard mode."),this.flush(),this._injected=!0);return}this._serverSheet={cssRules:[],insertRule:function(t,r){return"number"==typeof r?e._serverSheet.cssRules[r]={cssText:t}:e._serverSheet.cssRules.push({cssText:t}),r},deleteRule:function(t){e._serverSheet.cssRules[t]=null}}},t.getSheetForTag=function(e){if(e.sheet)return e.sheet;for(var t=0;t<document.styleSheets.length;t++)if(document.styleSheets[t].ownerNode===e)return document.styleSheets[t]},t.getSheet=function(){return this.getSheetForTag(this._tags[this._tags.length-1])},t.insertRule=function(e,t){if(c(o(e),"`insertRule` accepts only strings"),"undefined"==typeof window)return"number"!=typeof t&&(t=this._serverSheet.cssRules.length),this._serverSheet.insertRule(e,t),this._rulesCount++;if(this._optimizeForSpeed){var r=this.getSheet();"number"!=typeof t&&(t=r.cssRules.length);try{r.insertRule(e,t)}catch(t){return i||console.warn("StyleSheet: illegal rule: \n\n"+e+"\n\nSee https://stackoverflow.com/q/20007992 for more info"),-1}}else{var n=this._tags[t];this._tags.push(this.makeStyleTag(this._name,e,n))}return this._rulesCount++},t.replaceRule=function(e,t){if(this._optimizeForSpeed||"undefined"==typeof window){var r="undefined"!=typeof window?this.getSheet():this._serverSheet;if(t.trim()||(t=this._deletedRulePlaceholder),!r.cssRules[e])return e;r.deleteRule(e);try{r.insertRule(t,e)}catch(n){i||console.warn("StyleSheet: illegal rule: \n\n"+t+"\n\nSee https://stackoverflow.com/q/20007992 for more info"),r.insertRule(this._deletedRulePlaceholder,e)}}else{var n=this._tags[e];c(n,"old rule at index `"+e+"` not found"),n.textContent=t}return e},t.deleteRule=function(e){if("undefined"==typeof window){this._serverSheet.deleteRule(e);return}if(this._optimizeForSpeed)this.replaceRule(e,"");else{var t=this._tags[e];c(t,"rule at index `"+e+"` not found"),t.parentNode.removeChild(t),this._tags[e]=null}},t.flush=function(){this._injected=!1,this._rulesCount=0,"undefined"!=typeof window?(this._tags.forEach(function(e){return e&&e.parentNode.removeChild(e)}),this._tags=[]):this._serverSheet.cssRules=[]},t.cssRules=function(){var e=this;return"undefined"==typeof window?this._serverSheet.cssRules:this._tags.reduce(function(t,r){return r?t=t.concat(Array.prototype.map.call(e.getSheetForTag(r).cssRules,function(t){return t.cssText===e._deletedRulePlaceholder?null:t})):t.push(null),t},[])},t.makeStyleTag=function(e,t,r){t&&c(o(t),"makeStyleTag accepts only strings as second parameter");var n=document.createElement("style");this._nonce&&n.setAttribute("nonce",this._nonce),n.type="text/css",n.setAttribute("data-"+e,""),t&&n.appendChild(document.createTextNode(t));var s=document.head||document.getElementsByTagName("head")[0];return r?s.insertBefore(n,r):s.appendChild(n),n},function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}(e.prototype,[{key:"length",get:function(){return this._rulesCount}}]),e}();function c(e,t){if(!e)throw Error("StyleSheet: "+t+".")}var u=function(e){for(var t=5381,r=e.length;r;)t=33*t^e.charCodeAt(--r);return t>>>0},d={};function h(e,t){if(!t)return"jsx-"+e;var r=String(t),n=e+r;return d[n]||(d[n]="jsx-"+u(e+"-"+r)),d[n]}function f(e,t){"undefined"==typeof window&&(t=t.replace(/\/style/gi,"\\/style"));var r=e+t;return d[r]||(d[r]=t.replace(/__jsx-style-dynamic-selector/g,e)),d[r]}var p=function(){function e(e){var t=void 0===e?{}:e,r=t.styleSheet,n=void 0===r?null:r,s=t.optimizeForSpeed,a=void 0!==s&&s;this._sheet=n||new l({name:"styled-jsx",optimizeForSpeed:a}),this._sheet.inject(),n&&"boolean"==typeof a&&(this._sheet.setOptimizeForSpeed(a),this._optimizeForSpeed=this._sheet.isOptimizeForSpeed()),this._fromServer=void 0,this._indices={},this._instancesCounts={}}var t=e.prototype;return t.add=function(e){var t=this;void 0===this._optimizeForSpeed&&(this._optimizeForSpeed=Array.isArray(e.children),this._sheet.setOptimizeForSpeed(this._optimizeForSpeed),this._optimizeForSpeed=this._sheet.isOptimizeForSpeed()),"undefined"==typeof window||this._fromServer||(this._fromServer=this.selectFromServer(),this._instancesCounts=Object.keys(this._fromServer).reduce(function(e,t){return e[t]=0,e},{}));var r=this.getIdAndRules(e),n=r.styleId,s=r.rules;if(n in this._instancesCounts){this._instancesCounts[n]+=1;return}var a=s.map(function(e){return t._sheet.insertRule(e)}).filter(function(e){return -1!==e});this._indices[n]=a,this._instancesCounts[n]=1},t.remove=function(e){var t=this,r=this.getIdAndRules(e).styleId;if(function(e,t){if(!e)throw Error("StyleSheetRegistry: "+t+".")}(r in this._instancesCounts,"styleId: `"+r+"` not found"),this._instancesCounts[r]-=1,this._instancesCounts[r]<1){var n=this._fromServer&&this._fromServer[r];n?(n.parentNode.removeChild(n),delete this._fromServer[r]):(this._indices[r].forEach(function(e){return t._sheet.deleteRule(e)}),delete this._indices[r]),delete this._instancesCounts[r]}},t.update=function(e,t){this.add(t),this.remove(e)},t.flush=function(){this._sheet.flush(),this._sheet.inject(),this._fromServer=void 0,this._indices={},this._instancesCounts={}},t.cssRules=function(){var e=this,t=this._fromServer?Object.keys(this._fromServer).map(function(t){return[t,e._fromServer[t]]}):[],r=this._sheet.cssRules();return t.concat(Object.keys(this._indices).map(function(t){return[t,e._indices[t].map(function(e){return r[e].cssText}).join(e._optimizeForSpeed?"":"\n")]}).filter(function(e){return!!e[1]}))},t.styles=function(e){var t,r;return t=this.cssRules(),void 0===(r=e)&&(r={}),t.map(function(e){var t=e[0],n=e[1];return a.default.createElement("style",{id:"__"+t,key:"__"+t,nonce:r.nonce?r.nonce:void 0,dangerouslySetInnerHTML:{__html:n}})})},t.getIdAndRules=function(e){var t=e.children,r=e.dynamic,n=e.id;if(r){var s=h(n,r);return{styleId:s,rules:Array.isArray(t)?t.map(function(e){return f(s,e)}):[f(s,t)]}}return{styleId:h(n),rules:Array.isArray(t)?t:[t]}},t.selectFromServer=function(){return Array.prototype.slice.call(document.querySelectorAll('[id^="__jsx-"]')).reduce(function(e,t){return e[t.id.slice(2)]=t,e},{})},e}(),m=s.createContext(null);m.displayName="StyleSheetContext";var x=a.default.useInsertionEffect||a.default.useLayoutEffect,y="undefined"!=typeof window?new p:void 0;function v(e){var t=y||s.useContext(m);return t&&("undefined"==typeof window?t.add(e):x(function(){return t.add(e),function(){t.remove(e)}},[e.id,String(e.dynamic)])),null}v.dynamic=function(e){return e.map(function(e){return h(e[0],e[1])}).join(" ")},t.style=v},8059:function(e,t,r){"use strict";e.exports=r(3398).style}},function(e){e.O(0,[971,23,744],function(){return e(e.s=56)}),_N_E=e.O()}]);