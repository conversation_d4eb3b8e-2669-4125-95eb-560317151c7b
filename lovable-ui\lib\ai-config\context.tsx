'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { AIConfigManager, aiConfigManager, ProviderConfig, AIProvider } from './index';

interface AIConfigContextType {
  providers: ProviderConfig[];
  defaultProvider: ProviderConfig | null;
  isLoading: boolean;
  error: string | null;
  saveProvider: (config: Partial<ProviderConfig>) => Promise<{
    success: boolean;
    provider?: ProviderConfig;
    errors?: string[];
  }>;
  deleteProvider: (id: string) => boolean;
  setDefaultProvider: (id: string) => boolean;
  testProvider: (config: ProviderConfig) => Promise<{
    success: boolean;
    error?: string;
  }>;
  getModels: (provider: AIProvider, apiKey: string) => Promise<string[]>;
  getDefaultConfig: (provider: AIProvider) => Partial<ProviderConfig>;
  refreshProviders: () => void;
}

const AIConfigContext = createContext<AIConfigContextType | undefined>(undefined);

export const AIConfigProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [providers, setProviders] = useState<ProviderConfig[]>([]);
  const [defaultProvider, setDefaultProvider] = useState<ProviderConfig | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const refreshProviders = () => {
    try {
      const allProviders = aiConfigManager.getProviders();
      const defaultProv = aiConfigManager.getDefaultProvider();
      
      setProviders(allProviders);
      setDefaultProvider(defaultProv);
      setError(null);
    } catch (err) {
      setError('Failed to load AI providers: ' + (err.message || 'Unknown error'));
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    // Initialize providers on client-side only
    if (typeof window !== 'undefined') {
      refreshProviders();
    }
  }, []);

  const saveProvider = async (config: Partial<ProviderConfig>) => {
    const result = await aiConfigManager.saveProvider(config);
    if (result.success) {
      refreshProviders();
    }
    return result;
  };

  const deleteProviderHandler = (id: string) => {
    const result = aiConfigManager.deleteProvider(id);
    if (result) {
      refreshProviders();
    }
    return result;
  };

  const setDefaultProviderHandler = (id: string) => {
    const result = aiConfigManager.setDefault(id);
    if (result) {
      refreshProviders();
    }
    return result;
  };

  const testProvider = async (config: ProviderConfig) => {
    return aiConfigManager.testProvider(config);
  };

  const getModels = async (provider: AIProvider, apiKey: string) => {
    return aiConfigManager.getModels(provider, apiKey);
  };

  const getDefaultConfig = (provider: AIProvider) => {
    return aiConfigManager.getDefaultConfig(provider);
  };

  const value = {
    providers,
    defaultProvider,
    isLoading,
    error,
    saveProvider,
    deleteProvider: deleteProviderHandler,
    setDefaultProvider: setDefaultProviderHandler,
    testProvider,
    getModels,
    getDefaultConfig,
    refreshProviders,
  };

  return (
    <AIConfigContext.Provider value={value}>
      {children}
    </AIConfigContext.Provider>
  );
};

export const useAIConfig = () => {
  const context = useContext(AIConfigContext);
  if (context === undefined) {
    throw new Error('useAIConfig must be used within an AIConfigProvider');
  }
  return context;
};

export default AIConfigContext;
