/**
 * AI Client Factory
 * 
 * This file provides utilities for creating AI clients based on provider configurations.
 */

import { openai } from '@ai-sdk/openai';
import { anthropic } from '@ai-sdk/anthropic';
import { google } from '@ai-sdk/google';
import { cohere } from '@ai-sdk/cohere';
import { generateText, streamText, CoreMessage } from 'ai';
import { 
  AIProvider, 
  ProviderConfig, 
  OpenAIConfig, 
  AnthropicConfig, 
  GoogleConfig, 
  CohereConfig, 
  CustomOpenAIConfig 
} from './types';

/**
 * Create an AI client based on the provider configuration
 */
export const createAIClient = (config: ProviderConfig) => {
  switch (config.provider) {
    case AIProvider.OPENAI:
      return openai({
        apiKey: config.apiKey,
      });
    
    case AIProvider.ANTHROPIC:
      return anthropic({
        apiKey: config.apiKey,
      });
    
    case AIProvider.GOOGLE:
      return google({
        apiKey: config.apiKey,
      });
    
    case AIProvider.COHERE:
      return cohere({
        apiKey: config.apiKey,
      });
    
    case AIProvider.CUSTOM_OPENAI:
      const customConfig = config as CustomOpenAIConfig;
      return openai({
        apiKey: customConfig.apiKey,
        baseURL: customConfig.baseUrl,
      });
    
    default:
      throw new Error(`Unsupported provider: ${config.provider}`);
  }
};

/**
 * Get model parameters based on provider configuration
 */
export const getModelParameters = (config: ProviderConfig) => {
  const baseParams = {
    temperature: config.temperature,
    maxTokens: config.maxTokens,
  };

  switch (config.provider) {
    case AIProvider.OPENAI:
    case AIProvider.CUSTOM_OPENAI:
      const openaiConfig = config as OpenAIConfig | CustomOpenAIConfig;
      return {
        ...baseParams,
        topP: openaiConfig.topP,
        frequencyPenalty: openaiConfig.frequencyPenalty,
        presencePenalty: openaiConfig.presencePenalty,
      };
    
    case AIProvider.ANTHROPIC:
      const anthropicConfig = config as AnthropicConfig;
      return {
        ...baseParams,
        topP: anthropicConfig.topP,
        topK: anthropicConfig.topK,
      };
    
    case AIProvider.GOOGLE:
      const googleConfig = config as GoogleConfig;
      return {
        ...baseParams,
        topP: googleConfig.topP,
        topK: googleConfig.topK,
      };
    
    case AIProvider.COHERE:
      const cohereConfig = config as CohereConfig;
      return {
        ...baseParams,
        topP: cohereConfig.topP,
        frequencyPenalty: cohereConfig.frequencyPenalty,
        presencePenalty: cohereConfig.presencePenalty,
      };
    
    default:
      return baseParams;
  }
};

/**
 * Generate text using the configured AI provider
 */
export const generateAIText = async (
  config: ProviderConfig,
  messages: CoreMessage[]
): Promise<string> => {
  try {
    const client = createAIClient(config);
    const model = client(config.model);
    const parameters = getModelParameters(config);

    const result = await generateText({
      model,
      messages,
      ...parameters,
    });

    return result.text;
  } catch (error) {
    console.error('Error generating AI text:', error);
    throw new Error(`Failed to generate text with ${config.provider}: ${error.message}`);
  }
};

/**
 * Stream text using the configured AI provider
 */
export const streamAIText = async (
  config: ProviderConfig,
  messages: CoreMessage[]
) => {
  try {
    const client = createAIClient(config);
    const model = client(config.model);
    const parameters = getModelParameters(config);

    const result = await streamText({
      model,
      messages,
      ...parameters,
    });

    return result;
  } catch (error) {
    console.error('Error streaming AI text:', error);
    throw new Error(`Failed to stream text with ${config.provider}: ${error.message}`);
  }
};

/**
 * Test connection to an AI provider
 */
export const testProviderConnection = async (config: ProviderConfig): Promise<{
  success: boolean;
  error?: string;
}> => {
  try {
    const testMessage: CoreMessage = {
      role: 'user',
      content: 'Hello, this is a connection test. Please respond with "Connection successful".',
    };

    const response = await generateAIText(config, [testMessage]);
    
    return {
      success: true,
    };
  } catch (error) {
    return {
      success: false,
      error: error.message || 'Unknown error occurred',
    };
  }
};

/**
 * Get available models for a provider (this would typically come from the provider's API)
 */
export const getAvailableModels = async (provider: AIProvider, apiKey: string): Promise<string[]> => {
  // For now, return default models. In a real implementation, you might query the provider's API
  // to get the actual available models for the given API key.
  
  const { DEFAULT_MODELS } = await import('./types');
  return DEFAULT_MODELS[provider] || [];
};

/**
 * Validate provider configuration
 */
export const validateProviderConfig = (config: ProviderConfig): {
  isValid: boolean;
  errors: string[];
} => {
  const errors: string[] = [];

  // Basic validation
  if (!config.name?.trim()) {
    errors.push('Name is required');
  }

  if (!config.apiKey?.trim()) {
    errors.push('API key is required');
  }

  if (!config.model?.trim()) {
    errors.push('Model is required');
  }

  // Provider-specific validation
  if (config.provider === AIProvider.CUSTOM_OPENAI) {
    const customConfig = config as CustomOpenAIConfig;
    if (!customConfig.baseUrl?.trim()) {
      errors.push('Base URL is required for custom OpenAI providers');
    } else {
      try {
        new URL(customConfig.baseUrl);
      } catch {
        errors.push('Base URL must be a valid URL');
      }
    }
  }

  // Parameter validation
  if (config.temperature < 0 || config.temperature > 2) {
    errors.push('Temperature must be between 0 and 2');
  }

  if (config.maxTokens < 1 || config.maxTokens > 100000) {
    errors.push('Max tokens must be between 1 and 100,000');
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
};
