import { Daytona } from "@daytonaio/sdk";
import * as dotenv from "dotenv";
import * as path from "path";

// Load environment variables
dotenv.config({ path: path.join(__dirname, "../../.env") });

async function removeSandbox(sandboxId: string) {
  if (!process.env.DAYTONA_API_KEY) {
    console.error("ERROR: DAYTONA_API_KEY must be set");
    process.exit(1);
  }

  const daytona = new Daytona({
    apiKey: process.env.DAYTONA_API_KEY,
  });

  try {
    console.log(`Removing sandbox: ${sandboxId}...`);
    
    // Get the sandbox first
    const sandboxes = await daytona.list();
    const sandbox = sandboxes.find((s: any) => s.id === sandboxId);
    
    if (!sandbox) {
      throw new Error(`Sandbox ${sandboxId} not found`);
    }
    
    // Delete the sandbox
    await sandbox.delete();
    console.log("✓ Sandbox removed successfully");
  } catch (error: any) {
    console.error("Failed to remove sandbox:", error.message);
    process.exit(1);
  }
}

// Main execution
const sandboxId = process.argv[2];

if (!sandboxId) {
  console.error("Usage: npx tsx scripts/remove-sandbox.ts <sandbox-id>");
  console.error("Example: npx tsx scripts/remove-sandbox.ts 7a517a82-942c-486b-8a62-6357773eb3ea");
  process.exit(1);
}

removeSandbox(sandboxId);
