(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[185],{8935:function(e,o,r){Promise.resolve().then(r.bind(r,2383)),Promise.resolve().then(r.t.bind(r,936,23)),Promise.resolve().then(r.t.bind(r,8877,23))},2383:function(e,o,r){"use strict";r.d(o,{AIConfigProvider:function(){return u},m:function(){return c}});var i=r(7437),t=r(2265),n=r(8506);function s(e,o){switch(o.type){case"SET_CONFIG":return{...e,config:o.payload,error:null};case"ADD_PROVIDER":return{...e,config:{...e.config,providers:[...e.config.providers,o.payload]}};case"UPDATE_PROVIDER":return{...e,config:{...e.config,providers:e.config.providers.map(e=>e.id===o.payload.id?{...e,...o.payload.config}:e)}};case"REMOVE_PROVIDER":var r;let i=e.config.providers.filter(e=>e.id!==o.payload);return{...e,config:{...e.config,providers:i,defaultProvider:e.config.defaultProvider===o.payload?null===(r=i[0])||void 0===r?void 0:r.id:e.config.defaultProvider}};case"SET_DEFAULT_PROVIDER":return{...e,config:{...e.config,defaultProvider:o.payload}};case"SET_DEFAULT_MODEL":return{...e,config:{...e.config,defaultModel:o.payload}};case"UPDATE_DEFAULT_PARAMETERS":return{...e,config:{...e.config,defaultParameters:{...e.config.defaultParameters,...o.payload}}};case"TOGGLE_PROVIDER":return{...e,config:{...e.config,providers:e.config.providers.map(e=>e.id===o.payload?{...e,enabled:!e.enabled}:e)}};case"RESET_CONFIG":return{...e,config:n.T,error:null};default:return e}}let a={config:n.T,isLoading:!1,error:null},p=(0,t.createContext)(void 0),d="ai-config";function u(e){let{children:o}=e,[r,u]=(0,t.useReducer)(s,a);(0,t.useEffect)(()=>{l()},[]),(0,t.useEffect)(()=>{r.config!==n.T&&c()},[r.config]);let c=async()=>{try{localStorage.setItem(d,JSON.stringify(r.config))}catch(e){console.error("Failed to save AI config:",e)}},l=async()=>{try{let e=localStorage.getItem(d);if(e){let o=JSON.parse(e);u({type:"SET_CONFIG",payload:o})}}catch(e){console.error("Failed to load AI config:",e)}},m=async e=>{let o=r.config.providers.find(o=>o.id===e);if(!o)return{success:!1,error:"Provider not found"};try{let e=await fetch("/api/ai/test-connection",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({provider:o})});return await e.json()}catch(e){return{success:!1,error:e instanceof Error?e.message:"Connection test failed"}}};return(0,i.jsx)(p.Provider,{value:{state:r,actions:{setConfig:e=>u({type:"SET_CONFIG",payload:e}),addProvider:e=>u({type:"ADD_PROVIDER",payload:e}),updateProvider:(e,o)=>u({type:"UPDATE_PROVIDER",payload:{id:e,config:o}}),removeProvider:e=>u({type:"REMOVE_PROVIDER",payload:e}),setDefaultProvider:e=>u({type:"SET_DEFAULT_PROVIDER",payload:e}),setDefaultModel:e=>u({type:"SET_DEFAULT_MODEL",payload:e}),updateDefaultParameters:e=>u({type:"UPDATE_DEFAULT_PARAMETERS",payload:e}),toggleProvider:e=>u({type:"TOGGLE_PROVIDER",payload:e}),resetConfig:()=>u({type:"RESET_CONFIG"}),saveConfig:c,loadConfig:l,testConnection:m,validateProvider:e=>{let o=[];if(e.name.trim()||o.push("Provider name is required"),e.apiKey.trim()||o.push("API key is required"),"custom"===e.provider){var r,i;(null===(r=e.baseURL)||void 0===r?void 0:r.trim())||o.push("Base URL is required for custom providers"),(null===(i=e.modelName)||void 0===i?void 0:i.trim())||o.push("Model name is required for custom providers")}return{isValid:0===o.length,errors:o}}}},children:o})}function c(){let e=(0,t.useContext)(p);if(void 0===e)throw Error("useAIConfig must be used within an AIConfigProvider");return e}},8506:function(e,o,r){"use strict";r.d(o,{T:function(){return t},k:function(){return i}});let i={openai:[{id:"gpt-4o",name:"GPT-4o",provider:"openai",maxTokens:4096,supportsStreaming:!0,supportsTools:!0,supportsVision:!0,contextWindow:128e3},{id:"gpt-4o-mini",name:"GPT-4o Mini",provider:"openai",maxTokens:16384,supportsStreaming:!0,supportsTools:!0,supportsVision:!0,contextWindow:128e3},{id:"gpt-4-turbo",name:"GPT-4 Turbo",provider:"openai",maxTokens:4096,supportsStreaming:!0,supportsTools:!0,supportsVision:!0,contextWindow:128e3},{id:"gpt-3.5-turbo",name:"GPT-3.5 Turbo",provider:"openai",maxTokens:4096,supportsStreaming:!0,supportsTools:!0,supportsVision:!1,contextWindow:16385}],anthropic:[{id:"claude-3-5-sonnet-20241022",name:"Claude 3.5 Sonnet",provider:"anthropic",maxTokens:8192,supportsStreaming:!0,supportsTools:!0,supportsVision:!0,contextWindow:2e5},{id:"claude-3-5-haiku-20241022",name:"Claude 3.5 Haiku",provider:"anthropic",maxTokens:8192,supportsStreaming:!0,supportsTools:!0,supportsVision:!0,contextWindow:2e5},{id:"claude-3-opus-20240229",name:"Claude 3 Opus",provider:"anthropic",maxTokens:4096,supportsStreaming:!0,supportsTools:!0,supportsVision:!0,contextWindow:2e5}],google:[{id:"gemini-1.5-pro",name:"Gemini 1.5 Pro",provider:"google",maxTokens:8192,supportsStreaming:!0,supportsTools:!0,supportsVision:!0,contextWindow:2e6},{id:"gemini-1.5-flash",name:"Gemini 1.5 Flash",provider:"google",maxTokens:8192,supportsStreaming:!0,supportsTools:!0,supportsVision:!0,contextWindow:1e6}],cohere:[{id:"command-r-plus",name:"Command R+",provider:"cohere",maxTokens:4096,supportsStreaming:!0,supportsTools:!0,supportsVision:!1,contextWindow:128e3},{id:"command-r",name:"Command R",provider:"cohere",maxTokens:4096,supportsStreaming:!0,supportsTools:!0,supportsVision:!1,contextWindow:128e3}],mistral:[{id:"mistral-large-latest",name:"Mistral Large",provider:"mistral",maxTokens:8192,supportsStreaming:!0,supportsTools:!0,supportsVision:!1,contextWindow:128e3},{id:"mistral-medium-latest",name:"Mistral Medium",provider:"mistral",maxTokens:8192,supportsStreaming:!0,supportsTools:!0,supportsVision:!1,contextWindow:32e3}],custom:[]},t={providers:[],defaultParameters:{temperature:.7,maxTokens:4096,topP:1,frequencyPenalty:0,presencePenalty:0}}},8877:function(){},936:function(e){e.exports={style:{fontFamily:"'__Inter_e8ce0c', '__Inter_Fallback_e8ce0c'",fontStyle:"normal"},className:"__className_e8ce0c"}}},function(e){e.O(0,[232,971,23,744],function(){return e(e.s=8935)}),_N_E=e.O()}]);