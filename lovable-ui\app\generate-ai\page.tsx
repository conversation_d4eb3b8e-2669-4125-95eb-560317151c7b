"use client";

import { useState, useEffect, Suspense } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { useAIConfig } from "@/lib/ai-config/context";
import { ProviderConfig } from "@/lib/ai-config";

interface Message {
  type: string;
  content?: string;
  message?: string;
}

function GenerateAIPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { providers, defaultProvider } = useAIConfig();
  
  const [messages, setMessages] = useState<Message[]>([]);
  const [isGenerating, setIsGenerating] = useState(false);
  const [error, setError] = useState("");
  const [selectedProvider, setSelectedProvider] = useState<ProviderConfig | null>(null);

  const prompt = searchParams?.get("prompt") || "";

  useEffect(() => {
    if (!prompt) {
      router.push("/");
      return;
    }

    // Set the default provider when available
    if (defaultProvider && !selectedProvider) {
      setSelectedProvider(defaultProvider);
    }
  }, [prompt, router, defaultProvider, selectedProvider]);

  useEffect(() => {
    if (selectedProvider && prompt) {
      generateCode();
    }
  }, [selectedProvider, prompt]);

  const generateCode = async () => {
    if (!selectedProvider) {
      setError("No AI provider selected");
      return;
    }

    setIsGenerating(true);
    setMessages([]);
    setError("");

    try {
      const response = await fetch("/api/generate-ai", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ 
          prompt,
          providerConfig: selectedProvider,
        }),
      });

      if (!response.ok) {
        throw new Error(`Error: ${response.status}`);
      }

      if (!response.body) {
        throw new Error("ReadableStream not supported");
      }

      // Set up SSE
      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      let buffer = "";

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split("\n\n");
        buffer = lines.pop() || "";

        for (const line of lines) {
          if (line.startsWith("data: ")) {
            const data = line.substring(6);
            if (data === "[DONE]") {
              setIsGenerating(false);
              return;
            }
            
            try {
              const parsed = JSON.parse(data);
              setMessages((prev) => [...prev, parsed as Message]);
              
              if (parsed.type === "error") {
                setError(parsed.message || "Generation failed");
                setIsGenerating(false);
                return;
              }
            } catch (e) {
              console.error("Failed to parse SSE message:", e);
            }
          }
        }
      }
    } catch (err: any) {
      console.error("Generation error:", err);
      setError(err.message || "Failed to generate code");
    } finally {
      setIsGenerating(false);
    }
  };

  const handleProviderChange = (provider: ProviderConfig) => {
    setSelectedProvider(provider);
    setMessages([]);
    setError("");
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-3xl font-bold mb-6 text-gray-900">AI Code Generation</h1>
          
          {/* Provider Selection */}
          <div className="bg-white rounded-lg shadow-sm border p-6 mb-6">
            <h2 className="text-lg font-semibold mb-4 text-gray-900">AI Provider</h2>
            {providers.length === 0 ? (
              <div className="text-center py-8">
                <p className="text-gray-500 mb-4">No AI providers configured</p>
                <p className="text-sm text-gray-400">
                  Please configure an AI provider in the settings to start generating code.
                </p>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {providers.map((provider) => (
                  <div
                    key={provider.id}
                    onClick={() => handleProviderChange(provider)}
                    className={`p-4 border rounded-lg cursor-pointer transition-all ${
                      selectedProvider?.id === provider.id
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                    }`}
                  >
                    <div className="flex items-center justify-between">
                      <div>
                        <h3 className="font-medium text-gray-900">{provider.name}</h3>
                        <p className="text-sm text-gray-500">{provider.provider}</p>
                        <p className="text-xs text-gray-400">{provider.model}</p>
                      </div>
                      {provider.isDefault && (
                        <span className="px-2 py-1 text-xs bg-green-100 text-green-800 rounded-full">
                          Default
                        </span>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Prompt Display */}
          <div className="bg-white rounded-lg shadow-sm border p-6 mb-6">
            <h2 className="text-lg font-semibold mb-4 text-gray-900">Your Prompt</h2>
            <div className="bg-gray-50 rounded-lg p-4">
              <p className="text-gray-700">{prompt}</p>
            </div>
          </div>

          {/* Generation Status */}
          {isGenerating && (
            <div className="bg-white rounded-lg shadow-sm border p-6 mb-6">
              <div className="flex items-center">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500 mr-3"></div>
                <p className="text-gray-700">
                  Generating with {selectedProvider?.name}... This may take a moment.
                </p>
              </div>
            </div>
          )}

          {/* Error Display */}
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-6 mb-6">
              <div className="flex items-center">
                <svg className="w-5 h-5 text-red-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
                <div className="text-red-700">{error}</div>
              </div>
            </div>
          )}

          {/* Generated Content */}
          {messages.length > 0 && (
            <div className="bg-white rounded-lg shadow-sm border p-6">
              <h2 className="text-lg font-semibold mb-4 text-gray-900">Generated Response</h2>
              <div className="space-y-4">
                {messages.map((message, index) => (
                  <div key={index} className="border-l-4 border-blue-500 pl-4">
                    {message.type === "text" && (
                      <div className="prose max-w-none">
                        <pre className="whitespace-pre-wrap text-sm text-gray-700 bg-gray-50 p-4 rounded">
                          {message.content}
                        </pre>
                      </div>
                    )}
                    {message.type === "error" && (
                      <div className="text-red-600">
                        Error: {message.message}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Actions */}
          <div className="mt-8 flex justify-center space-x-4">
            <button
              onClick={() => router.push("/")}
              className="px-6 py-2 text-gray-700 bg-gray-200 rounded-lg hover:bg-gray-300 transition-colors"
            >
              Back to Home
            </button>
            {selectedProvider && !isGenerating && (
              <button
                onClick={generateCode}
                className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                Regenerate
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

export default function GenerateAIPageWithSuspense() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <GenerateAIPage />
    </Suspense>
  );
}
