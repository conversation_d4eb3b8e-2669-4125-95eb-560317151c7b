(()=>{var e={};e.id=367,e.ids=[367],e.modules={7849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},220:(e,t,o)=>{"use strict";o.r(t),o.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,originalPathname:()=>p,pages:()=>c,routeModule:()=>m,tree:()=>d}),o(1627),o(9058),o(5866);var r=o(3191),s=o(8716),n=o(7922),i=o.n(n),a=o(5231),l={};for(let e in a)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>a[e]);o.d(t,l);let d=["",{children:["connect4",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(o.bind(o,1627)),"C:\\Users\\<USER>\\Documents\\github\\lovable-clone\\lovable-ui\\app\\connect4\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(o.bind(o,9058)),"C:\\Users\\<USER>\\Documents\\github\\lovable-clone\\lovable-ui\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(o.t.bind(o,5866,23)),"next/dist/client/components/not-found-error"]}],c=["C:\\Users\\<USER>\\Documents\\github\\lovable-clone\\lovable-ui\\app\\connect4\\page.tsx"],p="/connect4/page",u={require:o,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:s.x.APP_PAGE,page:"/connect4/page",pathname:"/connect4",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},7978:(e,t,o)=>{Promise.resolve().then(o.bind(o,625))},5130:(e,t,o)=>{Promise.resolve().then(o.bind(o,6719))},2688:(e,t,o)=>{Promise.resolve().then(o.t.bind(o,2994,23)),Promise.resolve().then(o.t.bind(o,6114,23)),Promise.resolve().then(o.t.bind(o,9727,23)),Promise.resolve().then(o.t.bind(o,9671,23)),Promise.resolve().then(o.t.bind(o,1868,23)),Promise.resolve().then(o.t.bind(o,4759,23))},625:(e,t,o)=>{"use strict";o.r(t),o.d(t,{default:()=>n});var r=o(326),s=o(7577);function n(){let[e,t]=(0,s.useState)(()=>Array(6).fill(null).map(()=>Array(7).fill(0))),[o,n]=(0,s.useState)(1),[i,a]=(0,s.useState)(null),[l,d]=(0,s.useState)(!1),c=(0,s.useCallback)((e,t,o,r)=>{let s=0;for(let o=0;o<7;o++)if(e[t][o]===r){if(4==++s)return!0}else s=0;s=0;for(let t=0;t<6;t++)if(e[t][o]===r){if(4==++s)return!0}else s=0;let n=Math.max(0,t-o),i=Math.max(0,o-t);s=0;for(let t=0;t<Math.min(6-n,7-i);t++)if(e[n+t][i+t]===r){if(4==++s)return!0}else s=0;let a=Math.max(0,t-(6-o)),l=Math.min(6,o+t);s=0;for(let t=0;t<Math.min(6-a,l+1);t++)if(e[a+t][l-t]===r){if(4==++s)return!0}else s=0;return!1},[]),p=(0,s.useCallback)(e=>e[0].every(e=>0!==e),[]),u=(0,s.useCallback)(r=>{if(i||l||0!==e[0][r])return;let s=e.map(e=>[...e]),u=5;for(;u>=0&&0!==s[u][r];)u--;u<0||(s[u][r]=o,t(s),c(s,u,r,o)?a(o):p(s)?d(!0):n(1===o?2:1))},[e,o,i,l,c,p]),m=(0,s.useCallback)(()=>{t(Array(6).fill(null).map(()=>Array(7).fill(0))),n(1),a(null),d(!1)},[]);return(0,r.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-gray-900 to-gray-800 flex flex-col items-center justify-center p-4",children:[r.jsx("h1",{className:"text-5xl font-bold text-white mb-8",children:"Connect 4"}),r.jsx("div",{className:"mb-6 text-center",children:i?(0,r.jsxs)("p",{className:"text-2xl font-semibold text-white",children:["Player ",i," (",1===i?"Red":"Yellow",") wins!"]}):l?r.jsx("p",{className:"text-2xl font-semibold text-white",children:"It's a draw!"}):(0,r.jsxs)("p",{className:"text-xl text-white",children:["Current player: ",1===o?"Red":"Yellow"]})}),r.jsx("div",{className:"bg-blue-600 p-4 rounded-xl shadow-2xl",children:r.jsx("div",{className:"grid grid-cols-7 gap-2",children:e.map((e,t)=>e.map((e,o)=>r.jsx("button",{onClick:()=>u(o),className:"w-16 h-16 bg-blue-800 rounded-full relative overflow-hidden hover:bg-blue-700 transition-colors",disabled:null!==i||l,children:r.jsx("div",{className:`absolute inset-2 rounded-full transition-all duration-300 ${1===e?"bg-red-500 shadow-inner":2===e?"bg-yellow-400 shadow-inner":"bg-blue-900"}`})},`${t}-${o}`)))})}),r.jsx("button",{onClick:m,className:"mt-8 px-6 py-3 bg-green-600 text-white font-semibold rounded-lg hover:bg-green-700 transition-colors shadow-lg",children:"New Game"}),r.jsx("a",{href:"/",className:"mt-4 text-blue-300 hover:text-blue-400 transition-colors",children:"← Back to Lovable UI"})]})}},6719:(e,t,o)=>{"use strict";o.d(t,{AIConfigProvider:()=>c,m:()=>p});var r=o(326),s=o(7577),n=o(729);function i(e,t){switch(t.type){case"SET_CONFIG":return{...e,config:t.payload,error:null};case"ADD_PROVIDER":return{...e,config:{...e.config,providers:[...e.config.providers,t.payload]}};case"UPDATE_PROVIDER":return{...e,config:{...e.config,providers:e.config.providers.map(e=>e.id===t.payload.id?{...e,...t.payload.config}:e)}};case"REMOVE_PROVIDER":let o=e.config.providers.filter(e=>e.id!==t.payload);return{...e,config:{...e.config,providers:o,defaultProvider:e.config.defaultProvider===t.payload?o[0]?.id:e.config.defaultProvider}};case"SET_DEFAULT_PROVIDER":return{...e,config:{...e.config,defaultProvider:t.payload}};case"SET_DEFAULT_MODEL":return{...e,config:{...e.config,defaultModel:t.payload}};case"UPDATE_DEFAULT_PARAMETERS":return{...e,config:{...e.config,defaultParameters:{...e.config.defaultParameters,...t.payload}}};case"TOGGLE_PROVIDER":return{...e,config:{...e.config,providers:e.config.providers.map(e=>e.id===t.payload?{...e,enabled:!e.enabled}:e)}};case"RESET_CONFIG":return{...e,config:n.T,error:null};default:return e}}let a={config:n.T,isLoading:!1,error:null},l=(0,s.createContext)(void 0),d="ai-config";function c({children:e}){let[t,o]=(0,s.useReducer)(i,a),n=async()=>{try{localStorage.setItem(d,JSON.stringify(t.config))}catch(e){console.error("Failed to save AI config:",e)}},c=async()=>{try{let e=localStorage.getItem(d);if(e){let t=JSON.parse(e);o({type:"SET_CONFIG",payload:t})}}catch(e){console.error("Failed to load AI config:",e)}},p=async e=>{let o=t.config.providers.find(t=>t.id===e);if(!o)return{success:!1,error:"Provider not found"};try{let e=await fetch("/api/ai/test-connection",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({provider:o})});return await e.json()}catch(e){return{success:!1,error:e instanceof Error?e.message:"Connection test failed"}}};return r.jsx(l.Provider,{value:{state:t,actions:{setConfig:e=>o({type:"SET_CONFIG",payload:e}),addProvider:e=>o({type:"ADD_PROVIDER",payload:e}),updateProvider:(e,t)=>o({type:"UPDATE_PROVIDER",payload:{id:e,config:t}}),removeProvider:e=>o({type:"REMOVE_PROVIDER",payload:e}),setDefaultProvider:e=>o({type:"SET_DEFAULT_PROVIDER",payload:e}),setDefaultModel:e=>o({type:"SET_DEFAULT_MODEL",payload:e}),updateDefaultParameters:e=>o({type:"UPDATE_DEFAULT_PARAMETERS",payload:e}),toggleProvider:e=>o({type:"TOGGLE_PROVIDER",payload:e}),resetConfig:()=>o({type:"RESET_CONFIG"}),saveConfig:n,loadConfig:c,testConnection:p,validateProvider:e=>{let t=[];return e.name.trim()||t.push("Provider name is required"),e.apiKey.trim()||t.push("API key is required"),"custom"!==e.provider||(e.baseURL?.trim()||t.push("Base URL is required for custom providers"),e.modelName?.trim()||t.push("Model name is required for custom providers")),{isValid:0===t.length,errors:t}}}},children:e})}function p(){let e=(0,s.useContext)(l);if(void 0===e)throw Error("useAIConfig must be used within an AIConfigProvider");return e}},729:(e,t,o)=>{"use strict";o.d(t,{T:()=>s,k:()=>r});let r={openai:[{id:"gpt-4o",name:"GPT-4o",provider:"openai",maxTokens:4096,supportsStreaming:!0,supportsTools:!0,supportsVision:!0,contextWindow:128e3},{id:"gpt-4o-mini",name:"GPT-4o Mini",provider:"openai",maxTokens:16384,supportsStreaming:!0,supportsTools:!0,supportsVision:!0,contextWindow:128e3},{id:"gpt-4-turbo",name:"GPT-4 Turbo",provider:"openai",maxTokens:4096,supportsStreaming:!0,supportsTools:!0,supportsVision:!0,contextWindow:128e3},{id:"gpt-3.5-turbo",name:"GPT-3.5 Turbo",provider:"openai",maxTokens:4096,supportsStreaming:!0,supportsTools:!0,supportsVision:!1,contextWindow:16385}],anthropic:[{id:"claude-3-5-sonnet-20241022",name:"Claude 3.5 Sonnet",provider:"anthropic",maxTokens:8192,supportsStreaming:!0,supportsTools:!0,supportsVision:!0,contextWindow:2e5},{id:"claude-3-5-haiku-20241022",name:"Claude 3.5 Haiku",provider:"anthropic",maxTokens:8192,supportsStreaming:!0,supportsTools:!0,supportsVision:!0,contextWindow:2e5},{id:"claude-3-opus-20240229",name:"Claude 3 Opus",provider:"anthropic",maxTokens:4096,supportsStreaming:!0,supportsTools:!0,supportsVision:!0,contextWindow:2e5}],google:[{id:"gemini-1.5-pro",name:"Gemini 1.5 Pro",provider:"google",maxTokens:8192,supportsStreaming:!0,supportsTools:!0,supportsVision:!0,contextWindow:2e6},{id:"gemini-1.5-flash",name:"Gemini 1.5 Flash",provider:"google",maxTokens:8192,supportsStreaming:!0,supportsTools:!0,supportsVision:!0,contextWindow:1e6}],cohere:[{id:"command-r-plus",name:"Command R+",provider:"cohere",maxTokens:4096,supportsStreaming:!0,supportsTools:!0,supportsVision:!1,contextWindow:128e3},{id:"command-r",name:"Command R",provider:"cohere",maxTokens:4096,supportsStreaming:!0,supportsTools:!0,supportsVision:!1,contextWindow:128e3}],mistral:[{id:"mistral-large-latest",name:"Mistral Large",provider:"mistral",maxTokens:8192,supportsStreaming:!0,supportsTools:!0,supportsVision:!1,contextWindow:128e3},{id:"mistral-medium-latest",name:"Mistral Medium",provider:"mistral",maxTokens:8192,supportsStreaming:!0,supportsTools:!0,supportsVision:!1,contextWindow:32e3}],custom:[]},s={providers:[],defaultParameters:{temperature:.7,maxTokens:4096,topP:1,frequencyPenalty:0,presencePenalty:0}}},1627:(e,t,o)=>{"use strict";o.r(t),o.d(t,{$$typeof:()=>i,__esModule:()=>n,default:()=>a});var r=o(8570);let s=(0,r.createProxy)(String.raw`C:\Users\<USER>\Documents\github\lovable-clone\lovable-ui\app\connect4\page.tsx`),{__esModule:n,$$typeof:i}=s;s.default;let a=(0,r.createProxy)(String.raw`C:\Users\<USER>\Documents\github\lovable-clone\lovable-ui\app\connect4\page.tsx#default`)},9058:(e,t,o)=>{"use strict";o.r(t),o.d(t,{default:()=>u,metadata:()=>p});var r=o(9510),s=o(7366),n=o.n(s);o(7272);var i=o(8570);let a=(0,i.createProxy)(String.raw`C:\Users\<USER>\Documents\github\lovable-clone\lovable-ui\lib\contexts\ai-config-context.tsx`),{__esModule:l,$$typeof:d}=a;a.default;let c=(0,i.createProxy)(String.raw`C:\Users\<USER>\Documents\github\lovable-clone\lovable-ui\lib\contexts\ai-config-context.tsx#AIConfigProvider`);(0,i.createProxy)(String.raw`C:\Users\<USER>\Documents\github\lovable-clone\lovable-ui\lib\contexts\ai-config-context.tsx#useAIConfig`);let p={title:"Lovable Clone - AI-Powered Code Generation",description:"Build applications faster with AI-powered code generation"};function u({children:e}){return r.jsx("html",{lang:"en",children:r.jsx("body",{className:n().className,children:r.jsx(c,{children:e})})})}},7272:()=>{}};var t=require("../../webpack-runtime.js");t.C(e);var o=e=>t(t.s=e),r=t.X(0,[948,82],()=>o(220));module.exports=r})();