"use client";

import { useEffect, useState } from "react";

interface Message {
  type: string;
  content?: string;
  name?: string;
  input?: any;
  result?: any;
}

interface MessageDisplayProps {
  messages: Message[];
}

export default function MessageDisplay({ messages }: MessageDisplayProps) {
  const [generatedPages, setGeneratedPages] = useState<string[]>([]);

  useEffect(() => {
    const pages = messages
      .filter((msg) => msg.type === "tool_use" && msg.name === "Write")
      .map((msg) => msg.input?.file_path)
      .filter(Boolean) as string[];
    
    setGeneratedPages([...new Set(pages)]);
  }, [messages]);

  if (messages.length === 0) return null;

  return (
    <div className="space-y-4">
      {generatedPages.length > 0 && (
        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <h3 className="text-green-800 font-semibold mb-2">Generated Files:</h3>
          <ul className="list-disc list-inside text-green-700">
            {generatedPages.map((page, index) => (
              <li key={index}>{page}</li>
            ))}
          </ul>
        </div>
      )}
      
      <div className="bg-gray-50 border rounded-lg p-4 max-h-96 overflow-y-auto">
        <h3 className="font-semibold mb-2">Generation Log:</h3>
        <div className="space-y-2 text-sm">
          {messages.map((message, index) => (
            <div key={index} className="border-b pb-2">
              <span className="font-medium text-blue-600">{message.type}</span>
              {message.name && (
                <span className="ml-2 text-gray-600">({message.name})</span>
              )}
              {message.content && (
                <div className="mt-1 text-gray-700">{message.content}</div>
              )}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
