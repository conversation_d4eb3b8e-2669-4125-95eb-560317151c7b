import { query as opencoder<PERSON><PERSON>y, initializeOpenCoderAdapter, type SDKMessage } from "./opencoder-adapter";
import { AI<PERSON>rovider } from "./ai-config";

export interface CodeGenerationResult {
  success: boolean;
  messages: SDKMessage[];
  error?: string;
}

// Initialize the OpenCoder adapter if API keys are available
if (typeof process !== 'undefined') {
  if (process.env.ANTHROPIC_API_KEY) {
    initializeOpenCoderAdapter(process.env.ANTHROPIC_API_KEY, AIProvider.ANTHROPIC);
  } else if (process.env.OPENAI_API_KEY) {
    initializeOpenCoderAdapter(process.env.OPENAI_API_KEY, AIProvider.OPENAI);
  } else if (process.env.GOOGLE_AI_API_KEY) {
    initializeOpenCoderAdapter(process.env.GOOGLE_AI_API_KEY, AIProvider.GOOGLE);
  } else if (process.env.COHERE_API_KEY) {
    initializeOpenCoderAdapter(process.env.COHERE_API_KEY, AIProvider.COHERE);
  }
}

export async function generateCodeWithClaude(prompt: string): Promise<CodeGenerationResult> {
  try {
    const messages: SDKMessage[] = [];
    const abortController = new AbortController();

    // Execute the query and collect all messages
    for await (const message of opencoderQuery({
      prompt: prompt,
      abortController: abortController,
      options: {
        maxTurns: 10, // Allow multiple turns for complex builds
        // Grant all necessary permissions for code generation
        allowedTools: [
          "Read",
          "Write",
          "Edit",
          "MultiEdit",
          "Bash",
          "LS",
          "Glob",
          "Grep",
          "WebSearch",
          "WebFetch"
        ]
      }
    })) {
      messages.push(message);
    }

    return {
      success: true,
      messages: messages
    };

  } catch (error: any) {
    console.error("Error generating code:", error);
    return {
      success: false,
      messages: [],
      error: error.message
    };
  }
}