/**
 * AI Configuration Manager
 * 
 * This is the main entry point for the AI configuration system.
 * It provides a unified interface for managing AI providers and configurations.
 */

export * from './types';
export * from './storage';
export * from './client';

import { 
  ProviderConfig, 
  AIProvider, 
  DEFAULT_CONFIGS,
  ProviderConfigSchema 
} from './types';
import {
  saveProviderConfig,
  getProviderConfigs,
  getProviderConfigById,
  deleteProviderConfig,
  getDefaultProviderConfig,
  setDefaultProvider,
  initializeDefaultProviders,
} from './storage';
import {
  generateAIText,
  streamAIText,
  testProviderConnection,
  validateProviderConfig,
  getAvailableModels,
} from './client';

/**
 * AI Configuration Manager Class
 * 
 * This class provides a high-level interface for managing AI configurations.
 */
export class AIConfigManager {
  private static instance: AIConfigManager;

  private constructor() {
    // Initialize default providers if none exist
    if (typeof window !== 'undefined') {
      initializeDefaultProviders();
    }
  }

  /**
   * Get the singleton instance
   */
  public static getInstance(): AIConfigManager {
    if (!AIConfigManager.instance) {
      AIConfigManager.instance = new AIConfigManager();
    }
    return AIConfigManager.instance;
  }

  /**
   * Get all provider configurations
   */
  public getProviders(): ProviderConfig[] {
    return getProviderConfigs();
  }

  /**
   * Get a provider by ID
   */
  public getProvider(id: string): ProviderConfig | null {
    return getProviderConfigById(id);
  }

  /**
   * Get the default provider
   */
  public getDefaultProvider(): ProviderConfig | null {
    return getDefaultProviderConfig();
  }

  /**
   * Save a provider configuration
   */
  public async saveProvider(config: Partial<ProviderConfig>): Promise<{
    success: boolean;
    provider?: ProviderConfig;
    errors?: string[];
  }> {
    try {
      // Validate the configuration
      const validationResult = ProviderConfigSchema.safeParse(config);
      
      if (!validationResult.success) {
        return {
          success: false,
          errors: validationResult.error.errors.map(e => e.message),
        };
      }

      const validatedConfig = validationResult.data;
      
      // Additional validation
      const { isValid, errors } = validateProviderConfig(validatedConfig);
      if (!isValid) {
        return {
          success: false,
          errors,
        };
      }

      // Save the configuration
      const savedProvider = saveProviderConfig(validatedConfig);
      
      return {
        success: true,
        provider: savedProvider,
      };
    } catch (error) {
      return {
        success: false,
        errors: [error.message || 'Failed to save provider configuration'],
      };
    }
  }

  /**
   * Delete a provider configuration
   */
  public deleteProvider(id: string): boolean {
    return deleteProviderConfig(id);
  }

  /**
   * Set a provider as default
   */
  public setDefault(id: string): boolean {
    return setDefaultProvider(id);
  }

  /**
   * Test a provider connection
   */
  public async testProvider(config: ProviderConfig): Promise<{
    success: boolean;
    error?: string;
  }> {
    return testProviderConnection(config);
  }

  /**
   * Generate text using the default provider
   */
  public async generateText(prompt: string): Promise<string> {
    const defaultProvider = this.getDefaultProvider();
    if (!defaultProvider) {
      throw new Error('No default provider configured');
    }

    const messages = [
      {
        role: 'user' as const,
        content: prompt,
      },
    ];

    return generateAIText(defaultProvider, messages);
  }

  /**
   * Generate text using a specific provider
   */
  public async generateTextWithProvider(providerId: string, prompt: string): Promise<string> {
    const provider = this.getProvider(providerId);
    if (!provider) {
      throw new Error(`Provider with ID ${providerId} not found`);
    }

    const messages = [
      {
        role: 'user' as const,
        content: prompt,
      },
    ];

    return generateAIText(provider, messages);
  }

  /**
   * Stream text using the default provider
   */
  public async streamText(prompt: string) {
    const defaultProvider = this.getDefaultProvider();
    if (!defaultProvider) {
      throw new Error('No default provider configured');
    }

    const messages = [
      {
        role: 'user' as const,
        content: prompt,
      },
    ];

    return streamAIText(defaultProvider, messages);
  }

  /**
   * Stream text using a specific provider
   */
  public async streamTextWithProvider(providerId: string, prompt: string) {
    const provider = this.getProvider(providerId);
    if (!provider) {
      throw new Error(`Provider with ID ${providerId} not found`);
    }

    const messages = [
      {
        role: 'user' as const,
        content: prompt,
      },
    ];

    return streamAIText(provider, messages);
  }

  /**
   * Get available models for a provider
   */
  public async getModels(provider: AIProvider, apiKey: string): Promise<string[]> {
    return getAvailableModels(provider, apiKey);
  }

  /**
   * Get default configuration for a provider
   */
  public getDefaultConfig(provider: AIProvider): Partial<ProviderConfig> {
    return DEFAULT_CONFIGS[provider];
  }

  /**
   * Import configurations from JSON
   */
  public importConfigurations(configs: ProviderConfig[]): {
    success: boolean;
    imported: number;
    errors: string[];
  } {
    const errors: string[] = [];
    let imported = 0;

    for (const config of configs) {
      try {
        const result = ProviderConfigSchema.safeParse(config);
        if (result.success) {
          saveProviderConfig(result.data);
          imported++;
        } else {
          errors.push(`Invalid configuration for ${config.name}: ${result.error.message}`);
        }
      } catch (error) {
        errors.push(`Failed to import ${config.name}: ${error.message}`);
      }
    }

    return {
      success: errors.length === 0,
      imported,
      errors,
    };
  }

  /**
   * Export configurations to JSON
   */
  public exportConfigurations(): ProviderConfig[] {
    return this.getProviders();
  }
}

/**
 * Get the global AI configuration manager instance
 */
export const aiConfigManager = AIConfigManager.getInstance();
