(()=>{var e={};e.id=366,e.ids=[366],e.modules={7849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},218:(e,o,r)=>{"use strict";r.r(o),r.d(o,{GlobalError:()=>n.a,__next_app__:()=>c,originalPathname:()=>u,pages:()=>d,routeModule:()=>m,tree:()=>p}),r(8759),r(9058),r(5866);var t=r(3191),s=r(8716),i=r(7922),n=r.n(i),a=r(5231),l={};for(let e in a)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>a[e]);r.d(o,l);let p=["",{children:["hello-world",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,8759)),"C:\\Users\\<USER>\\Documents\\github\\lovable-clone\\lovable-ui\\app\\hello-world\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,9058)),"C:\\Users\\<USER>\\Documents\\github\\lovable-clone\\lovable-ui\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,5866,23)),"next/dist/client/components/not-found-error"]}],d=["C:\\Users\\<USER>\\Documents\\github\\lovable-clone\\lovable-ui\\app\\hello-world\\page.tsx"],u="/hello-world/page",c={require:r,loadChunk:()=>Promise.resolve()},m=new t.AppPageRouteModule({definition:{kind:s.x.APP_PAGE,page:"/hello-world/page",pathname:"/hello-world",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:p}})},5130:(e,o,r)=>{Promise.resolve().then(r.bind(r,6719))},2688:(e,o,r)=>{Promise.resolve().then(r.t.bind(r,2994,23)),Promise.resolve().then(r.t.bind(r,6114,23)),Promise.resolve().then(r.t.bind(r,9727,23)),Promise.resolve().then(r.t.bind(r,9671,23)),Promise.resolve().then(r.t.bind(r,1868,23)),Promise.resolve().then(r.t.bind(r,4759,23))},5303:()=>{},6719:(e,o,r)=>{"use strict";r.d(o,{AIConfigProvider:()=>d,m:()=>u});var t=r(326),s=r(7577),i=r(729);function n(e,o){switch(o.type){case"SET_CONFIG":return{...e,config:o.payload,error:null};case"ADD_PROVIDER":return{...e,config:{...e.config,providers:[...e.config.providers,o.payload]}};case"UPDATE_PROVIDER":return{...e,config:{...e.config,providers:e.config.providers.map(e=>e.id===o.payload.id?{...e,...o.payload.config}:e)}};case"REMOVE_PROVIDER":let r=e.config.providers.filter(e=>e.id!==o.payload);return{...e,config:{...e.config,providers:r,defaultProvider:e.config.defaultProvider===o.payload?r[0]?.id:e.config.defaultProvider}};case"SET_DEFAULT_PROVIDER":return{...e,config:{...e.config,defaultProvider:o.payload}};case"SET_DEFAULT_MODEL":return{...e,config:{...e.config,defaultModel:o.payload}};case"UPDATE_DEFAULT_PARAMETERS":return{...e,config:{...e.config,defaultParameters:{...e.config.defaultParameters,...o.payload}}};case"TOGGLE_PROVIDER":return{...e,config:{...e.config,providers:e.config.providers.map(e=>e.id===o.payload?{...e,enabled:!e.enabled}:e)}};case"RESET_CONFIG":return{...e,config:i.T,error:null};default:return e}}let a={config:i.T,isLoading:!1,error:null},l=(0,s.createContext)(void 0),p="ai-config";function d({children:e}){let[o,r]=(0,s.useReducer)(n,a),i=async()=>{try{localStorage.setItem(p,JSON.stringify(o.config))}catch(e){console.error("Failed to save AI config:",e)}},d=async()=>{try{let e=localStorage.getItem(p);if(e){let o=JSON.parse(e);r({type:"SET_CONFIG",payload:o})}}catch(e){console.error("Failed to load AI config:",e)}},u=async e=>{let r=o.config.providers.find(o=>o.id===e);if(!r)return{success:!1,error:"Provider not found"};try{let e=await fetch("/api/ai/test-connection",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({provider:r})});return await e.json()}catch(e){return{success:!1,error:e instanceof Error?e.message:"Connection test failed"}}};return t.jsx(l.Provider,{value:{state:o,actions:{setConfig:e=>r({type:"SET_CONFIG",payload:e}),addProvider:e=>r({type:"ADD_PROVIDER",payload:e}),updateProvider:(e,o)=>r({type:"UPDATE_PROVIDER",payload:{id:e,config:o}}),removeProvider:e=>r({type:"REMOVE_PROVIDER",payload:e}),setDefaultProvider:e=>r({type:"SET_DEFAULT_PROVIDER",payload:e}),setDefaultModel:e=>r({type:"SET_DEFAULT_MODEL",payload:e}),updateDefaultParameters:e=>r({type:"UPDATE_DEFAULT_PARAMETERS",payload:e}),toggleProvider:e=>r({type:"TOGGLE_PROVIDER",payload:e}),resetConfig:()=>r({type:"RESET_CONFIG"}),saveConfig:i,loadConfig:d,testConnection:u,validateProvider:e=>{let o=[];return e.name.trim()||o.push("Provider name is required"),e.apiKey.trim()||o.push("API key is required"),"custom"!==e.provider||(e.baseURL?.trim()||o.push("Base URL is required for custom providers"),e.modelName?.trim()||o.push("Model name is required for custom providers")),{isValid:0===o.length,errors:o}}}},children:e})}function u(){let e=(0,s.useContext)(l);if(void 0===e)throw Error("useAIConfig must be used within an AIConfigProvider");return e}},729:(e,o,r)=>{"use strict";r.d(o,{T:()=>s,k:()=>t});let t={openai:[{id:"gpt-4o",name:"GPT-4o",provider:"openai",maxTokens:4096,supportsStreaming:!0,supportsTools:!0,supportsVision:!0,contextWindow:128e3},{id:"gpt-4o-mini",name:"GPT-4o Mini",provider:"openai",maxTokens:16384,supportsStreaming:!0,supportsTools:!0,supportsVision:!0,contextWindow:128e3},{id:"gpt-4-turbo",name:"GPT-4 Turbo",provider:"openai",maxTokens:4096,supportsStreaming:!0,supportsTools:!0,supportsVision:!0,contextWindow:128e3},{id:"gpt-3.5-turbo",name:"GPT-3.5 Turbo",provider:"openai",maxTokens:4096,supportsStreaming:!0,supportsTools:!0,supportsVision:!1,contextWindow:16385}],anthropic:[{id:"claude-3-5-sonnet-20241022",name:"Claude 3.5 Sonnet",provider:"anthropic",maxTokens:8192,supportsStreaming:!0,supportsTools:!0,supportsVision:!0,contextWindow:2e5},{id:"claude-3-5-haiku-20241022",name:"Claude 3.5 Haiku",provider:"anthropic",maxTokens:8192,supportsStreaming:!0,supportsTools:!0,supportsVision:!0,contextWindow:2e5},{id:"claude-3-opus-20240229",name:"Claude 3 Opus",provider:"anthropic",maxTokens:4096,supportsStreaming:!0,supportsTools:!0,supportsVision:!0,contextWindow:2e5}],google:[{id:"gemini-1.5-pro",name:"Gemini 1.5 Pro",provider:"google",maxTokens:8192,supportsStreaming:!0,supportsTools:!0,supportsVision:!0,contextWindow:2e6},{id:"gemini-1.5-flash",name:"Gemini 1.5 Flash",provider:"google",maxTokens:8192,supportsStreaming:!0,supportsTools:!0,supportsVision:!0,contextWindow:1e6}],cohere:[{id:"command-r-plus",name:"Command R+",provider:"cohere",maxTokens:4096,supportsStreaming:!0,supportsTools:!0,supportsVision:!1,contextWindow:128e3},{id:"command-r",name:"Command R",provider:"cohere",maxTokens:4096,supportsStreaming:!0,supportsTools:!0,supportsVision:!1,contextWindow:128e3}],mistral:[{id:"mistral-large-latest",name:"Mistral Large",provider:"mistral",maxTokens:8192,supportsStreaming:!0,supportsTools:!0,supportsVision:!1,contextWindow:128e3},{id:"mistral-medium-latest",name:"Mistral Medium",provider:"mistral",maxTokens:8192,supportsStreaming:!0,supportsTools:!0,supportsVision:!1,contextWindow:32e3}],custom:[]},s={providers:[],defaultParameters:{temperature:.7,maxTokens:4096,topP:1,frequencyPenalty:0,presencePenalty:0}}},8759:(e,o,r)=>{"use strict";r.r(o),r.d(o,{default:()=>s});var t=r(9510);function s(){return t.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-purple-400 via-pink-500 to-red-500",children:t.jsx("h1",{className:"text-6xl font-bold text-white drop-shadow-lg",children:"Hello World"})})}},9058:(e,o,r)=>{"use strict";r.r(o),r.d(o,{default:()=>c,metadata:()=>u});var t=r(9510),s=r(7366),i=r.n(s);r(7272);var n=r(8570);let a=(0,n.createProxy)(String.raw`C:\Users\<USER>\Documents\github\lovable-clone\lovable-ui\lib\contexts\ai-config-context.tsx`),{__esModule:l,$$typeof:p}=a;a.default;let d=(0,n.createProxy)(String.raw`C:\Users\<USER>\Documents\github\lovable-clone\lovable-ui\lib\contexts\ai-config-context.tsx#AIConfigProvider`);(0,n.createProxy)(String.raw`C:\Users\<USER>\Documents\github\lovable-clone\lovable-ui\lib\contexts\ai-config-context.tsx#useAIConfig`);let u={title:"Lovable Clone - AI-Powered Code Generation",description:"Build applications faster with AI-powered code generation"};function c({children:e}){return t.jsx("html",{lang:"en",children:t.jsx("body",{className:i().className,children:t.jsx(d,{children:e})})})}},7272:()=>{}};var o=require("../../webpack-runtime.js");o.C(e);var r=e=>o(o.s=e),t=o.X(0,[948,82],()=>r(218));module.exports=t})();