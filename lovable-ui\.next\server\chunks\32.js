"use strict";exports.id=32,exports.ids=[32],exports.modules={9303:(e,t,n)=>{e.exports=n(517)},5762:e=>{let t="undefined"!=typeof Buffer,n=/"(?:_|\\u005[Ff])(?:_|\\u005[Ff])(?:p|\\u0070)(?:r|\\u0072)(?:o|\\u006[Ff])(?:t|\\u0074)(?:o|\\u006[Ff])(?:_|\\u005[Ff])(?:_|\\u005[Ff])"\s*:/,s=/"(?:c|\\u0063)(?:o|\\u006[Ff])(?:n|\\u006[Ee])(?:s|\\u0073)(?:t|\\u0074)(?:r|\\u0072)(?:u|\\u0075)(?:c|\\u0063)(?:t|\\u0074)(?:o|\\u006[Ff])(?:r|\\u0072)"\s*:/;function a(e,a,i){null==i&&null!==a&&"object"==typeof a&&(i=a,a=void 0),t&&Buffer.isBuffer(e)&&(e=e.toString()),e&&65279===e.charCodeAt(0)&&(e=e.slice(1));let o=JSON.parse(e,a);if(null===o||"object"!=typeof o)return o;let l=i&&i.protoAction||"error",u=i&&i.constructorAction||"error";if("ignore"===l&&"ignore"===u)return o;if("ignore"!==l&&"ignore"!==u){if(!1===n.test(e)&&!1===s.test(e))return o}else if("ignore"!==l&&"ignore"===u){if(!1===n.test(e))return o}else if(!1===s.test(e))return o;return r(o,{protoAction:l,constructorAction:u,safe:i&&i.safe})}function r(e,{protoAction:t="error",constructorAction:n="error",safe:s}={}){let a=[e];for(;a.length;){let e=a;for(let r of(a=[],e)){if("ignore"!==t&&Object.prototype.hasOwnProperty.call(r,"__proto__")){if(!0===s)return null;if("error"===t)throw SyntaxError("Object contains forbidden prototype property");delete r.__proto__}if("ignore"!==n&&Object.prototype.hasOwnProperty.call(r,"constructor")&&Object.prototype.hasOwnProperty.call(r.constructor,"prototype")){if(!0===s)return null;if("error"===n)throw SyntaxError("Object contains forbidden prototype property");delete r.constructor}for(let e in r){let t=r[e];t&&"object"==typeof t&&a.push(t)}}}return e}function i(e,t,n){let s=Error.stackTraceLimit;Error.stackTraceLimit=0;try{return a(e,t,n)}finally{Error.stackTraceLimit=s}}e.exports=i,e.exports.default=i,e.exports.parse=i,e.exports.safeParse=function(e,t){let n=Error.stackTraceLimit;Error.stackTraceLimit=0;try{return a(e,t,{safe:!0})}catch(e){return null}finally{Error.stackTraceLimit=n}},e.exports.scan=r},9225:(e,t,n)=>{n.d(t,{D:()=>b});var s=n(1760),a=n(3888),r=n(1067),i=r.Ry({type:r.i0("error"),error:r.Ry({type:r.Z_(),message:r.Z_()})}),o=(0,a.Sq)({errorSchema:i,errorToMessage:e=>e.error.message});function l(e){switch(e){case"end_turn":case"stop_sequence":return"stop";case"tool_use":return"tool-calls";case"max_tokens":return"length";default:return"unknown"}}var u=class{constructor(e,t,n){this.specificationVersion="v1",this.defaultObjectGenerationMode="tool",this.modelId=e,this.settings=t,this.config=n}supportsUrl(e){return"https:"===e.protocol}get provider(){return this.config.provider}get supportsImageUrls(){return this.config.supportsImageUrls}async getArgs({mode:e,prompt:t,maxTokens:n=4096,temperature:r,topP:i,topK:o,frequencyPenalty:l,presencePenalty:u,stopSequences:d,responseFormat:c,seed:h,providerMetadata:m}){var f,g,y;let _=e.type,v=[];null!=l&&v.push({type:"unsupported-setting",setting:"frequencyPenalty"}),null!=u&&v.push({type:"unsupported-setting",setting:"presencePenalty"}),null!=h&&v.push({type:"unsupported-setting",setting:"seed"}),null!=c&&"text"!==c.type&&v.push({type:"unsupported-setting",setting:"responseFormat",details:"JSON response format is not supported."});let{prompt:b,betas:x}=function({prompt:e,sendReasoning:t,warnings:n}){var r,i,o,l;let u;let d=new Set,c=function(e){let t;let n=[];for(let s of e){let{role:e}=s;switch(e){case"system":(null==t?void 0:t.type)!=="system"&&(t={type:"system",messages:[]},n.push(t)),t.messages.push(s);break;case"assistant":(null==t?void 0:t.type)!=="assistant"&&(t={type:"assistant",messages:[]},n.push(t)),t.messages.push(s);break;case"user":case"tool":(null==t?void 0:t.type)!=="user"&&(t={type:"user",messages:[]},n.push(t)),t.messages.push(s);break;default:throw Error(`Unsupported role: ${e}`)}}return n}(e),p=[];function h(e){var t;let n=null==e?void 0:e.anthropic;return null!=(t=null==n?void 0:n.cacheControl)?t:null==n?void 0:n.cache_control}for(let e=0;e<c.length;e++){let m=c[e],f=e===c.length-1,g=m.type;switch(g){case"system":if(null!=u)throw new s.A_({functionality:"Multiple system messages that are separated by user/assistant messages"});u=m.messages.map(({content:e,providerMetadata:t})=>({type:"text",text:e,cache_control:h(t)}));break;case"user":{let e=[];for(let t of m.messages){let{role:n,content:l}=t;switch(n){case"user":for(let n=0;n<l.length;n++){let o=l[n],u=n===l.length-1,c=null!=(r=h(o.providerMetadata))?r:u?h(t.providerMetadata):void 0;switch(o.type){case"text":e.push({type:"text",text:o.text,cache_control:c});break;case"image":e.push({type:"image",source:o.image instanceof URL?{type:"url",url:o.image.toString()}:{type:"base64",media_type:null!=(i=o.mimeType)?i:"image/jpeg",data:(0,a.k0)(o.image)},cache_control:c});break;case"file":if("application/pdf"!==o.mimeType)throw new s.A_({functionality:"Non-PDF files in user messages"});d.add("pdfs-2024-09-25"),e.push({type:"document",source:o.data instanceof URL?{type:"url",url:o.data.toString()}:{type:"base64",media_type:"application/pdf",data:o.data},cache_control:c})}}break;case"tool":for(let n=0;n<l.length;n++){let s=l[n],a=n===l.length-1,r=null!=(o=h(s.providerMetadata))?o:a?h(t.providerMetadata):void 0,i=null!=s.content?s.content.map(e=>{var t;switch(e.type){case"text":return{type:"text",text:e.text,cache_control:void 0};case"image":return{type:"image",source:{type:"base64",media_type:null!=(t=e.mimeType)?t:"image/jpeg",data:e.data},cache_control:void 0}}}):JSON.stringify(s.result);e.push({type:"tool_result",tool_use_id:s.toolCallId,content:i,is_error:s.isError,cache_control:r})}break;default:throw Error(`Unsupported role: ${n}`)}}p.push({role:"user",content:e});break}case"assistant":{let e=[];for(let s=0;s<m.messages.length;s++){let a=m.messages[s],r=s===m.messages.length-1,{content:i}=a;for(let s=0;s<i.length;s++){let o=i[s],u=s===i.length-1,d=null!=(l=h(o.providerMetadata))?l:u?h(a.providerMetadata):void 0;switch(o.type){case"text":e.push({type:"text",text:f&&r&&u?o.text.trim():o.text,cache_control:d});break;case"reasoning":t?e.push({type:"thinking",thinking:o.text,signature:o.signature,cache_control:d}):n.push({type:"other",message:"sending reasoning content is disabled for this model"});break;case"redacted-reasoning":e.push({type:"redacted_thinking",data:o.data,cache_control:d});break;case"tool-call":e.push({type:"tool_use",id:o.toolCallId,name:o.toolName,input:o.args,cache_control:d})}}}p.push({role:"assistant",content:e});break}default:throw Error(`Unsupported type: ${g}`)}}return{prompt:{system:u,messages:p},betas:d}}({prompt:t,sendReasoning:null==(f=this.settings.sendReasoning)||f,warnings:v}),k=(0,a.c1)({provider:"anthropic",providerOptions:m,schema:p}),w=(null==(g=null==k?void 0:k.thinking)?void 0:g.type)==="enabled",R=null==(y=null==k?void 0:k.thinking)?void 0:y.budgetTokens,T={model:this.modelId,max_tokens:n,temperature:r,top_k:o,top_p:i,stop_sequences:d,...w&&{thinking:{type:"enabled",budget_tokens:R}},system:b.system,messages:b.messages};if(w){if(null==R)throw new s.A_({functionality:"thinking requires a budget"});null!=T.temperature&&(T.temperature=void 0,v.push({type:"unsupported-setting",setting:"temperature",details:"temperature is not supported when thinking is enabled"})),null!=o&&(T.top_k=void 0,v.push({type:"unsupported-setting",setting:"topK",details:"topK is not supported when thinking is enabled"})),null!=i&&(T.top_p=void 0,v.push({type:"unsupported-setting",setting:"topP",details:"topP is not supported when thinking is enabled"})),T.max_tokens=n+R}switch(_){case"regular":{let{tools:t,tool_choice:n,toolWarnings:a,betas:r}=function(e){var t;let n=(null==(t=e.tools)?void 0:t.length)?e.tools:void 0,a=[],r=new Set;if(null==n)return{tools:void 0,tool_choice:void 0,toolWarnings:a,betas:r};let i=[];for(let e of n)switch(e.type){case"function":i.push({name:e.name,description:e.description,input_schema:e.parameters});break;case"provider-defined":switch(e.id){case"anthropic.computer_20250124":r.add("computer-use-2025-01-24"),i.push({name:e.name,type:"computer_20250124",display_width_px:e.args.displayWidthPx,display_height_px:e.args.displayHeightPx,display_number:e.args.displayNumber});break;case"anthropic.computer_20241022":r.add("computer-use-2024-10-22"),i.push({name:e.name,type:"computer_20241022",display_width_px:e.args.displayWidthPx,display_height_px:e.args.displayHeightPx,display_number:e.args.displayNumber});break;case"anthropic.text_editor_20250124":r.add("computer-use-2025-01-24"),i.push({name:e.name,type:"text_editor_20250124"});break;case"anthropic.text_editor_20241022":r.add("computer-use-2024-10-22"),i.push({name:e.name,type:"text_editor_20241022"});break;case"anthropic.bash_20250124":r.add("computer-use-2025-01-24"),i.push({name:e.name,type:"bash_20250124"});break;case"anthropic.bash_20241022":r.add("computer-use-2024-10-22"),i.push({name:e.name,type:"bash_20241022"});break;default:a.push({type:"unsupported-tool",tool:e})}break;default:a.push({type:"unsupported-tool",tool:e})}let o=e.toolChoice;if(null==o)return{tools:i,tool_choice:void 0,toolWarnings:a,betas:r};let l=o.type;switch(l){case"auto":return{tools:i,tool_choice:{type:"auto"},toolWarnings:a,betas:r};case"required":return{tools:i,tool_choice:{type:"any"},toolWarnings:a,betas:r};case"none":return{tools:void 0,tool_choice:void 0,toolWarnings:a,betas:r};case"tool":return{tools:i,tool_choice:{type:"tool",name:o.toolName},toolWarnings:a,betas:r};default:throw new s.A_({functionality:`Unsupported tool choice type: ${l}`})}}(e);return{args:{...T,tools:t,tool_choice:n},warnings:[...v,...a],betas:new Set([...x,...r])}}case"object-json":throw new s.A_({functionality:"json-mode object generation"});case"object-tool":{let{name:t,description:n,parameters:s}=e.tool;return{args:{...T,tools:[{name:t,description:n,input_schema:s}],tool_choice:{type:"tool",name:t}},warnings:v,betas:x}}default:throw Error(`Unsupported type: ${_}`)}}async getHeaders({betas:e,headers:t}){return(0,a.NF)(await (0,a.DB)(this.config.headers),e.size>0?{"anthropic-beta":Array.from(e).join(",")}:{},t)}buildRequestUrl(e){var t,n,s;return null!=(s=null==(n=(t=this.config).buildRequestUrl)?void 0:n.call(t,this.config.baseURL,e))?s:`${this.config.baseURL}/messages`}transformRequestBody(e){var t,n,s;return null!=(s=null==(n=(t=this.config).transformRequestBody)?void 0:n.call(t,e))?s:e}async doGenerate(e){var t,n,s,r;let i;let{args:u,warnings:c,betas:p}=await this.getArgs(e),{responseHeaders:h,value:m,rawValue:f}=await (0,a.A8)({url:this.buildRequestUrl(!1),headers:await this.getHeaders({betas:p,headers:e.headers}),body:this.transformRequestBody(u),failedResponseHandler:o,successfulResponseHandler:(0,a.tc)(d),abortSignal:e.abortSignal,fetch:this.config.fetch}),{messages:g,...y}=u,_="";for(let e of m.content)"text"===e.type&&(_+=e.text);if(m.content.some(e=>"tool_use"===e.type))for(let e of(i=[],m.content))"tool_use"===e.type&&i.push({toolCallType:"function",toolCallId:e.id,toolName:e.name,args:JSON.stringify(e.input)});let v=m.content.filter(e=>"redacted_thinking"===e.type||"thinking"===e.type).map(e=>"thinking"===e.type?{type:"text",text:e.thinking,signature:e.signature}:{type:"redacted",data:e.data});return{text:_,reasoning:v.length>0?v:void 0,toolCalls:i,finishReason:l(m.stop_reason),usage:{promptTokens:m.usage.input_tokens,completionTokens:m.usage.output_tokens},rawCall:{rawPrompt:g,rawSettings:y},rawResponse:{headers:h,body:f},response:{id:null!=(t=m.id)?t:void 0,modelId:null!=(n=m.model)?n:void 0},warnings:c,providerMetadata:{anthropic:{cacheCreationInputTokens:null!=(s=m.usage.cache_creation_input_tokens)?s:null,cacheReadInputTokens:null!=(r=m.usage.cache_read_input_tokens)?r:null}},request:{body:JSON.stringify(u)}}}async doStream(e){let t,n;let{args:s,warnings:r,betas:i}=await this.getArgs(e),u={...s,stream:!0},{responseHeaders:d,value:p}=await (0,a.A8)({url:this.buildRequestUrl(!0),headers:await this.getHeaders({betas:i,headers:e.headers}),body:this.transformRequestBody(u),failedResponseHandler:o,successfulResponseHandler:(0,a.cP)(c),abortSignal:e.abortSignal,fetch:this.config.fetch}),{messages:h,...m}=s,f="unknown",g={promptTokens:Number.NaN,completionTokens:Number.NaN},y={};return{stream:p.pipeThrough(new TransformStream({transform(e,s){var a,r,i,o;if(!e.success){s.enqueue({type:"error",error:e.error});return}let u=e.value;switch(u.type){case"ping":return;case"content_block_start":{let e=u.content_block.type;switch(n=e,e){case"text":case"thinking":return;case"redacted_thinking":s.enqueue({type:"redacted-reasoning",data:u.content_block.data});return;case"tool_use":y[u.index]={toolCallId:u.content_block.id,toolName:u.content_block.name,jsonText:""};return;default:throw Error(`Unsupported content block type: ${e}`)}}case"content_block_stop":if(null!=y[u.index]){let e=y[u.index];s.enqueue({type:"tool-call",toolCallType:"function",toolCallId:e.toolCallId,toolName:e.toolName,args:e.jsonText}),delete y[u.index]}n=void 0;return;case"content_block_delta":{let e=u.delta.type;switch(e){case"text_delta":s.enqueue({type:"text-delta",textDelta:u.delta.text});return;case"thinking_delta":s.enqueue({type:"reasoning",textDelta:u.delta.thinking});return;case"signature_delta":"thinking"===n&&s.enqueue({type:"reasoning-signature",signature:u.delta.signature});return;case"input_json_delta":{let e=y[u.index];s.enqueue({type:"tool-call-delta",toolCallType:"function",toolCallId:e.toolCallId,toolName:e.toolName,argsTextDelta:u.delta.partial_json}),e.jsonText+=u.delta.partial_json;return}default:throw Error(`Unsupported delta type: ${e}`)}}case"message_start":g.promptTokens=u.message.usage.input_tokens,g.completionTokens=u.message.usage.output_tokens,t={anthropic:{cacheCreationInputTokens:null!=(a=u.message.usage.cache_creation_input_tokens)?a:null,cacheReadInputTokens:null!=(r=u.message.usage.cache_read_input_tokens)?r:null}},s.enqueue({type:"response-metadata",id:null!=(i=u.message.id)?i:void 0,modelId:null!=(o=u.message.model)?o:void 0});return;case"message_delta":g.completionTokens=u.usage.output_tokens,f=l(u.delta.stop_reason);return;case"message_stop":s.enqueue({type:"finish",finishReason:f,usage:g,providerMetadata:t});return;case"error":s.enqueue({type:"error",error:u.error});return;default:throw Error(`Unsupported chunk type: ${u}`)}}})),rawCall:{rawPrompt:h,rawSettings:m},rawResponse:{headers:d},warnings:r,request:{body:JSON.stringify(u)}}}},d=r.Ry({type:r.i0("message"),id:r.Z_().nullish(),model:r.Z_().nullish(),content:r.IX(r.VK("type",[r.Ry({type:r.i0("text"),text:r.Z_()}),r.Ry({type:r.i0("thinking"),thinking:r.Z_(),signature:r.Z_()}),r.Ry({type:r.i0("redacted_thinking"),data:r.Z_()}),r.Ry({type:r.i0("tool_use"),id:r.Z_(),name:r.Z_(),input:r._4()})])),stop_reason:r.Z_().nullish(),usage:r.Ry({input_tokens:r.Rx(),output_tokens:r.Rx(),cache_creation_input_tokens:r.Rx().nullish(),cache_read_input_tokens:r.Rx().nullish()})}),c=r.VK("type",[r.Ry({type:r.i0("message_start"),message:r.Ry({id:r.Z_().nullish(),model:r.Z_().nullish(),usage:r.Ry({input_tokens:r.Rx(),output_tokens:r.Rx(),cache_creation_input_tokens:r.Rx().nullish(),cache_read_input_tokens:r.Rx().nullish()})})}),r.Ry({type:r.i0("content_block_start"),index:r.Rx(),content_block:r.VK("type",[r.Ry({type:r.i0("text"),text:r.Z_()}),r.Ry({type:r.i0("thinking"),thinking:r.Z_()}),r.Ry({type:r.i0("tool_use"),id:r.Z_(),name:r.Z_()}),r.Ry({type:r.i0("redacted_thinking"),data:r.Z_()})])}),r.Ry({type:r.i0("content_block_delta"),index:r.Rx(),delta:r.VK("type",[r.Ry({type:r.i0("input_json_delta"),partial_json:r.Z_()}),r.Ry({type:r.i0("text_delta"),text:r.Z_()}),r.Ry({type:r.i0("thinking_delta"),thinking:r.Z_()}),r.Ry({type:r.i0("signature_delta"),signature:r.Z_()})])}),r.Ry({type:r.i0("content_block_stop"),index:r.Rx()}),r.Ry({type:r.i0("error"),error:r.Ry({type:r.Z_(),message:r.Z_()})}),r.Ry({type:r.i0("message_delta"),delta:r.Ry({stop_reason:r.Z_().nullish()}),usage:r.Ry({output_tokens:r.Rx()})}),r.Ry({type:r.i0("message_stop")}),r.Ry({type:r.i0("ping")})]),p=r.Ry({thinking:r.Ry({type:r.G0([r.i0("enabled"),r.i0("disabled")]),budgetTokens:r.Rx().optional()}).optional()}),h=r.Ry({command:r.Z_(),restart:r.O7().optional()}),m=r.Ry({command:r.Z_(),restart:r.O7().optional()}),f=r.Ry({command:r.Km(["view","create","str_replace","insert","undo_edit"]),path:r.Z_(),file_text:r.Z_().optional(),insert_line:r.Rx().int().optional(),new_str:r.Z_().optional(),old_str:r.Z_().optional(),view_range:r.IX(r.Rx().int()).optional()}),g=r.Ry({command:r.Km(["view","create","str_replace","insert","undo_edit"]),path:r.Z_(),file_text:r.Z_().optional(),insert_line:r.Rx().int().optional(),new_str:r.Z_().optional(),old_str:r.Z_().optional(),view_range:r.IX(r.Rx().int()).optional()}),y=r.Ry({action:r.Km(["key","type","mouse_move","left_click","left_click_drag","right_click","middle_click","double_click","screenshot","cursor_position"]),coordinate:r.IX(r.Rx().int()).optional(),text:r.Z_().optional()}),_=r.Ry({action:r.Km(["key","hold_key","type","cursor_position","mouse_move","left_mouse_down","left_mouse_up","left_click","left_click_drag","right_click","middle_click","double_click","triple_click","scroll","wait","screenshot"]),coordinate:r.bc([r.Rx().int(),r.Rx().int()]).optional(),duration:r.Rx().optional(),scroll_amount:r.Rx().optional(),scroll_direction:r.Km(["up","down","left","right"]).optional(),start_coordinate:r.bc([r.Rx().int(),r.Rx().int()]).optional(),text:r.Z_().optional()}),v={bash_20241022:function(e={}){return{type:"provider-defined",id:"anthropic.bash_20241022",args:{},parameters:h,execute:e.execute,experimental_toToolResultContent:e.experimental_toToolResultContent}},bash_20250124:function(e={}){return{type:"provider-defined",id:"anthropic.bash_20250124",args:{},parameters:m,execute:e.execute,experimental_toToolResultContent:e.experimental_toToolResultContent}},textEditor_20241022:function(e={}){return{type:"provider-defined",id:"anthropic.text_editor_20241022",args:{},parameters:f,execute:e.execute,experimental_toToolResultContent:e.experimental_toToolResultContent}},textEditor_20250124:function(e={}){return{type:"provider-defined",id:"anthropic.text_editor_20250124",args:{},parameters:g,execute:e.execute,experimental_toToolResultContent:e.experimental_toToolResultContent}},computer_20241022:function(e){return{type:"provider-defined",id:"anthropic.computer_20241022",args:{displayWidthPx:e.displayWidthPx,displayHeightPx:e.displayHeightPx,displayNumber:e.displayNumber},parameters:y,execute:e.execute,experimental_toToolResultContent:e.experimental_toToolResultContent}},computer_20250124:function(e){return{type:"provider-defined",id:"anthropic.computer_20250124",args:{displayWidthPx:e.displayWidthPx,displayHeightPx:e.displayHeightPx,displayNumber:e.displayNumber},parameters:_,execute:e.execute,experimental_toToolResultContent:e.experimental_toToolResultContent}}};function b(e={}){var t;let n=null!=(t=(0,a.QT)(e.baseURL))?t:"https://api.anthropic.com/v1",r=()=>({"anthropic-version":"2023-06-01","x-api-key":(0,a.pd)({apiKey:e.apiKey,environmentVariableName:"ANTHROPIC_API_KEY",description:"Anthropic"}),...e.headers}),i=(t,s={})=>new u(t,s,{provider:"anthropic.messages",baseURL:n,headers:r,fetch:e.fetch,supportsImageUrls:!0}),o=function(e,t){if(new.target)throw Error("The Anthropic model function cannot be called with the new keyword.");return i(e,t)};return o.languageModel=i,o.chat=i,o.messages=i,o.textEmbeddingModel=e=>{throw new s.JJ({modelId:e,modelType:"textEmbeddingModel"})},o.tools=v,o}b()},4343:(e,t,n)=>{n.d(t,{L:()=>C});var s=n(3888),a=n(1067),r=n(1760);function i(e){if(null!=e&&"object"==typeof e&&"object"===e.type&&(null==e.properties||0===Object.keys(e.properties).length)&&!e.additionalProperties)return;if("boolean"==typeof e)return{type:"boolean",properties:{}};let{type:t,description:n,required:s,properties:a,items:r,allOf:o,anyOf:l,oneOf:u,format:d,const:c,minLength:p,enum:h}=e,m={};if(n&&(m.description=n),s&&(m.required=s),d&&(m.format=d),void 0!==c&&(m.enum=[c]),t&&(Array.isArray(t)?t.includes("null")?(m.type=t.filter(e=>"null"!==e)[0],m.nullable=!0):m.type=t:"null"===t?m.type="null":m.type=t),void 0!==h&&(m.enum=h),null!=a&&(m.properties=Object.entries(a).reduce((e,[t,n])=>(e[t]=i(n),e),{})),r&&(m.items=Array.isArray(r)?r.map(i):i(r)),o&&(m.allOf=o.map(i)),l){if(l.some(e=>"object"==typeof e&&(null==e?void 0:e.type)==="null")){let e=l.filter(e=>!("object"==typeof e&&(null==e?void 0:e.type)==="null"));if(1===e.length){let t=i(e[0]);"object"==typeof t&&(m.nullable=!0,Object.assign(m,t))}else m.anyOf=e.map(i),m.nullable=!0}else m.anyOf=l.map(i)}return u&&(m.oneOf=u.map(i)),void 0!==p&&(m.minLength=p),m}function o(e){return e.includes("/")?e:`models/${e}`}var l=a.Ry({error:a.Ry({code:a.Rx().nullable(),message:a.Z_(),status:a.Z_()})}),u=(0,s.Sq)({errorSchema:l,errorToMessage:e=>e.error.message});function d({finishReason:e,hasToolCalls:t}){switch(e){case"STOP":return t?"tool-calls":"stop";case"MAX_TOKENS":return"length";case"IMAGE_SAFETY":case"RECITATION":case"SAFETY":case"BLOCKLIST":case"PROHIBITED_CONTENT":case"SPII":return"content-filter";case"FINISH_REASON_UNSPECIFIED":case"OTHER":return"other";case"MALFORMED_FUNCTION_CALL":return"error";default:return"unknown"}}var c=class{constructor(e,t,n){this.specificationVersion="v1",this.defaultObjectGenerationMode="json",this.supportsImageUrls=!1,this.modelId=e,this.settings=t,this.config=n}get supportsStructuredOutputs(){var e;return null==(e=this.settings.structuredOutputs)||e}get provider(){return this.config.provider}async getArgs({mode:e,prompt:t,maxTokens:n,temperature:a,topP:o,topK:l,frequencyPenalty:u,presencePenalty:d,stopSequences:c,responseFormat:p,seed:h,providerMetadata:m}){var f,g,y;let _=e.type,v=[],b=(0,s.c1)({provider:"google",providerOptions:m,schema:w});(null==(f=null==b?void 0:b.thinkingConfig)?void 0:f.includeThoughts)!==!0||this.config.provider.startsWith("google.vertex.")||v.push({type:"other",message:`The 'includeThoughts' option is only supported with the Google Vertex provider and might not be supported or could behave unexpectedly with the current Google provider (${this.config.provider}).`});let x={maxOutputTokens:n,temperature:a,topK:l,topP:o,frequencyPenalty:u,presencePenalty:d,stopSequences:c,seed:h,responseMimeType:(null==p?void 0:p.type)==="json"?"application/json":void 0,responseSchema:(null==p?void 0:p.type)==="json"&&null!=p.schema&&this.supportsStructuredOutputs?i(p.schema):void 0,...this.settings.audioTimestamp&&{audioTimestamp:this.settings.audioTimestamp},responseModalities:null==b?void 0:b.responseModalities,thinkingConfig:null==b?void 0:b.thinkingConfig},{contents:k,systemInstruction:R}=function(e){var t,n;let a=[],i=[],o=!0;for(let{role:l,content:u}of e)switch(l){case"system":if(!o)throw new r.A_({functionality:"system messages are only supported at the beginning of the conversation"});a.push({text:u});break;case"user":{o=!1;let e=[];for(let a of u)switch(a.type){case"text":e.push({text:a.text});break;case"image":e.push(a.image instanceof URL?{fileData:{mimeType:null!=(t=a.mimeType)?t:"image/jpeg",fileUri:a.image.toString()}}:{inlineData:{mimeType:null!=(n=a.mimeType)?n:"image/jpeg",data:(0,s.k0)(a.image)}});break;case"file":e.push(a.data instanceof URL?{fileData:{mimeType:a.mimeType,fileUri:a.data.toString()}}:{inlineData:{mimeType:a.mimeType,data:a.data}})}i.push({role:"user",parts:e});break}case"assistant":o=!1,i.push({role:"model",parts:u.map(e=>{switch(e.type){case"text":return 0===e.text.length?void 0:{text:e.text};case"file":if("image/png"!==e.mimeType)throw new r.A_({functionality:"Only PNG images are supported in assistant messages"});if(e.data instanceof URL)throw new r.A_({functionality:"File data URLs in assistant messages are not supported"});return{inlineData:{mimeType:e.mimeType,data:e.data}};case"tool-call":return{functionCall:{name:e.toolName,args:e.args}}}}).filter(e=>void 0!==e)});break;case"tool":o=!1,i.push({role:"user",parts:u.map(e=>({functionResponse:{name:e.toolName,response:{name:e.toolName,content:e.result}}}))})}return{systemInstruction:a.length>0?{parts:a}:void 0,contents:i}}(t);switch(_){case"regular":{let{tools:t,toolConfig:n,toolWarnings:s}=function(e,t,n,s){var a,o;let l=(null==(a=e.tools)?void 0:a.length)?e.tools:void 0,u=[],d=s.includes("gemini-2"),c=s.includes("gemini-1.5-flash")&&!s.includes("-8b");if(t)return{tools:d?{googleSearch:{}}:{googleSearchRetrieval:c&&n?{dynamicRetrievalConfig:n}:{}},toolConfig:void 0,toolWarnings:u};if(null==l)return{tools:void 0,toolConfig:void 0,toolWarnings:u};let p=[];for(let e of l)"provider-defined"===e.type?u.push({type:"unsupported-tool",tool:e}):p.push({name:e.name,description:null!=(o=e.description)?o:"",parameters:i(e.parameters)});let h=e.toolChoice;if(null==h)return{tools:{functionDeclarations:p},toolConfig:void 0,toolWarnings:u};let m=h.type;switch(m){case"auto":return{tools:{functionDeclarations:p},toolConfig:{functionCallingConfig:{mode:"AUTO"}},toolWarnings:u};case"none":return{tools:{functionDeclarations:p},toolConfig:{functionCallingConfig:{mode:"NONE"}},toolWarnings:u};case"required":return{tools:{functionDeclarations:p},toolConfig:{functionCallingConfig:{mode:"ANY"}},toolWarnings:u};case"tool":return{tools:{functionDeclarations:p},toolConfig:{functionCallingConfig:{mode:"ANY",allowedFunctionNames:[h.toolName]}},toolWarnings:u};default:throw new r.A_({functionality:`Unsupported tool choice type: ${m}`})}}(e,null!=(g=this.settings.useSearchGrounding)&&g,this.settings.dynamicRetrievalConfig,this.modelId);return{args:{generationConfig:x,contents:k,systemInstruction:R,safetySettings:this.settings.safetySettings,tools:t,toolConfig:n,cachedContent:this.settings.cachedContent},warnings:[...v,...s]}}case"object-json":return{args:{generationConfig:{...x,responseMimeType:"application/json",responseSchema:null!=e.schema&&this.supportsStructuredOutputs?i(e.schema):void 0},contents:k,systemInstruction:R,safetySettings:this.settings.safetySettings,cachedContent:this.settings.cachedContent},warnings:v};case"object-tool":return{args:{generationConfig:x,contents:k,systemInstruction:R,tools:{functionDeclarations:[{name:e.tool.name,description:null!=(y=e.tool.description)?y:"",parameters:i(e.tool.parameters)}]},toolConfig:{functionCallingConfig:{mode:"ANY"}},safetySettings:this.settings.safetySettings,cachedContent:this.settings.cachedContent},warnings:v};default:throw Error(`Unsupported type: ${_}`)}}supportsUrl(e){return this.config.isSupportedUrl(e)}async doGenerate(e){var t,n,a,r,i;let{args:l,warnings:c}=await this.getArgs(e),y=JSON.stringify(l),_=(0,s.NF)(await (0,s.DB)(this.config.headers),e.headers),{responseHeaders:v,value:b,rawValue:k}=await (0,s.A8)({url:`${this.config.baseURL}/${o(this.modelId)}:generateContent`,headers:_,body:l,failedResponseHandler:u,successfulResponseHandler:(0,s.tc)(x),abortSignal:e.abortSignal,fetch:this.config.fetch}),{contents:w,...R}=l,T=b.candidates[0],I=null!=T.content&&"object"==typeof T.content&&"parts"in T.content?T.content.parts:[],C=p({parts:I,generateId:this.config.generateId}),Z=b.usageMetadata;return{text:h(I),reasoning:m(I),files:null==(t=f(I))?void 0:t.map(e=>({data:e.inlineData.data,mimeType:e.inlineData.mimeType})),toolCalls:C,finishReason:d({finishReason:T.finishReason,hasToolCalls:null!=C&&C.length>0}),usage:{promptTokens:null!=(n=null==Z?void 0:Z.promptTokenCount)?n:NaN,completionTokens:null!=(a=null==Z?void 0:Z.candidatesTokenCount)?a:NaN},rawCall:{rawPrompt:w,rawSettings:R},rawResponse:{headers:v,body:k},warnings:c,providerMetadata:{google:{groundingMetadata:null!=(r=T.groundingMetadata)?r:null,safetyRatings:null!=(i=T.safetyRatings)?i:null}},sources:g({groundingMetadata:T.groundingMetadata,generateId:this.config.generateId}),request:{body:y}}}async doStream(e){let t;let{args:n,warnings:a}=await this.getArgs(e),r=JSON.stringify(n),i=(0,s.NF)(await (0,s.DB)(this.config.headers),e.headers),{responseHeaders:l,value:c}=await (0,s.A8)({url:`${this.config.baseURL}/${o(this.modelId)}:streamGenerateContent?alt=sse`,headers:i,body:n,failedResponseHandler:u,successfulResponseHandler:(0,s.cP)(k),abortSignal:e.abortSignal,fetch:this.config.fetch}),{contents:y,..._}=n,v="unknown",b={promptTokens:Number.NaN,completionTokens:Number.NaN},x=this.config.generateId,w=!1;return{stream:c.pipeThrough(new TransformStream({transform(e,n){var s,a,r,i,o,l;if(!e.success){n.enqueue({type:"error",error:e.error});return}let u=e.value,c=u.usageMetadata;null!=c&&(b={promptTokens:null!=(s=c.promptTokenCount)?s:NaN,completionTokens:null!=(a=c.candidatesTokenCount)?a:NaN});let y=null==(r=u.candidates)?void 0:r[0];if(null==y)return;let _=y.content;if(null!=_){let e=h(_.parts);null!=e&&n.enqueue({type:"text-delta",textDelta:e});let t=m(_.parts);if(null!=t)for(let e of t)n.enqueue({type:"reasoning",textDelta:e.text});let s=f(_.parts);if(null!=s)for(let e of s)n.enqueue({type:"file",mimeType:e.inlineData.mimeType,data:e.inlineData.data});let a=p({parts:_.parts,generateId:x});if(null!=a)for(let e of a)n.enqueue({type:"tool-call-delta",toolCallType:"function",toolCallId:e.toolCallId,toolName:e.toolName,argsTextDelta:e.args}),n.enqueue({type:"tool-call",toolCallType:"function",toolCallId:e.toolCallId,toolName:e.toolName,args:e.args}),w=!0}if(null!=y.finishReason){for(let e of(v=d({finishReason:y.finishReason,hasToolCalls:w}),null!=(i=g({groundingMetadata:y.groundingMetadata,generateId:x}))?i:[]))n.enqueue({type:"source",source:e});t={google:{groundingMetadata:null!=(o=y.groundingMetadata)?o:null,safetyRatings:null!=(l=y.safetyRatings)?l:null}}}},flush(e){e.enqueue({type:"finish",finishReason:v,usage:b,providerMetadata:t})}})),rawCall:{rawPrompt:y,rawSettings:_},rawResponse:{headers:l},warnings:a,request:{body:r}}}};function p({parts:e,generateId:t}){let n=null==e?void 0:e.filter(e=>"functionCall"in e);return null==n||0===n.length?void 0:n.map(e=>({toolCallType:"function",toolCallId:t(),toolName:e.functionCall.name,args:JSON.stringify(e.functionCall.args)}))}function h(e){let t=null==e?void 0:e.filter(e=>"text"in e&&!0!==e.thought);return null==t||0===t.length?void 0:t.map(e=>e.text).join("")}function m(e){let t=null==e?void 0:e.filter(e=>"text"in e&&!0===e.thought&&null!=e.text);return null==t||0===t.length?void 0:t.map(e=>({type:"text",text:e.text}))}function f(e){return null==e?void 0:e.filter(e=>"inlineData"in e)}function g({groundingMetadata:e,generateId:t}){var n;return null==(n=null==e?void 0:e.groundingChunks)?void 0:n.filter(e=>null!=e.web).map(e=>({sourceType:"url",id:t(),url:e.web.uri,title:e.web.title}))}var y=a.Ry({parts:a.IX(a.G0([a.Ry({functionCall:a.Ry({name:a.Z_(),args:a._4()})}),a.Ry({inlineData:a.Ry({mimeType:a.Z_(),data:a.Z_()})}),a.Ry({text:a.Z_().nullish(),thought:a.O7().nullish()})])).nullish()}),_=a.Ry({web:a.Ry({uri:a.Z_(),title:a.Z_()}).nullish(),retrievedContext:a.Ry({uri:a.Z_(),title:a.Z_()}).nullish()}),v=a.Ry({webSearchQueries:a.IX(a.Z_()).nullish(),retrievalQueries:a.IX(a.Z_()).nullish(),searchEntryPoint:a.Ry({renderedContent:a.Z_()}).nullish(),groundingChunks:a.IX(_).nullish(),groundingSupports:a.IX(a.Ry({segment:a.Ry({startIndex:a.Rx().nullish(),endIndex:a.Rx().nullish(),text:a.Z_().nullish()}),segment_text:a.Z_().nullish(),groundingChunkIndices:a.IX(a.Rx()).nullish(),supportChunkIndices:a.IX(a.Rx()).nullish(),confidenceScores:a.IX(a.Rx()).nullish(),confidenceScore:a.IX(a.Rx()).nullish()})).nullish(),retrievalMetadata:a.G0([a.Ry({webDynamicRetrievalScore:a.Rx()}),a.Ry({})]).nullish()}),b=a.Ry({category:a.Z_().nullish(),probability:a.Z_().nullish(),probabilityScore:a.Rx().nullish(),severity:a.Z_().nullish(),severityScore:a.Rx().nullish(),blocked:a.O7().nullish()}),x=a.Ry({candidates:a.IX(a.Ry({content:y.nullish().or(a.Ry({}).strict()),finishReason:a.Z_().nullish(),safetyRatings:a.IX(b).nullish(),groundingMetadata:v.nullish()})),usageMetadata:a.Ry({promptTokenCount:a.Rx().nullish(),candidatesTokenCount:a.Rx().nullish(),totalTokenCount:a.Rx().nullish()}).nullish()}),k=a.Ry({candidates:a.IX(a.Ry({content:y.nullish(),finishReason:a.Z_().nullish(),safetyRatings:a.IX(b).nullish(),groundingMetadata:v.nullish()})).nullish(),usageMetadata:a.Ry({promptTokenCount:a.Rx().nullish(),candidatesTokenCount:a.Rx().nullish(),totalTokenCount:a.Rx().nullish()}).nullish()}),w=a.Ry({responseModalities:a.IX(a.Km(["TEXT","IMAGE"])).nullish(),thinkingConfig:a.Ry({thinkingBudget:a.Rx().nullish(),includeThoughts:a.O7().nullish()}).nullish()}),R=class{constructor(e,t,n){this.specificationVersion="v1",this.modelId=e,this.settings=t,this.config=n}get provider(){return this.config.provider}get maxEmbeddingsPerCall(){return 2048}get supportsParallelCalls(){return!0}async doEmbed({values:e,headers:t,abortSignal:n}){if(e.length>this.maxEmbeddingsPerCall)throw new r.ON({provider:this.provider,modelId:this.modelId,maxEmbeddingsPerCall:this.maxEmbeddingsPerCall,values:e});let a=(0,s.NF)(await (0,s.DB)(this.config.headers),t),{responseHeaders:i,value:o}=await (0,s.A8)({url:`${this.config.baseURL}/models/${this.modelId}:batchEmbedContents`,headers:a,body:{requests:e.map(e=>({model:`models/${this.modelId}`,content:{role:"user",parts:[{text:e}]},outputDimensionality:this.settings.outputDimensionality,taskType:this.settings.taskType}))},failedResponseHandler:u,successfulResponseHandler:(0,s.tc)(T),abortSignal:n,fetch:this.config.fetch});return{embeddings:o.embeddings.map(e=>e.values),usage:void 0,rawResponse:{headers:i}}}},T=a.Ry({embeddings:a.IX(a.Ry({values:a.IX(a.Rx())}))});function I(e){return e.toString().startsWith("https://generativelanguage.googleapis.com/v1beta/files/")}function C(e={}){var t;let n=null!=(t=(0,s.QT)(e.baseURL))?t:"https://generativelanguage.googleapis.com/v1beta",a=()=>({"x-goog-api-key":(0,s.pd)({apiKey:e.apiKey,environmentVariableName:"GOOGLE_GENERATIVE_AI_API_KEY",description:"Google Generative AI"}),...e.headers}),r=(t,r={})=>{var i;return new c(t,r,{provider:"google.generative-ai",baseURL:n,headers:a,generateId:null!=(i=e.generateId)?i:s.Ox,isSupportedUrl:I,fetch:e.fetch})},i=(t,s={})=>new R(t,s,{provider:"google.generative-ai",baseURL:n,headers:a,fetch:e.fetch}),o=function(e,t){if(new.target)throw Error("The Google Generative AI model function cannot be called with the new keyword.");return r(e,t)};return o.languageModel=r,o.chat=r,o.generativeAI=r,o.embedding=i,o.textEmbedding=i,o.textEmbeddingModel=i,o}C()},7725:(e,t,n)=>{n.d(t,{J:()=>H});var s=n(3888),a=n(1760),r=n(1067);function i(e){var t,n;return null!=(n=null==(t=null==e?void 0:e.content)?void 0:t.map(({token:e,logprob:t,top_logprobs:n})=>({token:e,logprob:t,topLogprobs:n?n.map(({token:e,logprob:t})=>({token:e,logprob:t})):[]})))?n:void 0}function o(e){switch(e){case"stop":return"stop";case"length":return"length";case"content_filter":return"content-filter";case"function_call":case"tool_calls":return"tool-calls";default:return"unknown"}}var l=r.Ry({error:r.Ry({message:r.Z_(),type:r.Z_().nullish(),param:r.Yj().nullish(),code:r.G0([r.Z_(),r.Rx()]).nullish()})}),u=(0,s.Sq)({errorSchema:l,errorToMessage:e=>e.error.message});function d({id:e,model:t,created:n}){return{id:null!=e?e:void 0,modelId:null!=t?t:void 0,timestamp:null!=n?new Date(1e3*n):void 0}}var c=class{constructor(e,t,n){this.specificationVersion="v1",this.modelId=e,this.settings=t,this.config=n}get supportsStructuredOutputs(){var e;return null!=(e=this.settings.structuredOutputs)?e:f(this.modelId)}get defaultObjectGenerationMode(){return this.modelId.startsWith("gpt-4o-audio-preview")?"tool":this.supportsStructuredOutputs?"json":"tool"}get provider(){return this.config.provider}get supportsImageUrls(){return!this.settings.downloadImages}getArgs({mode:e,prompt:t,maxTokens:n,temperature:r,topP:i,topK:o,frequencyPenalty:l,presencePenalty:u,stopSequences:d,responseFormat:c,seed:p,providerMetadata:h}){var m,y,_,v,b,x,k,w,R,T,I;let C=e.type,Z=[];null!=o&&Z.push({type:"unsupported-setting",setting:"topK"}),(null==c?void 0:c.type)!=="json"||null==c.schema||this.supportsStructuredOutputs||Z.push({type:"unsupported-setting",setting:"responseFormat",details:"JSON response format schema is only supported with structuredOutputs"});let S=this.settings.useLegacyFunctionCalling;if(S&&!0===this.settings.parallelToolCalls)throw new a.A_({functionality:"useLegacyFunctionCalling with parallelToolCalls"});if(S&&this.supportsStructuredOutputs)throw new a.A_({functionality:"structuredOutputs with useLegacyFunctionCalling"});let{messages:N,warnings:O}=function({prompt:e,useLegacyFunctionCalling:t=!1,systemMessageMode:n="system"}){let r=[],i=[];for(let{role:o,content:l}of e)switch(o){case"system":switch(n){case"system":r.push({role:"system",content:l});break;case"developer":r.push({role:"developer",content:l});break;case"remove":i.push({type:"other",message:"system messages are removed for this model"});break;default:throw Error(`Unsupported system message mode: ${n}`)}break;case"user":if(1===l.length&&"text"===l[0].type){r.push({role:"user",content:l[0].text});break}r.push({role:"user",content:l.map((e,t)=>{var n,r,i,o;switch(e.type){case"text":return{type:"text",text:e.text};case"image":return{type:"image_url",image_url:{url:e.image instanceof URL?e.image.toString():`data:${null!=(n=e.mimeType)?n:"image/jpeg"};base64,${(0,s.k0)(e.image)}`,detail:null==(i=null==(r=e.providerMetadata)?void 0:r.openai)?void 0:i.imageDetail}};case"file":if(e.data instanceof URL)throw new a.A_({functionality:"'File content parts with URL data' functionality not supported."});switch(e.mimeType){case"audio/wav":return{type:"input_audio",input_audio:{data:e.data,format:"wav"}};case"audio/mp3":case"audio/mpeg":return{type:"input_audio",input_audio:{data:e.data,format:"mp3"}};case"application/pdf":return{type:"file",file:{filename:null!=(o=e.filename)?o:`part-${t}.pdf`,file_data:`data:application/pdf;base64,${e.data}`}};default:throw new a.A_({functionality:`File content part type ${e.mimeType} in user messages`})}}})});break;case"assistant":{let e="",n=[];for(let t of l)switch(t.type){case"text":e+=t.text;break;case"tool-call":n.push({id:t.toolCallId,type:"function",function:{name:t.toolName,arguments:JSON.stringify(t.args)}})}if(t){if(n.length>1)throw new a.A_({functionality:"useLegacyFunctionCalling with multiple tool calls in one message"});r.push({role:"assistant",content:e,function_call:n.length>0?n[0].function:void 0})}else r.push({role:"assistant",content:e,tool_calls:n.length>0?n:void 0});break}case"tool":for(let e of l)t?r.push({role:"function",name:e.toolName,content:JSON.stringify(e.result)}):r.push({role:"tool",tool_call_id:e.toolCallId,content:JSON.stringify(e.result)});break;default:throw Error(`Unsupported role: ${o}`)}return{messages:r,warnings:i}}({prompt:t,useLegacyFunctionCalling:S,systemMessageMode:f(R=this.modelId)?null!=(I=null==(T=g[R])?void 0:T.systemMessageMode)?I:"developer":"system"});Z.push(...O);let A={model:this.modelId,logit_bias:this.settings.logitBias,logprobs:!0===this.settings.logprobs||"number"==typeof this.settings.logprobs||void 0,top_logprobs:"number"==typeof this.settings.logprobs?this.settings.logprobs:"boolean"==typeof this.settings.logprobs&&this.settings.logprobs?0:void 0,user:this.settings.user,parallel_tool_calls:this.settings.parallelToolCalls,max_tokens:n,temperature:r,top_p:i,frequency_penalty:l,presence_penalty:u,response_format:(null==c?void 0:c.type)==="json"?this.supportsStructuredOutputs&&null!=c.schema?{type:"json_schema",json_schema:{schema:c.schema,strict:!0,name:null!=(m=c.name)?m:"response",description:c.description}}:{type:"json_object"}:void 0,stop:d,seed:p,max_completion_tokens:null==(y=null==h?void 0:h.openai)?void 0:y.maxCompletionTokens,store:null==(_=null==h?void 0:h.openai)?void 0:_.store,metadata:null==(v=null==h?void 0:h.openai)?void 0:v.metadata,prediction:null==(b=null==h?void 0:h.openai)?void 0:b.prediction,reasoning_effort:null!=(k=null==(x=null==h?void 0:h.openai)?void 0:x.reasoningEffort)?k:this.settings.reasoningEffort,messages:N};switch(f(this.modelId)?(null!=A.temperature&&(A.temperature=void 0,Z.push({type:"unsupported-setting",setting:"temperature",details:"temperature is not supported for reasoning models"})),null!=A.top_p&&(A.top_p=void 0,Z.push({type:"unsupported-setting",setting:"topP",details:"topP is not supported for reasoning models"})),null!=A.frequency_penalty&&(A.frequency_penalty=void 0,Z.push({type:"unsupported-setting",setting:"frequencyPenalty",details:"frequencyPenalty is not supported for reasoning models"})),null!=A.presence_penalty&&(A.presence_penalty=void 0,Z.push({type:"unsupported-setting",setting:"presencePenalty",details:"presencePenalty is not supported for reasoning models"})),null!=A.logit_bias&&(A.logit_bias=void 0,Z.push({type:"other",message:"logitBias is not supported for reasoning models"})),null!=A.logprobs&&(A.logprobs=void 0,Z.push({type:"other",message:"logprobs is not supported for reasoning models"})),null!=A.top_logprobs&&(A.top_logprobs=void 0,Z.push({type:"other",message:"topLogprobs is not supported for reasoning models"})),null!=A.max_tokens&&(null==A.max_completion_tokens&&(A.max_completion_tokens=A.max_tokens),A.max_tokens=void 0)):(this.modelId.startsWith("gpt-4o-search-preview")||this.modelId.startsWith("gpt-4o-mini-search-preview"))&&null!=A.temperature&&(A.temperature=void 0,Z.push({type:"unsupported-setting",setting:"temperature",details:"temperature is not supported for the search preview models and has been removed."})),C){case"regular":{let{tools:t,tool_choice:n,functions:s,function_call:r,toolWarnings:i}=function({mode:e,useLegacyFunctionCalling:t=!1,structuredOutputs:n}){var s;let r=(null==(s=e.tools)?void 0:s.length)?e.tools:void 0,i=[];if(null==r)return{tools:void 0,tool_choice:void 0,toolWarnings:i};let o=e.toolChoice;if(t){let e=[];for(let t of r)"provider-defined"===t.type?i.push({type:"unsupported-tool",tool:t}):e.push({name:t.name,description:t.description,parameters:t.parameters});if(null==o)return{functions:e,function_call:void 0,toolWarnings:i};switch(o.type){case"auto":case"none":case void 0:return{functions:e,function_call:void 0,toolWarnings:i};case"required":throw new a.A_({functionality:"useLegacyFunctionCalling and toolChoice: required"});default:return{functions:e,function_call:{name:o.toolName},toolWarnings:i}}}let l=[];for(let e of r)"provider-defined"===e.type?i.push({type:"unsupported-tool",tool:e}):l.push({type:"function",function:{name:e.name,description:e.description,parameters:e.parameters,strict:!!n||void 0}});if(null==o)return{tools:l,tool_choice:void 0,toolWarnings:i};let u=o.type;switch(u){case"auto":case"none":case"required":return{tools:l,tool_choice:u,toolWarnings:i};case"tool":return{tools:l,tool_choice:{type:"function",function:{name:o.toolName}},toolWarnings:i};default:throw new a.A_({functionality:`Unsupported tool choice type: ${u}`})}}({mode:e,useLegacyFunctionCalling:S,structuredOutputs:this.supportsStructuredOutputs});return{args:{...A,tools:t,tool_choice:n,functions:s,function_call:r},warnings:[...Z,...i]}}case"object-json":return{args:{...A,response_format:this.supportsStructuredOutputs&&null!=e.schema?{type:"json_schema",json_schema:{schema:e.schema,strict:!0,name:null!=(w=e.name)?w:"response",description:e.description}}:{type:"json_object"}},warnings:Z};case"object-tool":return{args:S?{...A,function_call:{name:e.tool.name},functions:[{name:e.tool.name,description:e.tool.description,parameters:e.tool.parameters}]}:{...A,tool_choice:{type:"function",function:{name:e.tool.name}},tools:[{type:"function",function:{name:e.tool.name,description:e.tool.description,parameters:e.tool.parameters,strict:!!this.supportsStructuredOutputs||void 0}}]},warnings:Z};default:throw Error(`Unsupported type: ${C}`)}}async doGenerate(e){var t,n,a,r,l,c,p,m;let{args:f,warnings:g}=this.getArgs(e),{responseHeaders:y,value:_,rawValue:v}=await (0,s.A8)({url:this.config.url({path:"/chat/completions",modelId:this.modelId}),headers:(0,s.NF)(this.config.headers(),e.headers),body:f,failedResponseHandler:u,successfulResponseHandler:(0,s.tc)(h),abortSignal:e.abortSignal,fetch:this.config.fetch}),{messages:b,...x}=f,k=_.choices[0],w=null==(t=_.usage)?void 0:t.completion_tokens_details,R=null==(n=_.usage)?void 0:n.prompt_tokens_details,T={openai:{}};return(null==w?void 0:w.reasoning_tokens)!=null&&(T.openai.reasoningTokens=null==w?void 0:w.reasoning_tokens),(null==w?void 0:w.accepted_prediction_tokens)!=null&&(T.openai.acceptedPredictionTokens=null==w?void 0:w.accepted_prediction_tokens),(null==w?void 0:w.rejected_prediction_tokens)!=null&&(T.openai.rejectedPredictionTokens=null==w?void 0:w.rejected_prediction_tokens),(null==R?void 0:R.cached_tokens)!=null&&(T.openai.cachedPromptTokens=null==R?void 0:R.cached_tokens),{text:null!=(a=k.message.content)?a:void 0,toolCalls:this.settings.useLegacyFunctionCalling&&k.message.function_call?[{toolCallType:"function",toolCallId:(0,s.Ox)(),toolName:k.message.function_call.name,args:k.message.function_call.arguments}]:null==(r=k.message.tool_calls)?void 0:r.map(e=>{var t;return{toolCallType:"function",toolCallId:null!=(t=e.id)?t:(0,s.Ox)(),toolName:e.function.name,args:e.function.arguments}}),finishReason:o(k.finish_reason),usage:{promptTokens:null!=(c=null==(l=_.usage)?void 0:l.prompt_tokens)?c:NaN,completionTokens:null!=(m=null==(p=_.usage)?void 0:p.completion_tokens)?m:NaN},rawCall:{rawPrompt:b,rawSettings:x},rawResponse:{headers:y,body:v},request:{body:JSON.stringify(f)},response:d(_),warnings:g,logprobs:i(k.logprobs),providerMetadata:T}}async doStream(e){let t;if(this.settings.simulateStreaming){let t=await this.doGenerate(e);return{stream:new ReadableStream({start(e){if(e.enqueue({type:"response-metadata",...t.response}),t.text&&e.enqueue({type:"text-delta",textDelta:t.text}),t.toolCalls)for(let n of t.toolCalls)e.enqueue({type:"tool-call-delta",toolCallType:"function",toolCallId:n.toolCallId,toolName:n.toolName,argsTextDelta:n.args}),e.enqueue({type:"tool-call",...n});e.enqueue({type:"finish",finishReason:t.finishReason,usage:t.usage,logprobs:t.logprobs,providerMetadata:t.providerMetadata}),e.close()}}),rawCall:t.rawCall,rawResponse:t.rawResponse,warnings:t.warnings}}let{args:n,warnings:r}=this.getArgs(e),l={...n,stream:!0,stream_options:"strict"===this.config.compatibility?{include_usage:!0}:void 0},{responseHeaders:c,value:p}=await (0,s.A8)({url:this.config.url({path:"/chat/completions",modelId:this.modelId}),headers:(0,s.NF)(this.config.headers(),e.headers),body:l,failedResponseHandler:u,successfulResponseHandler:(0,s.cP)(m),abortSignal:e.abortSignal,fetch:this.config.fetch}),{messages:h,...f}=n,g=[],y="unknown",_={promptTokens:void 0,completionTokens:void 0},v=!0,{useLegacyFunctionCalling:b}=this.settings,x={openai:{}};return{stream:p.pipeThrough(new TransformStream({transform(e,n){var r,l,u,c,p,h,m,f,k,w,R,T;if(!e.success){y="error",n.enqueue({type:"error",error:e.error});return}let I=e.value;if("error"in I){y="error",n.enqueue({type:"error",error:I.error});return}if(v&&(v=!1,n.enqueue({type:"response-metadata",...d(I)})),null!=I.usage){let{prompt_tokens:e,completion_tokens:t,prompt_tokens_details:n,completion_tokens_details:s}=I.usage;_={promptTokens:null!=e?e:void 0,completionTokens:null!=t?t:void 0},(null==s?void 0:s.reasoning_tokens)!=null&&(x.openai.reasoningTokens=null==s?void 0:s.reasoning_tokens),(null==s?void 0:s.accepted_prediction_tokens)!=null&&(x.openai.acceptedPredictionTokens=null==s?void 0:s.accepted_prediction_tokens),(null==s?void 0:s.rejected_prediction_tokens)!=null&&(x.openai.rejectedPredictionTokens=null==s?void 0:s.rejected_prediction_tokens),(null==n?void 0:n.cached_tokens)!=null&&(x.openai.cachedPromptTokens=null==n?void 0:n.cached_tokens)}let C=I.choices[0];if((null==C?void 0:C.finish_reason)!=null&&(y=o(C.finish_reason)),(null==C?void 0:C.delta)==null)return;let Z=C.delta;null!=Z.content&&n.enqueue({type:"text-delta",textDelta:Z.content});let S=i(null==C?void 0:C.logprobs);(null==S?void 0:S.length)&&(void 0===t&&(t=[]),t.push(...S));let N=b&&null!=Z.function_call?[{type:"function",id:(0,s.Ox)(),function:Z.function_call,index:0}]:Z.tool_calls;if(null!=N)for(let e of N){let t=e.index;if(null==g[t]){if("function"!==e.type)throw new a.Rt({data:e,message:"Expected 'function' type."});if(null==e.id)throw new a.Rt({data:e,message:"Expected 'id' to be a string."});if((null==(r=e.function)?void 0:r.name)==null)throw new a.Rt({data:e,message:"Expected 'function.name' to be a string."});g[t]={id:e.id,type:"function",function:{name:e.function.name,arguments:null!=(l=e.function.arguments)?l:""},hasFinished:!1};let i=g[t];(null==(u=i.function)?void 0:u.name)!=null&&(null==(c=i.function)?void 0:c.arguments)!=null&&(i.function.arguments.length>0&&n.enqueue({type:"tool-call-delta",toolCallType:"function",toolCallId:i.id,toolName:i.function.name,argsTextDelta:i.function.arguments}),(0,s.Gr)(i.function.arguments)&&(n.enqueue({type:"tool-call",toolCallType:"function",toolCallId:null!=(p=i.id)?p:(0,s.Ox)(),toolName:i.function.name,args:i.function.arguments}),i.hasFinished=!0));continue}let i=g[t];!i.hasFinished&&((null==(h=e.function)?void 0:h.arguments)!=null&&(i.function.arguments+=null!=(f=null==(m=e.function)?void 0:m.arguments)?f:""),n.enqueue({type:"tool-call-delta",toolCallType:"function",toolCallId:i.id,toolName:i.function.name,argsTextDelta:null!=(k=e.function.arguments)?k:""}),(null==(w=i.function)?void 0:w.name)!=null&&(null==(R=i.function)?void 0:R.arguments)!=null&&(0,s.Gr)(i.function.arguments)&&(n.enqueue({type:"tool-call",toolCallType:"function",toolCallId:null!=(T=i.id)?T:(0,s.Ox)(),toolName:i.function.name,args:i.function.arguments}),i.hasFinished=!0))}},flush(e){var n,s;e.enqueue({type:"finish",finishReason:y,logprobs:t,usage:{promptTokens:null!=(n=_.promptTokens)?n:NaN,completionTokens:null!=(s=_.completionTokens)?s:NaN},...null!=x?{providerMetadata:x}:{}})}})),rawCall:{rawPrompt:h,rawSettings:f},rawResponse:{headers:c},request:{body:JSON.stringify(l)},warnings:r}}},p=r.Ry({prompt_tokens:r.Rx().nullish(),completion_tokens:r.Rx().nullish(),prompt_tokens_details:r.Ry({cached_tokens:r.Rx().nullish()}).nullish(),completion_tokens_details:r.Ry({reasoning_tokens:r.Rx().nullish(),accepted_prediction_tokens:r.Rx().nullish(),rejected_prediction_tokens:r.Rx().nullish()}).nullish()}).nullish(),h=r.Ry({id:r.Z_().nullish(),created:r.Rx().nullish(),model:r.Z_().nullish(),choices:r.IX(r.Ry({message:r.Ry({role:r.i0("assistant").nullish(),content:r.Z_().nullish(),function_call:r.Ry({arguments:r.Z_(),name:r.Z_()}).nullish(),tool_calls:r.IX(r.Ry({id:r.Z_().nullish(),type:r.i0("function"),function:r.Ry({name:r.Z_(),arguments:r.Z_()})})).nullish()}),index:r.Rx(),logprobs:r.Ry({content:r.IX(r.Ry({token:r.Z_(),logprob:r.Rx(),top_logprobs:r.IX(r.Ry({token:r.Z_(),logprob:r.Rx()}))})).nullable()}).nullish(),finish_reason:r.Z_().nullish()})),usage:p}),m=r.G0([r.Ry({id:r.Z_().nullish(),created:r.Rx().nullish(),model:r.Z_().nullish(),choices:r.IX(r.Ry({delta:r.Ry({role:r.Km(["assistant"]).nullish(),content:r.Z_().nullish(),function_call:r.Ry({name:r.Z_().optional(),arguments:r.Z_().optional()}).nullish(),tool_calls:r.IX(r.Ry({index:r.Rx(),id:r.Z_().nullish(),type:r.i0("function").nullish(),function:r.Ry({name:r.Z_().nullish(),arguments:r.Z_().nullish()})})).nullish()}).nullish(),logprobs:r.Ry({content:r.IX(r.Ry({token:r.Z_(),logprob:r.Rx(),top_logprobs:r.IX(r.Ry({token:r.Z_(),logprob:r.Rx()}))})).nullable()}).nullish(),finish_reason:r.Z_().nullish(),index:r.Rx()})),usage:p}),l]);function f(e){return e.startsWith("o")}var g={"o1-mini":{systemMessageMode:"remove"},"o1-mini-2024-09-12":{systemMessageMode:"remove"},"o1-preview":{systemMessageMode:"remove"},"o1-preview-2024-09-12":{systemMessageMode:"remove"},o3:{systemMessageMode:"developer"},"o3-2025-04-16":{systemMessageMode:"developer"},"o3-mini":{systemMessageMode:"developer"},"o3-mini-2025-01-31":{systemMessageMode:"developer"},"o4-mini":{systemMessageMode:"developer"},"o4-mini-2025-04-16":{systemMessageMode:"developer"}};function y(e){return null==e?void 0:e.tokens.map((t,n)=>({token:t,logprob:e.token_logprobs[n],topLogprobs:e.top_logprobs?Object.entries(e.top_logprobs[n]).map(([e,t])=>({token:e,logprob:t})):[]}))}var _=class{constructor(e,t,n){this.specificationVersion="v1",this.defaultObjectGenerationMode=void 0,this.modelId=e,this.settings=t,this.config=n}get provider(){return this.config.provider}getArgs({mode:e,inputFormat:t,prompt:n,maxTokens:s,temperature:r,topP:i,topK:o,frequencyPenalty:l,presencePenalty:u,stopSequences:d,responseFormat:c,seed:p}){var h;let m=e.type,f=[];null!=o&&f.push({type:"unsupported-setting",setting:"topK"}),null!=c&&"text"!==c.type&&f.push({type:"unsupported-setting",setting:"responseFormat",details:"JSON response format is not supported."});let{prompt:g,stopSequences:y}=function({prompt:e,inputFormat:t,user:n="user",assistant:s="assistant"}){if("prompt"===t&&1===e.length&&"user"===e[0].role&&1===e[0].content.length&&"text"===e[0].content[0].type)return{prompt:e[0].content[0].text};let r="";for(let{role:t,content:i}of("system"===e[0].role&&(r+=`${e[0].content}

`,e=e.slice(1)),e))switch(t){case"system":throw new a.Mt({message:"Unexpected system message in prompt: ${content}",prompt:e});case"user":{let e=i.map(e=>{switch(e.type){case"text":return e.text;case"image":throw new a.A_({functionality:"images"})}}).join("");r+=`${n}:
${e}

`;break}case"assistant":{let e=i.map(e=>{switch(e.type){case"text":return e.text;case"tool-call":throw new a.A_({functionality:"tool-call messages"})}}).join("");r+=`${s}:
${e}

`;break}case"tool":throw new a.A_({functionality:"tool messages"});default:throw Error(`Unsupported role: ${t}`)}return{prompt:r+=`${s}:
`,stopSequences:[`
${n}:`]}}({prompt:n,inputFormat:t}),_=[...null!=y?y:[],...null!=d?d:[]],v={model:this.modelId,echo:this.settings.echo,logit_bias:this.settings.logitBias,logprobs:"number"==typeof this.settings.logprobs?this.settings.logprobs:"boolean"==typeof this.settings.logprobs&&this.settings.logprobs?0:void 0,suffix:this.settings.suffix,user:this.settings.user,max_tokens:s,temperature:r,top_p:i,frequency_penalty:l,presence_penalty:u,seed:p,prompt:g,stop:_.length>0?_:void 0};switch(m){case"regular":if(null==(h=e.tools)?void 0:h.length)throw new a.A_({functionality:"tools"});if(e.toolChoice)throw new a.A_({functionality:"toolChoice"});return{args:v,warnings:f};case"object-json":throw new a.A_({functionality:"object-json mode"});case"object-tool":throw new a.A_({functionality:"object-tool mode"});default:throw Error(`Unsupported type: ${m}`)}}async doGenerate(e){let{args:t,warnings:n}=this.getArgs(e),{responseHeaders:a,value:r,rawValue:i}=await (0,s.A8)({url:this.config.url({path:"/completions",modelId:this.modelId}),headers:(0,s.NF)(this.config.headers(),e.headers),body:t,failedResponseHandler:u,successfulResponseHandler:(0,s.tc)(v),abortSignal:e.abortSignal,fetch:this.config.fetch}),{prompt:l,...c}=t,p=r.choices[0];return{text:p.text,usage:{promptTokens:r.usage.prompt_tokens,completionTokens:r.usage.completion_tokens},finishReason:o(p.finish_reason),logprobs:y(p.logprobs),rawCall:{rawPrompt:l,rawSettings:c},rawResponse:{headers:a,body:i},response:d(r),warnings:n,request:{body:JSON.stringify(t)}}}async doStream(e){let t;let{args:n,warnings:a}=this.getArgs(e),r={...n,stream:!0,stream_options:"strict"===this.config.compatibility?{include_usage:!0}:void 0},{responseHeaders:i,value:l}=await (0,s.A8)({url:this.config.url({path:"/completions",modelId:this.modelId}),headers:(0,s.NF)(this.config.headers(),e.headers),body:r,failedResponseHandler:u,successfulResponseHandler:(0,s.cP)(b),abortSignal:e.abortSignal,fetch:this.config.fetch}),{prompt:c,...p}=n,h="unknown",m={promptTokens:Number.NaN,completionTokens:Number.NaN},f=!0;return{stream:l.pipeThrough(new TransformStream({transform(e,n){if(!e.success){h="error",n.enqueue({type:"error",error:e.error});return}let s=e.value;if("error"in s){h="error",n.enqueue({type:"error",error:s.error});return}f&&(f=!1,n.enqueue({type:"response-metadata",...d(s)})),null!=s.usage&&(m={promptTokens:s.usage.prompt_tokens,completionTokens:s.usage.completion_tokens});let a=s.choices[0];(null==a?void 0:a.finish_reason)!=null&&(h=o(a.finish_reason)),(null==a?void 0:a.text)!=null&&n.enqueue({type:"text-delta",textDelta:a.text});let r=y(null==a?void 0:a.logprobs);(null==r?void 0:r.length)&&(void 0===t&&(t=[]),t.push(...r))},flush(e){e.enqueue({type:"finish",finishReason:h,logprobs:t,usage:m})}})),rawCall:{rawPrompt:c,rawSettings:p},rawResponse:{headers:i},warnings:a,request:{body:JSON.stringify(r)}}}},v=r.Ry({id:r.Z_().nullish(),created:r.Rx().nullish(),model:r.Z_().nullish(),choices:r.IX(r.Ry({text:r.Z_(),finish_reason:r.Z_(),logprobs:r.Ry({tokens:r.IX(r.Z_()),token_logprobs:r.IX(r.Rx()),top_logprobs:r.IX(r.IM(r.Z_(),r.Rx())).nullable()}).nullish()})),usage:r.Ry({prompt_tokens:r.Rx(),completion_tokens:r.Rx()})}),b=r.G0([r.Ry({id:r.Z_().nullish(),created:r.Rx().nullish(),model:r.Z_().nullish(),choices:r.IX(r.Ry({text:r.Z_(),finish_reason:r.Z_().nullish(),index:r.Rx(),logprobs:r.Ry({tokens:r.IX(r.Z_()),token_logprobs:r.IX(r.Rx()),top_logprobs:r.IX(r.IM(r.Z_(),r.Rx())).nullable()}).nullish()})),usage:r.Ry({prompt_tokens:r.Rx(),completion_tokens:r.Rx()}).nullish()}),l]),x=class{constructor(e,t,n){this.specificationVersion="v1",this.modelId=e,this.settings=t,this.config=n}get provider(){return this.config.provider}get maxEmbeddingsPerCall(){var e;return null!=(e=this.settings.maxEmbeddingsPerCall)?e:2048}get supportsParallelCalls(){var e;return null==(e=this.settings.supportsParallelCalls)||e}async doEmbed({values:e,headers:t,abortSignal:n}){if(e.length>this.maxEmbeddingsPerCall)throw new a.ON({provider:this.provider,modelId:this.modelId,maxEmbeddingsPerCall:this.maxEmbeddingsPerCall,values:e});let{responseHeaders:r,value:i}=await (0,s.A8)({url:this.config.url({path:"/embeddings",modelId:this.modelId}),headers:(0,s.NF)(this.config.headers(),t),body:{model:this.modelId,input:e,encoding_format:"float",dimensions:this.settings.dimensions,user:this.settings.user},failedResponseHandler:u,successfulResponseHandler:(0,s.tc)(k),abortSignal:n,fetch:this.config.fetch});return{embeddings:i.data.map(e=>e.embedding),usage:i.usage?{tokens:i.usage.prompt_tokens}:void 0,rawResponse:{headers:r}}}},k=r.Ry({data:r.IX(r.Ry({embedding:r.IX(r.Rx())})),usage:r.Ry({prompt_tokens:r.Rx()}).nullish()}),w={"dall-e-3":1,"dall-e-2":10,"gpt-image-1":10},R=new Set(["gpt-image-1"]),T=class{constructor(e,t,n){this.modelId=e,this.settings=t,this.config=n,this.specificationVersion="v1"}get maxImagesPerCall(){var e,t;return null!=(t=null!=(e=this.settings.maxImagesPerCall)?e:w[this.modelId])?t:1}get provider(){return this.config.provider}async doGenerate({prompt:e,n:t,size:n,aspectRatio:a,seed:r,providerOptions:i,headers:o,abortSignal:l}){var d,c,p,h;let m=[];null!=a&&m.push({type:"unsupported-setting",setting:"aspectRatio",details:"This model does not support aspect ratio. Use `size` instead."}),null!=r&&m.push({type:"unsupported-setting",setting:"seed"});let f=null!=(p=null==(c=null==(d=this.config._internal)?void 0:d.currentDate)?void 0:c.call(d))?p:new Date,{value:g,responseHeaders:y}=await (0,s.A8)({url:this.config.url({path:"/images/generations",modelId:this.modelId}),headers:(0,s.NF)(this.config.headers(),o),body:{model:this.modelId,prompt:e,n:t,size:n,...null!=(h=i.openai)?h:{},...R.has(this.modelId)?{}:{response_format:"b64_json"}},failedResponseHandler:u,successfulResponseHandler:(0,s.tc)(I),abortSignal:l,fetch:this.config.fetch});return{images:g.data.map(e=>e.b64_json),warnings:m,response:{timestamp:f,modelId:this.modelId,headers:y}}}},I=r.Ry({data:r.IX(r.Ry({b64_json:r.Z_()}))}),C=r.Ry({include:r.IX(r.Z_()).nullish(),language:r.Z_().nullish(),prompt:r.Z_().nullish(),temperature:r.Rx().min(0).max(1).nullish().default(0),timestampGranularities:r.IX(r.Km(["word","segment"])).nullish().default(["segment"])}),Z={afrikaans:"af",arabic:"ar",armenian:"hy",azerbaijani:"az",belarusian:"be",bosnian:"bs",bulgarian:"bg",catalan:"ca",chinese:"zh",croatian:"hr",czech:"cs",danish:"da",dutch:"nl",english:"en",estonian:"et",finnish:"fi",french:"fr",galician:"gl",german:"de",greek:"el",hebrew:"he",hindi:"hi",hungarian:"hu",icelandic:"is",indonesian:"id",italian:"it",japanese:"ja",kannada:"kn",kazakh:"kk",korean:"ko",latvian:"lv",lithuanian:"lt",macedonian:"mk",malay:"ms",marathi:"mr",maori:"mi",nepali:"ne",norwegian:"no",persian:"fa",polish:"pl",portuguese:"pt",romanian:"ro",russian:"ru",serbian:"sr",slovak:"sk",slovenian:"sl",spanish:"es",swahili:"sw",swedish:"sv",tagalog:"tl",tamil:"ta",thai:"th",turkish:"tr",ukrainian:"uk",urdu:"ur",vietnamese:"vi",welsh:"cy"},S=class{constructor(e,t){this.modelId=e,this.config=t,this.specificationVersion="v1"}get provider(){return this.config.provider}getArgs({audio:e,mediaType:t,providerOptions:n}){var a,r,i,o,l;let u=(0,s.c1)({provider:"openai",providerOptions:n,schema:C}),d=new FormData,c=e instanceof Uint8Array?new Blob([e]):new Blob([(0,s.MS)(e)]);if(d.append("model",this.modelId),d.append("file",new File([c],"audio",{type:t})),u){let e={include:null!=(a=u.include)?a:void 0,language:null!=(r=u.language)?r:void 0,prompt:null!=(i=u.prompt)?i:void 0,temperature:null!=(o=u.temperature)?o:void 0,timestamp_granularities:null!=(l=u.timestampGranularities)?l:void 0};for(let t in e){let n=e[t];void 0!==n&&d.append(t,String(n))}}return{formData:d,warnings:[]}}async doGenerate(e){var t,n,a,r,i,o;let l=null!=(a=null==(n=null==(t=this.config._internal)?void 0:t.currentDate)?void 0:n.call(t))?a:new Date,{formData:d,warnings:c}=this.getArgs(e),{value:p,responseHeaders:h,rawValue:m}=await (0,s.do)({url:this.config.url({path:"/audio/transcriptions",modelId:this.modelId}),headers:(0,s.NF)(this.config.headers(),e.headers),formData:d,failedResponseHandler:u,successfulResponseHandler:(0,s.tc)(N),abortSignal:e.abortSignal,fetch:this.config.fetch}),f=null!=p.language&&p.language in Z?Z[p.language]:void 0;return{text:p.text,segments:null!=(i=null==(r=p.words)?void 0:r.map(e=>({text:e.word,startSecond:e.start,endSecond:e.end})))?i:[],language:f,durationInSeconds:null!=(o=p.duration)?o:void 0,warnings:c,response:{timestamp:l,modelId:this.modelId,headers:h,body:m}}}},N=r.Ry({text:r.Z_(),language:r.Z_().nullish(),duration:r.Rx().nullish(),words:r.IX(r.Ry({word:r.Z_(),start:r.Rx(),end:r.Rx()})).nullish()});function O({finishReason:e,hasToolCalls:t}){switch(e){case void 0:case null:return t?"tool-calls":"stop";case"max_output_tokens":return"length";case"content_filter":return"content-filter";default:return t?"tool-calls":"unknown"}}var A=class{constructor(e,t){this.specificationVersion="v1",this.defaultObjectGenerationMode="json",this.supportsStructuredOutputs=!0,this.modelId=e,this.config=t}get provider(){return this.config.provider}getArgs({mode:e,maxTokens:t,temperature:n,stopSequences:r,topP:i,topK:o,presencePenalty:l,frequencyPenalty:u,seed:d,prompt:c,providerMetadata:p,responseFormat:h}){var m,f,g,y;let _=[],v=(y=this.modelId).startsWith("o")?y.startsWith("o1-mini")||y.startsWith("o1-preview")?{isReasoningModel:!0,systemMessageMode:"remove",requiredAutoTruncation:!1}:{isReasoningModel:!0,systemMessageMode:"developer",requiredAutoTruncation:!1}:{isReasoningModel:!1,systemMessageMode:"system",requiredAutoTruncation:!1},b=e.type;null!=o&&_.push({type:"unsupported-setting",setting:"topK"}),null!=d&&_.push({type:"unsupported-setting",setting:"seed"}),null!=l&&_.push({type:"unsupported-setting",setting:"presencePenalty"}),null!=u&&_.push({type:"unsupported-setting",setting:"frequencyPenalty"}),null!=r&&_.push({type:"unsupported-setting",setting:"stopSequences"});let{messages:x,warnings:k}=function({prompt:e,systemMessageMode:t}){let n=[],r=[];for(let{role:i,content:o}of e)switch(i){case"system":switch(t){case"system":n.push({role:"system",content:o});break;case"developer":n.push({role:"developer",content:o});break;case"remove":r.push({type:"other",message:"system messages are removed for this model"});break;default:throw Error(`Unsupported system message mode: ${t}`)}break;case"user":n.push({role:"user",content:o.map((e,t)=>{var n,r,i,o;switch(e.type){case"text":return{type:"input_text",text:e.text};case"image":return{type:"input_image",image_url:e.image instanceof URL?e.image.toString():`data:${null!=(n=e.mimeType)?n:"image/jpeg"};base64,${(0,s.k0)(e.image)}`,detail:null==(i=null==(r=e.providerMetadata)?void 0:r.openai)?void 0:i.imageDetail};case"file":if(e.data instanceof URL)throw new a.A_({functionality:"File URLs in user messages"});if("application/pdf"===e.mimeType)return{type:"input_file",filename:null!=(o=e.filename)?o:`part-${t}.pdf`,file_data:`data:application/pdf;base64,${e.data}`};throw new a.A_({functionality:"Only PDF files are supported in user messages"})}})});break;case"assistant":for(let e of o)switch(e.type){case"text":n.push({role:"assistant",content:[{type:"output_text",text:e.text}]});break;case"tool-call":n.push({type:"function_call",call_id:e.toolCallId,name:e.toolName,arguments:JSON.stringify(e.args)})}break;case"tool":for(let e of o)n.push({type:"function_call_output",call_id:e.toolCallId,output:JSON.stringify(e.result)});break;default:throw Error(`Unsupported role: ${i}`)}return{messages:n,warnings:r}}({prompt:c,systemMessageMode:v.systemMessageMode});_.push(...k);let w=(0,s.c1)({provider:"openai",providerOptions:p,schema:z}),R=null==(m=null==w?void 0:w.strictSchemas)||m,T={model:this.modelId,input:x,temperature:n,top_p:i,max_output_tokens:t,...(null==h?void 0:h.type)==="json"&&{text:{format:null!=h.schema?{type:"json_schema",strict:R,name:null!=(f=h.name)?f:"response",description:h.description,schema:h.schema}:{type:"json_object"}}},metadata:null==w?void 0:w.metadata,parallel_tool_calls:null==w?void 0:w.parallelToolCalls,previous_response_id:null==w?void 0:w.previousResponseId,store:null==w?void 0:w.store,user:null==w?void 0:w.user,instructions:null==w?void 0:w.instructions,...v.isReasoningModel&&((null==w?void 0:w.reasoningEffort)!=null||(null==w?void 0:w.reasoningSummary)!=null)&&{reasoning:{...(null==w?void 0:w.reasoningEffort)!=null&&{effort:w.reasoningEffort},...(null==w?void 0:w.reasoningSummary)!=null&&{summary:w.reasoningSummary}}},...v.requiredAutoTruncation&&{truncation:"auto"}};switch(v.isReasoningModel&&(null!=T.temperature&&(T.temperature=void 0,_.push({type:"unsupported-setting",setting:"temperature",details:"temperature is not supported for reasoning models"})),null!=T.top_p&&(T.top_p=void 0,_.push({type:"unsupported-setting",setting:"topP",details:"topP is not supported for reasoning models"}))),b){case"regular":{let{tools:t,tool_choice:n,toolWarnings:s}=function({mode:e,strict:t}){var n;let s=(null==(n=e.tools)?void 0:n.length)?e.tools:void 0,r=[];if(null==s)return{tools:void 0,tool_choice:void 0,toolWarnings:r};let i=e.toolChoice,o=[];for(let e of s)switch(e.type){case"function":o.push({type:"function",name:e.name,description:e.description,parameters:e.parameters,strict:!!t||void 0});break;case"provider-defined":"openai.web_search_preview"===e.id?o.push({type:"web_search_preview",search_context_size:e.args.searchContextSize,user_location:e.args.userLocation}):r.push({type:"unsupported-tool",tool:e});break;default:r.push({type:"unsupported-tool",tool:e})}if(null==i)return{tools:o,tool_choice:void 0,toolWarnings:r};let l=i.type;switch(l){case"auto":case"none":case"required":return{tools:o,tool_choice:l,toolWarnings:r};case"tool":if("web_search_preview"===i.toolName)return{tools:o,tool_choice:{type:"web_search_preview"},toolWarnings:r};return{tools:o,tool_choice:{type:"function",name:i.toolName},toolWarnings:r};default:throw new a.A_({functionality:`Unsupported tool choice type: ${l}`})}}({mode:e,strict:R});return{args:{...T,tools:t,tool_choice:n},warnings:[..._,...s]}}case"object-json":return{args:{...T,text:{format:null!=e.schema?{type:"json_schema",strict:R,name:null!=(g=e.name)?g:"response",description:e.description,schema:e.schema}:{type:"json_object"}}},warnings:_};case"object-tool":return{args:{...T,tool_choice:{type:"function",name:e.tool.name},tools:[{type:"function",name:e.tool.name,description:e.tool.description,parameters:e.tool.parameters,strict:R}]},warnings:_};default:throw Error(`Unsupported type: ${b}`)}}async doGenerate(e){var t,n,i,o,l,d,c;let{args:p,warnings:h}=this.getArgs(e),m=this.config.url({path:"/responses",modelId:this.modelId}),{responseHeaders:f,value:g,rawValue:y}=await (0,s.A8)({url:m,headers:(0,s.NF)(this.config.headers(),e.headers),body:p,failedResponseHandler:u,successfulResponseHandler:(0,s.tc)(r.Ry({id:r.Z_(),created_at:r.Rx(),error:r.Ry({message:r.Z_(),code:r.Z_()}).nullish(),model:r.Z_(),output:r.IX(r.VK("type",[r.Ry({type:r.i0("message"),role:r.i0("assistant"),content:r.IX(r.Ry({type:r.i0("output_text"),text:r.Z_(),annotations:r.IX(r.Ry({type:r.i0("url_citation"),start_index:r.Rx(),end_index:r.Rx(),url:r.Z_(),title:r.Z_()}))}))}),r.Ry({type:r.i0("function_call"),call_id:r.Z_(),name:r.Z_(),arguments:r.Z_()}),r.Ry({type:r.i0("web_search_call")}),r.Ry({type:r.i0("computer_call")}),r.Ry({type:r.i0("reasoning"),summary:r.IX(r.Ry({type:r.i0("summary_text"),text:r.Z_()}))})])),incomplete_details:r.Ry({reason:r.Z_()}).nullable(),usage:j})),abortSignal:e.abortSignal,fetch:this.config.fetch});if(g.error)throw new a.w({message:g.error.message,url:m,requestBodyValues:p,statusCode:400,responseHeaders:f,responseBody:y,isRetryable:!1});let _=g.output.filter(e=>"message"===e.type).flatMap(e=>e.content).filter(e=>"output_text"===e.type),v=g.output.filter(e=>"function_call"===e.type).map(e=>({toolCallType:"function",toolCallId:e.call_id,toolName:e.name,args:e.arguments})),b=null!=(n=null==(t=g.output.find(e=>"reasoning"===e.type))?void 0:t.summary)?n:null;return{text:_.map(e=>e.text).join("\n"),sources:_.flatMap(e=>e.annotations.map(e=>{var t,n,a;return{sourceType:"url",id:null!=(a=null==(n=(t=this.config).generateId)?void 0:n.call(t))?a:(0,s.Ox)(),url:e.url,title:e.title}})),finishReason:O({finishReason:null==(i=g.incomplete_details)?void 0:i.reason,hasToolCalls:v.length>0}),toolCalls:v.length>0?v:void 0,reasoning:b?b.map(e=>({type:"text",text:e.text})):void 0,usage:{promptTokens:g.usage.input_tokens,completionTokens:g.usage.output_tokens},rawCall:{rawPrompt:void 0,rawSettings:{}},rawResponse:{headers:f,body:y},request:{body:JSON.stringify(p)},response:{id:g.id,timestamp:new Date(1e3*g.created_at),modelId:g.model},providerMetadata:{openai:{responseId:g.id,cachedPromptTokens:null!=(l=null==(o=g.usage.input_tokens_details)?void 0:o.cached_tokens)?l:null,reasoningTokens:null!=(c=null==(d=g.usage.output_tokens_details)?void 0:d.reasoning_tokens)?c:null}},warnings:h}}async doStream(e){let{args:t,warnings:n}=this.getArgs(e),{responseHeaders:a,value:r}=await (0,s.A8)({url:this.config.url({path:"/responses",modelId:this.modelId}),headers:(0,s.NF)(this.config.headers(),e.headers),body:{...t,stream:!0},failedResponseHandler:u,successfulResponseHandler:(0,s.cP)(V),abortSignal:e.abortSignal,fetch:this.config.fetch}),i=this,o="unknown",l=NaN,d=NaN,c=null,p=null,h=null,m={},f=!1;return{stream:r.pipeThrough(new TransformStream({transform(e,t){var n,a,r,u,g,y,_,v;if(!e.success){o="error",t.enqueue({type:"error",error:e.error});return}let b=e.value;if("response.output_item.added"===b.type)"function_call"===b.item.type&&(m[b.output_index]={toolName:b.item.name,toolCallId:b.item.call_id},t.enqueue({type:"tool-call-delta",toolCallType:"function",toolCallId:b.item.call_id,toolName:b.item.name,argsTextDelta:b.item.arguments}));else if("response.function_call_arguments.delta"===b.type){let e=m[b.output_index];null!=e&&t.enqueue({type:"tool-call-delta",toolCallType:"function",toolCallId:e.toolCallId,toolName:e.toolName,argsTextDelta:b.delta})}else"response.created"===b.type?(h=b.response.id,t.enqueue({type:"response-metadata",id:b.response.id,timestamp:new Date(1e3*b.response.created_at),modelId:b.response.model})):"response.output_text.delta"===b.type?t.enqueue({type:"text-delta",textDelta:b.delta}):"response.reasoning_summary_text.delta"===b.type?t.enqueue({type:"reasoning",textDelta:b.delta}):"response.output_item.done"===b.type&&"function_call"===b.item.type?(m[b.output_index]=void 0,f=!0,t.enqueue({type:"tool-call",toolCallType:"function",toolCallId:b.item.call_id,toolName:b.item.name,args:b.item.arguments})):"response.completed"===b.type||"response.incomplete"===b.type?(o=O({finishReason:null==(n=b.response.incomplete_details)?void 0:n.reason,hasToolCalls:f}),l=b.response.usage.input_tokens,d=b.response.usage.output_tokens,c=null!=(r=null==(a=b.response.usage.input_tokens_details)?void 0:a.cached_tokens)?r:c,p=null!=(g=null==(u=b.response.usage.output_tokens_details)?void 0:u.reasoning_tokens)?g:p):"response.output_text.annotation.added"===b.type?t.enqueue({type:"source",source:{sourceType:"url",id:null!=(v=null==(_=(y=i.config).generateId)?void 0:_.call(y))?v:(0,s.Ox)(),url:b.annotation.url,title:b.annotation.title}}):"error"===b.type&&t.enqueue({type:"error",error:b})},flush(e){e.enqueue({type:"finish",finishReason:o,usage:{promptTokens:l,completionTokens:d},...(null!=c||null!=p)&&{providerMetadata:{openai:{responseId:h,cachedPromptTokens:c,reasoningTokens:p}}}})}})),rawCall:{rawPrompt:void 0,rawSettings:{}},rawResponse:{headers:a},request:{body:JSON.stringify(t)},warnings:n}}},j=r.Ry({input_tokens:r.Rx(),input_tokens_details:r.Ry({cached_tokens:r.Rx().nullish()}).nullish(),output_tokens:r.Rx(),output_tokens_details:r.Ry({reasoning_tokens:r.Rx().nullish()}).nullish()}),E=r.Ry({type:r.i0("response.output_text.delta"),delta:r.Z_()}),$=r.Ry({type:r.Km(["response.completed","response.incomplete"]),response:r.Ry({incomplete_details:r.Ry({reason:r.Z_()}).nullish(),usage:j})}),M=r.Ry({type:r.i0("response.created"),response:r.Ry({id:r.Z_(),created_at:r.Rx(),model:r.Z_()})}),P=r.Ry({type:r.i0("response.output_item.done"),output_index:r.Rx(),item:r.VK("type",[r.Ry({type:r.i0("message")}),r.Ry({type:r.i0("function_call"),id:r.Z_(),call_id:r.Z_(),name:r.Z_(),arguments:r.Z_(),status:r.i0("completed")})])}),q=r.Ry({type:r.i0("response.function_call_arguments.delta"),item_id:r.Z_(),output_index:r.Rx(),delta:r.Z_()}),F=r.Ry({type:r.i0("response.output_item.added"),output_index:r.Rx(),item:r.VK("type",[r.Ry({type:r.i0("message")}),r.Ry({type:r.i0("function_call"),id:r.Z_(),call_id:r.Z_(),name:r.Z_(),arguments:r.Z_()})])}),U=r.Ry({type:r.i0("response.output_text.annotation.added"),annotation:r.Ry({type:r.i0("url_citation"),url:r.Z_(),title:r.Z_()})}),D=r.Ry({type:r.i0("response.reasoning_summary_text.delta"),item_id:r.Z_(),output_index:r.Rx(),summary_index:r.Rx(),delta:r.Z_()}),L=r.Ry({type:r.i0("error"),code:r.Z_(),message:r.Z_(),param:r.Z_().nullish(),sequence_number:r.Rx()}),V=r.G0([E,$,M,P,q,F,U,D,L,r.Ry({type:r.Z_()}).passthrough()]),z=r.Ry({metadata:r.Yj().nullish(),parallelToolCalls:r.O7().nullish(),previousResponseId:r.Z_().nullish(),store:r.O7().nullish(),user:r.Z_().nullish(),reasoningEffort:r.Z_().nullish(),strictSchemas:r.O7().nullish(),instructions:r.Z_().nullish(),reasoningSummary:r.Z_().nullish()}),K=r.Ry({}),X={webSearchPreview:function({searchContextSize:e,userLocation:t}={}){return{type:"provider-defined",id:"openai.web_search_preview",args:{searchContextSize:e,userLocation:t},parameters:K}}},B=r.Ry({instructions:r.Z_().nullish(),speed:r.Rx().min(.25).max(4).default(1).nullish()}),G=class{constructor(e,t){this.modelId=e,this.config=t,this.specificationVersion="v1"}get provider(){return this.config.provider}getArgs({text:e,voice:t="alloy",outputFormat:n="mp3",speed:a,instructions:r,providerOptions:i}){let o=[],l=(0,s.c1)({provider:"openai",providerOptions:i,schema:B}),u={model:this.modelId,input:e,voice:t,response_format:"mp3",speed:a,instructions:r};if(n&&(["mp3","opus","aac","flac","wav","pcm"].includes(n)?u.response_format=n:o.push({type:"unsupported-setting",setting:"outputFormat",details:`Unsupported output format: ${n}. Using mp3 instead.`})),l){let e={};for(let t in e){let n=e[t];void 0!==n&&(u[t]=n)}}return{requestBody:u,warnings:o}}async doGenerate(e){var t,n,a;let r=null!=(a=null==(n=null==(t=this.config._internal)?void 0:t.currentDate)?void 0:n.call(t))?a:new Date,{requestBody:i,warnings:o}=this.getArgs(e),{value:l,responseHeaders:d,rawValue:c}=await (0,s.A8)({url:this.config.url({path:"/audio/speech",modelId:this.modelId}),headers:(0,s.NF)(this.config.headers(),e.headers),body:i,failedResponseHandler:u,successfulResponseHandler:(0,s.XH)(),abortSignal:e.abortSignal,fetch:this.config.fetch});return{audio:l,warnings:o,request:{body:JSON.stringify(i)},response:{timestamp:r,modelId:this.modelId,headers:d,body:c}}}};function H(e={}){var t,n,a;let r=null!=(t=(0,s.QT)(e.baseURL))?t:"https://api.openai.com/v1",i=null!=(n=e.compatibility)?n:"compatible",o=null!=(a=e.name)?a:"openai",l=()=>({Authorization:`Bearer ${(0,s.pd)({apiKey:e.apiKey,environmentVariableName:"OPENAI_API_KEY",description:"OpenAI"})}`,"OpenAI-Organization":e.organization,"OpenAI-Project":e.project,...e.headers}),u=(t,n={})=>new c(t,n,{provider:`${o}.chat`,url:({path:e})=>`${r}${e}`,headers:l,compatibility:i,fetch:e.fetch}),d=(t,n={})=>new _(t,n,{provider:`${o}.completion`,url:({path:e})=>`${r}${e}`,headers:l,compatibility:i,fetch:e.fetch}),p=(t,n={})=>new x(t,n,{provider:`${o}.embedding`,url:({path:e})=>`${r}${e}`,headers:l,fetch:e.fetch}),h=(t,n={})=>new T(t,n,{provider:`${o}.image`,url:({path:e})=>`${r}${e}`,headers:l,fetch:e.fetch}),m=t=>new S(t,{provider:`${o}.transcription`,url:({path:e})=>`${r}${e}`,headers:l,fetch:e.fetch}),f=t=>new G(t,{provider:`${o}.speech`,url:({path:e})=>`${r}${e}`,headers:l,fetch:e.fetch}),g=(e,t)=>{if(new.target)throw Error("The OpenAI model function cannot be called with the new keyword.");return"gpt-3.5-turbo-instruct"===e?d(e,t):u(e,t)},y=function(e,t){return g(e,t)};return y.languageModel=g,y.chat=u,y.completion=d,y.responses=t=>new A(t,{provider:`${o}.responses`,url:({path:e})=>`${r}${e}`,headers:l,fetch:e.fetch}),y.embedding=p,y.textEmbedding=p,y.textEmbeddingModel=p,y.image=h,y.imageModel=h,y.transcription=m,y.transcriptionModel=m,y.speech=f,y.speechModel=f,y.tools=X,y}H({compatibility:"strict"})},3888:(e,t,n)=>{n.d(t,{NF:()=>i,RV:()=>o,MS:()=>O,k0:()=>A,XH:()=>Z,cP:()=>I,bF:()=>d,Sq:()=>T,tc:()=>C,gw:()=>l,Ox:()=>c,e$:()=>p,D_:()=>h,Gr:()=>_,pd:()=>m,c1:()=>v,do:()=>k,A8:()=>x,DB:()=>R,NX:()=>y,pW:()=>g,pY:()=>f,QT:()=>j});var s=n(1760);let a=(e,t=21)=>(n=t)=>{let s="",a=0|n;for(;a--;)s+=e[Math.random()*e.length|0];return s};var r=n(5762);function i(...e){return e.reduce((e,t)=>({...e,...null!=t?t:{}}),{})}function o(e){return new ReadableStream({async pull(t){try{let{value:n,done:s}=await e.next();s?t.close():t.enqueue(n)}catch(e){t.error(e)}},cancel(){}})}async function l(e){return null==e?Promise.resolve():new Promise(t=>setTimeout(t,e))}function u(e){let t={};return e.headers.forEach((e,n)=>{t[n]=e}),t}var d=({prefix:e,size:t=16,alphabet:n="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz",separator:r="-"}={})=>{let i=a(n,t);if(null==e)return i;if(n.includes(r))throw new s.RU({argument:"separator",message:`The separator "${r}" must not be part of the alphabet "${n}".`});return t=>`${e}${r}${i(t)}`},c=d();function p(e){return null==e?"unknown error":"string"==typeof e?e:e instanceof Error?e.message:JSON.stringify(e)}function h(e){return e instanceof Error&&("AbortError"===e.name||"TimeoutError"===e.name)}function m({apiKey:e,environmentVariableName:t,apiKeyParameterName:n="apiKey",description:a}){if("string"==typeof e)return e;if(null!=e)throw new s.tZ({message:`${a} API key must be a string.`});if("undefined"==typeof process)throw new s.tZ({message:`${a} API key is missing. Pass it using the '${n}' parameter. Environment variables is not supported in this environment.`});if(null==(e=process.env[t]))throw new s.tZ({message:`${a} API key is missing. Pass it using the '${n}' parameter or the ${t} environment variable.`});if("string"!=typeof e)throw new s.tZ({message:`${a} API key must be a string. The value of the ${t} environment variable is not a string.`});return e}var f=Symbol.for("vercel.ai.validator");function g({value:e,schema:t}){let n="object"==typeof t&&null!==t&&f in t&&!0===t[f]&&"validate"in t?t:{[f]:!0,validate:e=>{let n=t.safeParse(e);return n.success?{success:!0,value:n.data}:{success:!1,error:n.error}}};try{if(null==n.validate)return{success:!0,value:e};let t=n.validate(e);if(t.success)return t;return{success:!1,error:s.Gz.wrap({value:e,cause:t.error})}}catch(t){return{success:!1,error:s.Gz.wrap({value:e,cause:t})}}}function y({text:e,schema:t}){try{let n=r.parse(e);if(null==t)return{success:!0,value:n,rawValue:n};let s=g({value:n,schema:t});return s.success?{...s,rawValue:n}:s}catch(t){return{success:!1,error:s.xU.isInstance(t)?t:new s.xU({text:e,cause:t})}}}function _(e){try{return r.parse(e),!0}catch(e){return!1}}function v({provider:e,providerOptions:t,schema:n}){if((null==t?void 0:t[e])==null)return;let a=g({value:t[e],schema:n});if(!a.success)throw new s.RU({argument:"providerOptions",message:`invalid ${e} provider options`,cause:a.error});return a.value}var b=()=>globalThis.fetch,x=async({url:e,headers:t,body:n,failedResponseHandler:s,successfulResponseHandler:a,abortSignal:r,fetch:i})=>w({url:e,headers:{"Content-Type":"application/json",...t},body:{content:JSON.stringify(n),values:n},failedResponseHandler:s,successfulResponseHandler:a,abortSignal:r,fetch:i}),k=async({url:e,headers:t,formData:n,failedResponseHandler:s,successfulResponseHandler:a,abortSignal:r,fetch:i})=>w({url:e,headers:t,body:{content:n,values:Object.fromEntries(n.entries())},failedResponseHandler:s,successfulResponseHandler:a,abortSignal:r,fetch:i}),w=async({url:e,headers:t={},body:n,successfulResponseHandler:a,failedResponseHandler:r,abortSignal:i,fetch:o=b()})=>{try{let l=await o(e,{method:"POST",headers:Object.fromEntries(Object.entries(t).filter(([e,t])=>null!=t)),body:n.content,signal:i}),d=u(l);if(!l.ok){let t;try{t=await r({response:l,url:e,requestBodyValues:n.values})}catch(t){if(h(t)||s.w.isInstance(t))throw t;throw new s.w({message:"Failed to process error response",cause:t,statusCode:l.status,url:e,responseHeaders:d,requestBodyValues:n.values})}throw t.value}try{return await a({response:l,url:e,requestBodyValues:n.values})}catch(t){if(t instanceof Error&&(h(t)||s.w.isInstance(t)))throw t;throw new s.w({message:"Failed to process successful response",cause:t,statusCode:l.status,url:e,responseHeaders:d,requestBodyValues:n.values})}}catch(t){if(h(t))throw t;if(t instanceof TypeError&&"fetch failed"===t.message){let a=t.cause;if(null!=a)throw new s.w({message:`Cannot connect to API: ${a.message}`,cause:a,url:e,requestBodyValues:n.values,isRetryable:!0})}throw t}};async function R(e){return"function"==typeof e&&(e=e()),Promise.resolve(e)}var T=({errorSchema:e,errorToMessage:t,isRetryable:n})=>async({response:a,url:i,requestBodyValues:o})=>{let l=await a.text(),d=u(a);if(""===l.trim())return{responseHeaders:d,value:new s.w({message:a.statusText,url:i,requestBodyValues:o,statusCode:a.status,responseHeaders:d,responseBody:l,isRetryable:null==n?void 0:n(a)})};try{let u=function({text:e,schema:t}){try{let n=r.parse(e);if(null==t)return n;return function({value:e,schema:t}){let n=g({value:e,schema:t});if(!n.success)throw s.Gz.wrap({value:e,cause:n.error});return n.value}({value:n,schema:t})}catch(t){if(s.xU.isInstance(t)||s.Gz.isInstance(t))throw t;throw new s.xU({text:e,cause:t})}}({text:l,schema:e});return{responseHeaders:d,value:new s.w({message:t(u),url:i,requestBodyValues:o,statusCode:a.status,responseHeaders:d,responseBody:l,data:u,isRetryable:null==n?void 0:n(a,u)})}}catch(e){return{responseHeaders:d,value:new s.w({message:a.statusText,url:i,requestBodyValues:o,statusCode:a.status,responseHeaders:d,responseBody:l,isRetryable:null==n?void 0:n(a)})}}},I=e=>async({response:t})=>{let n=u(t);if(null==t.body)throw new s.g8({});return{responseHeaders:n,value:t.body.pipeThrough(new TextDecoderStream).pipeThrough(function(){let e,t,n,s="",a=[];function r(e,t){if(""===e){i(t);return}if(e.startsWith(":"))return;let n=e.indexOf(":");if(-1===n){o(e,"");return}let s=e.slice(0,n),a=n+1;o(s,a<e.length&&" "===e[a]?e.slice(a+1):e.slice(a))}function i(s){a.length>0&&(s.enqueue({event:e,data:a.join("\n"),id:t,retry:n}),a=[],e=void 0,n=void 0)}function o(s,r){switch(s){case"event":e=r;break;case"data":a.push(r);break;case"id":t=r;break;case"retry":let i=parseInt(r,10);isNaN(i)||(n=i)}}return new TransformStream({transform(e,t){let{lines:n,incompleteLine:a}=function(e,t){let n=[],s=e;for(let e=0;e<t.length;){let a=t[e++];"\n"===a?(n.push(s),s=""):"\r"===a?(n.push(s),s="","\n"===t[e]&&e++):s+=a}return{lines:n,incompleteLine:s}}(s,e);s=a;for(let e=0;e<n.length;e++)r(n[e],t)},flush(e){r(s,e),i(e)}})}()).pipeThrough(new TransformStream({transform({data:t},n){"[DONE]"!==t&&n.enqueue(y({text:t,schema:e}))}}))}},C=e=>async({response:t,url:n,requestBodyValues:a})=>{let r=await t.text(),i=y({text:r,schema:e}),o=u(t);if(!i.success)throw new s.w({message:"Invalid JSON response",cause:i.error,statusCode:t.status,responseHeaders:o,responseBody:r,url:n,requestBodyValues:a});return{responseHeaders:o,value:i.value,rawValue:i.rawValue}},Z=()=>async({response:e,url:t,requestBodyValues:n})=>{let a=u(e);if(!e.body)throw new s.w({message:"Response body is empty",url:t,requestBodyValues:n,statusCode:e.status,responseHeaders:a,responseBody:void 0});try{let t=await e.arrayBuffer();return{responseHeaders:a,value:new Uint8Array(t)}}catch(r){throw new s.w({message:"Failed to read response as array buffer",url:t,requestBodyValues:n,statusCode:e.status,responseHeaders:a,responseBody:void 0,cause:r})}},{btoa:S,atob:N}=globalThis;function O(e){let t=N(e.replace(/-/g,"+").replace(/_/g,"/"));return Uint8Array.from(t,e=>e.codePointAt(0))}function A(e){let t="";for(let n=0;n<e.length;n++)t+=String.fromCodePoint(e[n]);return S(t)}function j(e){return null==e?void 0:e.replace(/\/$/,"")}},1760:(e,t,n)=>{n.d(t,{AX:()=>_,A_:()=>ed,Gz:()=>ei,JJ:()=>W,Mt:()=>$,ON:()=>et,RU:()=>O,Rt:()=>F,e$:()=>C,g8:()=>I,tZ:()=>B,w:()=>k,xU:()=>V});var s,a,r,i,o,l,u,d,c,p,h,m,f="vercel.ai.error",g=Symbol.for(f),y=class e extends Error{constructor({name:e,message:t,cause:n}){super(t),this[s]=!0,this.name=e,this.cause=n}static isInstance(t){return e.hasMarker(t,f)}static hasMarker(e,t){let n=Symbol.for(t);return null!=e&&"object"==typeof e&&n in e&&"boolean"==typeof e[n]&&!0===e[n]}};s=g;var _=y,v="AI_APICallError",b=`vercel.ai.error.${v}`,x=Symbol.for(b),k=class extends _{constructor({message:e,url:t,requestBodyValues:n,statusCode:s,responseHeaders:r,responseBody:i,cause:o,isRetryable:l=null!=s&&(408===s||409===s||429===s||s>=500),data:u}){super({name:v,message:e,cause:o}),this[a]=!0,this.url=t,this.requestBodyValues=n,this.statusCode=s,this.responseHeaders=r,this.responseBody=i,this.isRetryable=l,this.data=u}static isInstance(e){return _.hasMarker(e,b)}};a=x;var w="AI_EmptyResponseBodyError",R=`vercel.ai.error.${w}`,T=Symbol.for(R),I=class extends _{constructor({message:e="Empty response body"}={}){super({name:w,message:e}),this[r]=!0}static isInstance(e){return _.hasMarker(e,R)}};function C(e){return null==e?"unknown error":"string"==typeof e?e:e instanceof Error?e.message:JSON.stringify(e)}r=T;var Z="AI_InvalidArgumentError",S=`vercel.ai.error.${Z}`,N=Symbol.for(S),O=class extends _{constructor({message:e,cause:t,argument:n}){super({name:Z,message:e,cause:t}),this[i]=!0,this.argument=n}static isInstance(e){return _.hasMarker(e,S)}};i=N;var A="AI_InvalidPromptError",j=`vercel.ai.error.${A}`,E=Symbol.for(j),$=class extends _{constructor({prompt:e,message:t,cause:n}){super({name:A,message:`Invalid prompt: ${t}`,cause:n}),this[o]=!0,this.prompt=e}static isInstance(e){return _.hasMarker(e,j)}};o=E;var M="AI_InvalidResponseDataError",P=`vercel.ai.error.${M}`,q=Symbol.for(P),F=class extends _{constructor({data:e,message:t=`Invalid response data: ${JSON.stringify(e)}.`}){super({name:M,message:t}),this[l]=!0,this.data=e}static isInstance(e){return _.hasMarker(e,P)}};l=q;var U="AI_JSONParseError",D=`vercel.ai.error.${U}`,L=Symbol.for(D),V=class extends _{constructor({text:e,cause:t}){super({name:U,message:`JSON parsing failed: Text: ${e}.
Error message: ${C(t)}`,cause:t}),this[u]=!0,this.text=e}static isInstance(e){return _.hasMarker(e,D)}};u=L;var z="AI_LoadAPIKeyError",K=`vercel.ai.error.${z}`,X=Symbol.for(K),B=class extends _{constructor({message:e}){super({name:z,message:e}),this[d]=!0}static isInstance(e){return _.hasMarker(e,K)}};d=X,Symbol.for("vercel.ai.error.AI_LoadSettingError"),Symbol.for("vercel.ai.error.AI_NoContentGeneratedError");var G="AI_NoSuchModelError",H=`vercel.ai.error.${G}`,J=Symbol.for(H),W=class extends _{constructor({errorName:e=G,modelId:t,modelType:n,message:s=`No such ${n}: ${t}`}){super({name:e,message:s}),this[c]=!0,this.modelId=t,this.modelType=n}static isInstance(e){return _.hasMarker(e,H)}};c=J;var Y="AI_TooManyEmbeddingValuesForCallError",Q=`vercel.ai.error.${Y}`,ee=Symbol.for(Q),et=class extends _{constructor(e){super({name:Y,message:`Too many values for a single embedding call. The ${e.provider} model "${e.modelId}" can only embed up to ${e.maxEmbeddingsPerCall} values per call, but ${e.values.length} values were provided.`}),this[p]=!0,this.provider=e.provider,this.modelId=e.modelId,this.maxEmbeddingsPerCall=e.maxEmbeddingsPerCall,this.values=e.values}static isInstance(e){return _.hasMarker(e,Q)}};p=ee;var en="AI_TypeValidationError",es=`vercel.ai.error.${en}`,ea=Symbol.for(es),er=class e extends _{constructor({value:e,cause:t}){super({name:en,message:`Type validation failed: Value: ${JSON.stringify(e)}.
Error message: ${C(t)}`,cause:t}),this[h]=!0,this.value=e}static isInstance(e){return _.hasMarker(e,es)}static wrap({value:t,cause:n}){return e.isInstance(n)&&n.value===t?n:new e({value:t,cause:n})}};h=ea;var ei=er,eo="AI_UnsupportedFunctionalityError",el=`vercel.ai.error.${eo}`,eu=Symbol.for(el),ed=class extends _{constructor({functionality:e,message:t=`'${e}' functionality not supported.`}){super({name:eo,message:t}),this[m]=!0,this.functionality=e}static isInstance(e){return _.hasMarker(e,el)}};function ec(e){return null===e||"string"==typeof e||"number"==typeof e||"boolean"==typeof e||(Array.isArray(e)?e.every(ec):"object"==typeof e&&Object.entries(e).every(([e,t])=>"string"==typeof e&&ec(t)))}m=eu},1067:(e,t,n)=>{var s,a,r,i;let o;n.d(t,{pA:()=>i,Yj:()=>e$,IX:()=>eP,O7:()=>ej,PG:()=>eS,VK:()=>eU,Km:()=>eK,Pp:()=>eN,Vo:()=>eV,i0:()=>ez,lB:()=>eE,Rx:()=>eA,Ry:()=>eq,jt:()=>eX,IM:()=>eL,Z_:()=>eO,bc:()=>eD,G0:()=>eF,_4:()=>eM}),function(e){e.assertEqual=e=>{},e.assertIs=function(e){},e.assertNever=function(e){throw Error()},e.arrayToEnum=e=>{let t={};for(let n of e)t[n]=n;return t},e.getValidEnumValues=t=>{let n=e.objectKeys(t).filter(e=>"number"!=typeof t[t[e]]),s={};for(let e of n)s[e]=t[e];return e.objectValues(s)},e.objectValues=t=>e.objectKeys(t).map(function(e){return t[e]}),e.objectKeys="function"==typeof Object.keys?e=>Object.keys(e):e=>{let t=[];for(let n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.push(n);return t},e.find=(e,t)=>{for(let n of e)if(t(n))return n},e.isInteger="function"==typeof Number.isInteger?e=>Number.isInteger(e):e=>"number"==typeof e&&Number.isFinite(e)&&Math.floor(e)===e,e.joinValues=function(e,t=" | "){return e.map(e=>"string"==typeof e?`'${e}'`:e).join(t)},e.jsonStringifyReplacer=(e,t)=>"bigint"==typeof t?t.toString():t}(s||(s={})),(a||(a={})).mergeShapes=(e,t)=>({...e,...t});let l=s.arrayToEnum(["string","nan","number","integer","float","boolean","date","bigint","symbol","function","undefined","null","array","object","unknown","promise","void","never","map","set"]),u=e=>{switch(typeof e){case"undefined":return l.undefined;case"string":return l.string;case"number":return Number.isNaN(e)?l.nan:l.number;case"boolean":return l.boolean;case"function":return l.function;case"bigint":return l.bigint;case"symbol":return l.symbol;case"object":if(Array.isArray(e))return l.array;if(null===e)return l.null;if(e.then&&"function"==typeof e.then&&e.catch&&"function"==typeof e.catch)return l.promise;if("undefined"!=typeof Map&&e instanceof Map)return l.map;if("undefined"!=typeof Set&&e instanceof Set)return l.set;if("undefined"!=typeof Date&&e instanceof Date)return l.date;return l.object;default:return l.unknown}},d=s.arrayToEnum(["invalid_type","invalid_literal","custom","invalid_union","invalid_union_discriminator","invalid_enum_value","unrecognized_keys","invalid_arguments","invalid_return_type","invalid_date","invalid_string","too_small","too_big","invalid_intersection_types","not_multiple_of","not_finite"]);class c extends Error{get errors(){return this.issues}constructor(e){super(),this.issues=[],this.addIssue=e=>{this.issues=[...this.issues,e]},this.addIssues=(e=[])=>{this.issues=[...this.issues,...e]};let t=new.target.prototype;Object.setPrototypeOf?Object.setPrototypeOf(this,t):this.__proto__=t,this.name="ZodError",this.issues=e}format(e){let t=e||function(e){return e.message},n={_errors:[]},s=e=>{for(let a of e.issues)if("invalid_union"===a.code)a.unionErrors.map(s);else if("invalid_return_type"===a.code)s(a.returnTypeError);else if("invalid_arguments"===a.code)s(a.argumentsError);else if(0===a.path.length)n._errors.push(t(a));else{let e=n,s=0;for(;s<a.path.length;){let n=a.path[s];s===a.path.length-1?(e[n]=e[n]||{_errors:[]},e[n]._errors.push(t(a))):e[n]=e[n]||{_errors:[]},e=e[n],s++}}};return s(this),n}static assert(e){if(!(e instanceof c))throw Error(`Not a ZodError: ${e}`)}toString(){return this.message}get message(){return JSON.stringify(this.issues,s.jsonStringifyReplacer,2)}get isEmpty(){return 0===this.issues.length}flatten(e=e=>e.message){let t={},n=[];for(let s of this.issues)if(s.path.length>0){let n=s.path[0];t[n]=t[n]||[],t[n].push(e(s))}else n.push(e(s));return{formErrors:n,fieldErrors:t}}get formErrors(){return this.flatten()}}c.create=e=>new c(e);let p=(e,t)=>{let n;switch(e.code){case d.invalid_type:n=e.received===l.undefined?"Required":`Expected ${e.expected}, received ${e.received}`;break;case d.invalid_literal:n=`Invalid literal value, expected ${JSON.stringify(e.expected,s.jsonStringifyReplacer)}`;break;case d.unrecognized_keys:n=`Unrecognized key(s) in object: ${s.joinValues(e.keys,", ")}`;break;case d.invalid_union:n="Invalid input";break;case d.invalid_union_discriminator:n=`Invalid discriminator value. Expected ${s.joinValues(e.options)}`;break;case d.invalid_enum_value:n=`Invalid enum value. Expected ${s.joinValues(e.options)}, received '${e.received}'`;break;case d.invalid_arguments:n="Invalid function arguments";break;case d.invalid_return_type:n="Invalid function return type";break;case d.invalid_date:n="Invalid date";break;case d.invalid_string:"object"==typeof e.validation?"includes"in e.validation?(n=`Invalid input: must include "${e.validation.includes}"`,"number"==typeof e.validation.position&&(n=`${n} at one or more positions greater than or equal to ${e.validation.position}`)):"startsWith"in e.validation?n=`Invalid input: must start with "${e.validation.startsWith}"`:"endsWith"in e.validation?n=`Invalid input: must end with "${e.validation.endsWith}"`:s.assertNever(e.validation):n="regex"!==e.validation?`Invalid ${e.validation}`:"Invalid";break;case d.too_small:n="array"===e.type?`Array must contain ${e.exact?"exactly":e.inclusive?"at least":"more than"} ${e.minimum} element(s)`:"string"===e.type?`String must contain ${e.exact?"exactly":e.inclusive?"at least":"over"} ${e.minimum} character(s)`:"number"===e.type?`Number must be ${e.exact?"exactly equal to ":e.inclusive?"greater than or equal to ":"greater than "}${e.minimum}`:"bigint"===e.type?`Number must be ${e.exact?"exactly equal to ":e.inclusive?"greater than or equal to ":"greater than "}${e.minimum}`:"date"===e.type?`Date must be ${e.exact?"exactly equal to ":e.inclusive?"greater than or equal to ":"greater than "}${new Date(Number(e.minimum))}`:"Invalid input";break;case d.too_big:n="array"===e.type?`Array must contain ${e.exact?"exactly":e.inclusive?"at most":"less than"} ${e.maximum} element(s)`:"string"===e.type?`String must contain ${e.exact?"exactly":e.inclusive?"at most":"under"} ${e.maximum} character(s)`:"number"===e.type?`Number must be ${e.exact?"exactly":e.inclusive?"less than or equal to":"less than"} ${e.maximum}`:"bigint"===e.type?`BigInt must be ${e.exact?"exactly":e.inclusive?"less than or equal to":"less than"} ${e.maximum}`:"date"===e.type?`Date must be ${e.exact?"exactly":e.inclusive?"smaller than or equal to":"smaller than"} ${new Date(Number(e.maximum))}`:"Invalid input";break;case d.custom:n="Invalid input";break;case d.invalid_intersection_types:n="Intersection results could not be merged";break;case d.not_multiple_of:n=`Number must be a multiple of ${e.multipleOf}`;break;case d.not_finite:n="Number must be finite";break;default:n=t.defaultError,s.assertNever(e)}return{message:n}};!function(e){e.errToObj=e=>"string"==typeof e?{message:e}:e||{},e.toString=e=>"string"==typeof e?e:e?.message}(r||(r={}));let h=e=>{let{data:t,path:n,errorMaps:s,issueData:a}=e,r=[...n,...a.path||[]],i={...a,path:r};if(void 0!==a.message)return{...a,path:r,message:a.message};let o="";for(let e of s.filter(e=>!!e).slice().reverse())o=e(i,{data:t,defaultError:o}).message;return{...a,path:r,message:o}};function m(e,t){let n=h({issueData:t,data:e.data,path:e.path,errorMaps:[e.common.contextualErrorMap,e.schemaErrorMap,p,p==p?void 0:p].filter(e=>!!e)});e.common.issues.push(n)}class f{constructor(){this.value="valid"}dirty(){"valid"===this.value&&(this.value="dirty")}abort(){"aborted"!==this.value&&(this.value="aborted")}static mergeArray(e,t){let n=[];for(let s of t){if("aborted"===s.status)return g;"dirty"===s.status&&e.dirty(),n.push(s.value)}return{status:e.value,value:n}}static async mergeObjectAsync(e,t){let n=[];for(let e of t){let t=await e.key,s=await e.value;n.push({key:t,value:s})}return f.mergeObjectSync(e,n)}static mergeObjectSync(e,t){let n={};for(let s of t){let{key:t,value:a}=s;if("aborted"===t.status||"aborted"===a.status)return g;"dirty"===t.status&&e.dirty(),"dirty"===a.status&&e.dirty(),"__proto__"!==t.value&&(void 0!==a.value||s.alwaysSet)&&(n[t.value]=a.value)}return{status:e.value,value:n}}}let g=Object.freeze({status:"aborted"}),y=e=>({status:"dirty",value:e}),_=e=>({status:"valid",value:e}),v=e=>"aborted"===e.status,b=e=>"dirty"===e.status,x=e=>"valid"===e.status,k=e=>"undefined"!=typeof Promise&&e instanceof Promise;class w{constructor(e,t,n,s){this._cachedPath=[],this.parent=e,this.data=t,this._path=n,this._key=s}get path(){return this._cachedPath.length||(Array.isArray(this._key)?this._cachedPath.push(...this._path,...this._key):this._cachedPath.push(...this._path,this._key)),this._cachedPath}}let R=(e,t)=>{if(x(t))return{success:!0,data:t.value};if(!e.common.issues.length)throw Error("Validation failed but no issues detected.");return{success:!1,get error(){if(this._error)return this._error;let t=new c(e.common.issues);return this._error=t,this._error}}};function T(e){if(!e)return{};let{errorMap:t,invalid_type_error:n,required_error:s,description:a}=e;if(t&&(n||s))throw Error('Can\'t use "invalid_type_error" or "required_error" in conjunction with custom error map.');return t?{errorMap:t,description:a}:{errorMap:(t,a)=>{let{message:r}=e;return"invalid_enum_value"===t.code?{message:r??a.defaultError}:void 0===a.data?{message:r??s??a.defaultError}:"invalid_type"!==t.code?{message:a.defaultError}:{message:r??n??a.defaultError}},description:a}}class I{get description(){return this._def.description}_getType(e){return u(e.data)}_getOrReturnCtx(e,t){return t||{common:e.parent.common,data:e.data,parsedType:u(e.data),schemaErrorMap:this._def.errorMap,path:e.path,parent:e.parent}}_processInputParams(e){return{status:new f,ctx:{common:e.parent.common,data:e.data,parsedType:u(e.data),schemaErrorMap:this._def.errorMap,path:e.path,parent:e.parent}}}_parseSync(e){let t=this._parse(e);if(k(t))throw Error("Synchronous parse encountered promise.");return t}_parseAsync(e){return Promise.resolve(this._parse(e))}parse(e,t){let n=this.safeParse(e,t);if(n.success)return n.data;throw n.error}safeParse(e,t){let n={common:{issues:[],async:t?.async??!1,contextualErrorMap:t?.errorMap},path:t?.path||[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:u(e)},s=this._parseSync({data:e,path:n.path,parent:n});return R(n,s)}"~validate"(e){let t={common:{issues:[],async:!!this["~standard"].async},path:[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:u(e)};if(!this["~standard"].async)try{let n=this._parseSync({data:e,path:[],parent:t});return x(n)?{value:n.value}:{issues:t.common.issues}}catch(e){e?.message?.toLowerCase()?.includes("encountered")&&(this["~standard"].async=!0),t.common={issues:[],async:!0}}return this._parseAsync({data:e,path:[],parent:t}).then(e=>x(e)?{value:e.value}:{issues:t.common.issues})}async parseAsync(e,t){let n=await this.safeParseAsync(e,t);if(n.success)return n.data;throw n.error}async safeParseAsync(e,t){let n={common:{issues:[],contextualErrorMap:t?.errorMap,async:!0},path:t?.path||[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:u(e)},s=this._parse({data:e,path:n.path,parent:n});return R(n,await (k(s)?s:Promise.resolve(s)))}refine(e,t){let n=e=>"string"==typeof t||void 0===t?{message:t}:"function"==typeof t?t(e):t;return this._refinement((t,s)=>{let a=e(t),r=()=>s.addIssue({code:d.custom,...n(t)});return"undefined"!=typeof Promise&&a instanceof Promise?a.then(e=>!!e||(r(),!1)):!!a||(r(),!1)})}refinement(e,t){return this._refinement((n,s)=>!!e(n)||(s.addIssue("function"==typeof t?t(n,s):t),!1))}_refinement(e){return new ev({schema:this,typeName:i.ZodEffects,effect:{type:"refinement",refinement:e}})}superRefine(e){return this._refinement(e)}constructor(e){this.spa=this.safeParseAsync,this._def=e,this.parse=this.parse.bind(this),this.safeParse=this.safeParse.bind(this),this.parseAsync=this.parseAsync.bind(this),this.safeParseAsync=this.safeParseAsync.bind(this),this.spa=this.spa.bind(this),this.refine=this.refine.bind(this),this.refinement=this.refinement.bind(this),this.superRefine=this.superRefine.bind(this),this.optional=this.optional.bind(this),this.nullable=this.nullable.bind(this),this.nullish=this.nullish.bind(this),this.array=this.array.bind(this),this.promise=this.promise.bind(this),this.or=this.or.bind(this),this.and=this.and.bind(this),this.transform=this.transform.bind(this),this.brand=this.brand.bind(this),this.default=this.default.bind(this),this.catch=this.catch.bind(this),this.describe=this.describe.bind(this),this.pipe=this.pipe.bind(this),this.readonly=this.readonly.bind(this),this.isNullable=this.isNullable.bind(this),this.isOptional=this.isOptional.bind(this),this["~standard"]={version:1,vendor:"zod",validate:e=>this["~validate"](e)}}optional(){return eb.create(this,this._def)}nullable(){return ex.create(this,this._def)}nullish(){return this.nullable().optional()}array(){return en.create(this)}promise(){return e_.create(this,this._def)}or(e){return ea.create([this,e],this._def)}and(e){return eo.create(this,e,this._def)}transform(e){return new ev({...T(this._def),schema:this,typeName:i.ZodEffects,effect:{type:"transform",transform:e}})}default(e){return new ek({...T(this._def),innerType:this,defaultValue:"function"==typeof e?e:()=>e,typeName:i.ZodDefault})}brand(){return new eT({typeName:i.ZodBranded,type:this,...T(this._def)})}catch(e){return new ew({...T(this._def),innerType:this,catchValue:"function"==typeof e?e:()=>e,typeName:i.ZodCatch})}describe(e){return new this.constructor({...this._def,description:e})}pipe(e){return eI.create(this,e)}readonly(){return eC.create(this)}isOptional(){return this.safeParse(void 0).success}isNullable(){return this.safeParse(null).success}}let C=/^c[^\s-]{8,}$/i,Z=/^[0-9a-z]+$/,S=/^[0-9A-HJKMNP-TV-Z]{26}$/i,N=/^[0-9a-fA-F]{8}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{12}$/i,O=/^[a-z0-9_-]{21}$/i,A=/^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]*$/,j=/^[-+]?P(?!$)(?:(?:[-+]?\d+Y)|(?:[-+]?\d+[.,]\d+Y$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:(?:[-+]?\d+W)|(?:[-+]?\d+[.,]\d+W$))?(?:(?:[-+]?\d+D)|(?:[-+]?\d+[.,]\d+D$))?(?:T(?=[\d+-])(?:(?:[-+]?\d+H)|(?:[-+]?\d+[.,]\d+H$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:[-+]?\d+(?:[.,]\d+)?S)?)??$/,E=/^(?!\.)(?!.*\.\.)([A-Z0-9_'+\-\.]*)[A-Z0-9_+-]@([A-Z0-9][A-Z0-9\-]*\.)+[A-Z]{2,}$/i,$=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/,M=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\/(3[0-2]|[12]?[0-9])$/,P=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))$/,q=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/,F=/^([0-9a-zA-Z+/]{4})*(([0-9a-zA-Z+/]{2}==)|([0-9a-zA-Z+/]{3}=))?$/,U=/^([0-9a-zA-Z-_]{4})*(([0-9a-zA-Z-_]{2}(==)?)|([0-9a-zA-Z-_]{3}(=)?))?$/,D="((\\d\\d[2468][048]|\\d\\d[13579][26]|\\d\\d0[48]|[02468][048]00|[13579][26]00)-02-29|\\d{4}-((0[13578]|1[02])-(0[1-9]|[12]\\d|3[01])|(0[469]|11)-(0[1-9]|[12]\\d|30)|(02)-(0[1-9]|1\\d|2[0-8])))",L=RegExp(`^${D}$`);function V(e){let t="[0-5]\\d";e.precision?t=`${t}\\.\\d{${e.precision}}`:null==e.precision&&(t=`${t}(\\.\\d+)?`);let n=e.precision?"+":"?";return`([01]\\d|2[0-3]):[0-5]\\d(:${t})${n}`}class z extends I{_parse(e){var t,n,a,r;let i;if(this._def.coerce&&(e.data=String(e.data)),this._getType(e)!==l.string){let t=this._getOrReturnCtx(e);return m(t,{code:d.invalid_type,expected:l.string,received:t.parsedType}),g}let u=new f;for(let l of this._def.checks)if("min"===l.kind)e.data.length<l.value&&(m(i=this._getOrReturnCtx(e,i),{code:d.too_small,minimum:l.value,type:"string",inclusive:!0,exact:!1,message:l.message}),u.dirty());else if("max"===l.kind)e.data.length>l.value&&(m(i=this._getOrReturnCtx(e,i),{code:d.too_big,maximum:l.value,type:"string",inclusive:!0,exact:!1,message:l.message}),u.dirty());else if("length"===l.kind){let t=e.data.length>l.value,n=e.data.length<l.value;(t||n)&&(i=this._getOrReturnCtx(e,i),t?m(i,{code:d.too_big,maximum:l.value,type:"string",inclusive:!0,exact:!0,message:l.message}):n&&m(i,{code:d.too_small,minimum:l.value,type:"string",inclusive:!0,exact:!0,message:l.message}),u.dirty())}else if("email"===l.kind)E.test(e.data)||(m(i=this._getOrReturnCtx(e,i),{validation:"email",code:d.invalid_string,message:l.message}),u.dirty());else if("emoji"===l.kind)o||(o=RegExp("^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$","u")),o.test(e.data)||(m(i=this._getOrReturnCtx(e,i),{validation:"emoji",code:d.invalid_string,message:l.message}),u.dirty());else if("uuid"===l.kind)N.test(e.data)||(m(i=this._getOrReturnCtx(e,i),{validation:"uuid",code:d.invalid_string,message:l.message}),u.dirty());else if("nanoid"===l.kind)O.test(e.data)||(m(i=this._getOrReturnCtx(e,i),{validation:"nanoid",code:d.invalid_string,message:l.message}),u.dirty());else if("cuid"===l.kind)C.test(e.data)||(m(i=this._getOrReturnCtx(e,i),{validation:"cuid",code:d.invalid_string,message:l.message}),u.dirty());else if("cuid2"===l.kind)Z.test(e.data)||(m(i=this._getOrReturnCtx(e,i),{validation:"cuid2",code:d.invalid_string,message:l.message}),u.dirty());else if("ulid"===l.kind)S.test(e.data)||(m(i=this._getOrReturnCtx(e,i),{validation:"ulid",code:d.invalid_string,message:l.message}),u.dirty());else if("url"===l.kind)try{new URL(e.data)}catch{m(i=this._getOrReturnCtx(e,i),{validation:"url",code:d.invalid_string,message:l.message}),u.dirty()}else"regex"===l.kind?(l.regex.lastIndex=0,l.regex.test(e.data)||(m(i=this._getOrReturnCtx(e,i),{validation:"regex",code:d.invalid_string,message:l.message}),u.dirty())):"trim"===l.kind?e.data=e.data.trim():"includes"===l.kind?e.data.includes(l.value,l.position)||(m(i=this._getOrReturnCtx(e,i),{code:d.invalid_string,validation:{includes:l.value,position:l.position},message:l.message}),u.dirty()):"toLowerCase"===l.kind?e.data=e.data.toLowerCase():"toUpperCase"===l.kind?e.data=e.data.toUpperCase():"startsWith"===l.kind?e.data.startsWith(l.value)||(m(i=this._getOrReturnCtx(e,i),{code:d.invalid_string,validation:{startsWith:l.value},message:l.message}),u.dirty()):"endsWith"===l.kind?e.data.endsWith(l.value)||(m(i=this._getOrReturnCtx(e,i),{code:d.invalid_string,validation:{endsWith:l.value},message:l.message}),u.dirty()):"datetime"===l.kind?(function(e){let t=`${D}T${V(e)}`,n=[];return n.push(e.local?"Z?":"Z"),e.offset&&n.push("([+-]\\d{2}:?\\d{2})"),t=`${t}(${n.join("|")})`,RegExp(`^${t}$`)})(l).test(e.data)||(m(i=this._getOrReturnCtx(e,i),{code:d.invalid_string,validation:"datetime",message:l.message}),u.dirty()):"date"===l.kind?L.test(e.data)||(m(i=this._getOrReturnCtx(e,i),{code:d.invalid_string,validation:"date",message:l.message}),u.dirty()):"time"===l.kind?RegExp(`^${V(l)}$`).test(e.data)||(m(i=this._getOrReturnCtx(e,i),{code:d.invalid_string,validation:"time",message:l.message}),u.dirty()):"duration"===l.kind?j.test(e.data)||(m(i=this._getOrReturnCtx(e,i),{validation:"duration",code:d.invalid_string,message:l.message}),u.dirty()):"ip"===l.kind?(t=e.data,("v4"===(n=l.version)||!n)&&$.test(t)||("v6"===n||!n)&&P.test(t)||(m(i=this._getOrReturnCtx(e,i),{validation:"ip",code:d.invalid_string,message:l.message}),u.dirty())):"jwt"===l.kind?!function(e,t){if(!A.test(e))return!1;try{let[n]=e.split(".");if(!n)return!1;let s=n.replace(/-/g,"+").replace(/_/g,"/").padEnd(n.length+(4-n.length%4)%4,"="),a=JSON.parse(atob(s));if("object"!=typeof a||null===a||"typ"in a&&a?.typ!=="JWT"||!a.alg||t&&a.alg!==t)return!1;return!0}catch{return!1}}(e.data,l.alg)&&(m(i=this._getOrReturnCtx(e,i),{validation:"jwt",code:d.invalid_string,message:l.message}),u.dirty()):"cidr"===l.kind?(a=e.data,("v4"===(r=l.version)||!r)&&M.test(a)||("v6"===r||!r)&&q.test(a)||(m(i=this._getOrReturnCtx(e,i),{validation:"cidr",code:d.invalid_string,message:l.message}),u.dirty())):"base64"===l.kind?F.test(e.data)||(m(i=this._getOrReturnCtx(e,i),{validation:"base64",code:d.invalid_string,message:l.message}),u.dirty()):"base64url"===l.kind?U.test(e.data)||(m(i=this._getOrReturnCtx(e,i),{validation:"base64url",code:d.invalid_string,message:l.message}),u.dirty()):s.assertNever(l);return{status:u.value,value:e.data}}_regex(e,t,n){return this.refinement(t=>e.test(t),{validation:t,code:d.invalid_string,...r.errToObj(n)})}_addCheck(e){return new z({...this._def,checks:[...this._def.checks,e]})}email(e){return this._addCheck({kind:"email",...r.errToObj(e)})}url(e){return this._addCheck({kind:"url",...r.errToObj(e)})}emoji(e){return this._addCheck({kind:"emoji",...r.errToObj(e)})}uuid(e){return this._addCheck({kind:"uuid",...r.errToObj(e)})}nanoid(e){return this._addCheck({kind:"nanoid",...r.errToObj(e)})}cuid(e){return this._addCheck({kind:"cuid",...r.errToObj(e)})}cuid2(e){return this._addCheck({kind:"cuid2",...r.errToObj(e)})}ulid(e){return this._addCheck({kind:"ulid",...r.errToObj(e)})}base64(e){return this._addCheck({kind:"base64",...r.errToObj(e)})}base64url(e){return this._addCheck({kind:"base64url",...r.errToObj(e)})}jwt(e){return this._addCheck({kind:"jwt",...r.errToObj(e)})}ip(e){return this._addCheck({kind:"ip",...r.errToObj(e)})}cidr(e){return this._addCheck({kind:"cidr",...r.errToObj(e)})}datetime(e){return"string"==typeof e?this._addCheck({kind:"datetime",precision:null,offset:!1,local:!1,message:e}):this._addCheck({kind:"datetime",precision:void 0===e?.precision?null:e?.precision,offset:e?.offset??!1,local:e?.local??!1,...r.errToObj(e?.message)})}date(e){return this._addCheck({kind:"date",message:e})}time(e){return"string"==typeof e?this._addCheck({kind:"time",precision:null,message:e}):this._addCheck({kind:"time",precision:void 0===e?.precision?null:e?.precision,...r.errToObj(e?.message)})}duration(e){return this._addCheck({kind:"duration",...r.errToObj(e)})}regex(e,t){return this._addCheck({kind:"regex",regex:e,...r.errToObj(t)})}includes(e,t){return this._addCheck({kind:"includes",value:e,position:t?.position,...r.errToObj(t?.message)})}startsWith(e,t){return this._addCheck({kind:"startsWith",value:e,...r.errToObj(t)})}endsWith(e,t){return this._addCheck({kind:"endsWith",value:e,...r.errToObj(t)})}min(e,t){return this._addCheck({kind:"min",value:e,...r.errToObj(t)})}max(e,t){return this._addCheck({kind:"max",value:e,...r.errToObj(t)})}length(e,t){return this._addCheck({kind:"length",value:e,...r.errToObj(t)})}nonempty(e){return this.min(1,r.errToObj(e))}trim(){return new z({...this._def,checks:[...this._def.checks,{kind:"trim"}]})}toLowerCase(){return new z({...this._def,checks:[...this._def.checks,{kind:"toLowerCase"}]})}toUpperCase(){return new z({...this._def,checks:[...this._def.checks,{kind:"toUpperCase"}]})}get isDatetime(){return!!this._def.checks.find(e=>"datetime"===e.kind)}get isDate(){return!!this._def.checks.find(e=>"date"===e.kind)}get isTime(){return!!this._def.checks.find(e=>"time"===e.kind)}get isDuration(){return!!this._def.checks.find(e=>"duration"===e.kind)}get isEmail(){return!!this._def.checks.find(e=>"email"===e.kind)}get isURL(){return!!this._def.checks.find(e=>"url"===e.kind)}get isEmoji(){return!!this._def.checks.find(e=>"emoji"===e.kind)}get isUUID(){return!!this._def.checks.find(e=>"uuid"===e.kind)}get isNANOID(){return!!this._def.checks.find(e=>"nanoid"===e.kind)}get isCUID(){return!!this._def.checks.find(e=>"cuid"===e.kind)}get isCUID2(){return!!this._def.checks.find(e=>"cuid2"===e.kind)}get isULID(){return!!this._def.checks.find(e=>"ulid"===e.kind)}get isIP(){return!!this._def.checks.find(e=>"ip"===e.kind)}get isCIDR(){return!!this._def.checks.find(e=>"cidr"===e.kind)}get isBase64(){return!!this._def.checks.find(e=>"base64"===e.kind)}get isBase64url(){return!!this._def.checks.find(e=>"base64url"===e.kind)}get minLength(){let e=null;for(let t of this._def.checks)"min"===t.kind&&(null===e||t.value>e)&&(e=t.value);return e}get maxLength(){let e=null;for(let t of this._def.checks)"max"===t.kind&&(null===e||t.value<e)&&(e=t.value);return e}}z.create=e=>new z({checks:[],typeName:i.ZodString,coerce:e?.coerce??!1,...T(e)});class K extends I{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte,this.step=this.multipleOf}_parse(e){let t;if(this._def.coerce&&(e.data=Number(e.data)),this._getType(e)!==l.number){let t=this._getOrReturnCtx(e);return m(t,{code:d.invalid_type,expected:l.number,received:t.parsedType}),g}let n=new f;for(let a of this._def.checks)"int"===a.kind?s.isInteger(e.data)||(m(t=this._getOrReturnCtx(e,t),{code:d.invalid_type,expected:"integer",received:"float",message:a.message}),n.dirty()):"min"===a.kind?(a.inclusive?e.data<a.value:e.data<=a.value)&&(m(t=this._getOrReturnCtx(e,t),{code:d.too_small,minimum:a.value,type:"number",inclusive:a.inclusive,exact:!1,message:a.message}),n.dirty()):"max"===a.kind?(a.inclusive?e.data>a.value:e.data>=a.value)&&(m(t=this._getOrReturnCtx(e,t),{code:d.too_big,maximum:a.value,type:"number",inclusive:a.inclusive,exact:!1,message:a.message}),n.dirty()):"multipleOf"===a.kind?0!==function(e,t){let n=(e.toString().split(".")[1]||"").length,s=(t.toString().split(".")[1]||"").length,a=n>s?n:s;return Number.parseInt(e.toFixed(a).replace(".",""))%Number.parseInt(t.toFixed(a).replace(".",""))/10**a}(e.data,a.value)&&(m(t=this._getOrReturnCtx(e,t),{code:d.not_multiple_of,multipleOf:a.value,message:a.message}),n.dirty()):"finite"===a.kind?Number.isFinite(e.data)||(m(t=this._getOrReturnCtx(e,t),{code:d.not_finite,message:a.message}),n.dirty()):s.assertNever(a);return{status:n.value,value:e.data}}gte(e,t){return this.setLimit("min",e,!0,r.toString(t))}gt(e,t){return this.setLimit("min",e,!1,r.toString(t))}lte(e,t){return this.setLimit("max",e,!0,r.toString(t))}lt(e,t){return this.setLimit("max",e,!1,r.toString(t))}setLimit(e,t,n,s){return new K({...this._def,checks:[...this._def.checks,{kind:e,value:t,inclusive:n,message:r.toString(s)}]})}_addCheck(e){return new K({...this._def,checks:[...this._def.checks,e]})}int(e){return this._addCheck({kind:"int",message:r.toString(e)})}positive(e){return this._addCheck({kind:"min",value:0,inclusive:!1,message:r.toString(e)})}negative(e){return this._addCheck({kind:"max",value:0,inclusive:!1,message:r.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:0,inclusive:!0,message:r.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:0,inclusive:!0,message:r.toString(e)})}multipleOf(e,t){return this._addCheck({kind:"multipleOf",value:e,message:r.toString(t)})}finite(e){return this._addCheck({kind:"finite",message:r.toString(e)})}safe(e){return this._addCheck({kind:"min",inclusive:!0,value:Number.MIN_SAFE_INTEGER,message:r.toString(e)})._addCheck({kind:"max",inclusive:!0,value:Number.MAX_SAFE_INTEGER,message:r.toString(e)})}get minValue(){let e=null;for(let t of this._def.checks)"min"===t.kind&&(null===e||t.value>e)&&(e=t.value);return e}get maxValue(){let e=null;for(let t of this._def.checks)"max"===t.kind&&(null===e||t.value<e)&&(e=t.value);return e}get isInt(){return!!this._def.checks.find(e=>"int"===e.kind||"multipleOf"===e.kind&&s.isInteger(e.value))}get isFinite(){let e=null,t=null;for(let n of this._def.checks){if("finite"===n.kind||"int"===n.kind||"multipleOf"===n.kind)return!0;"min"===n.kind?(null===t||n.value>t)&&(t=n.value):"max"===n.kind&&(null===e||n.value<e)&&(e=n.value)}return Number.isFinite(t)&&Number.isFinite(e)}}K.create=e=>new K({checks:[],typeName:i.ZodNumber,coerce:e?.coerce||!1,...T(e)});class X extends I{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte}_parse(e){let t;if(this._def.coerce)try{e.data=BigInt(e.data)}catch{return this._getInvalidInput(e)}if(this._getType(e)!==l.bigint)return this._getInvalidInput(e);let n=new f;for(let a of this._def.checks)"min"===a.kind?(a.inclusive?e.data<a.value:e.data<=a.value)&&(m(t=this._getOrReturnCtx(e,t),{code:d.too_small,type:"bigint",minimum:a.value,inclusive:a.inclusive,message:a.message}),n.dirty()):"max"===a.kind?(a.inclusive?e.data>a.value:e.data>=a.value)&&(m(t=this._getOrReturnCtx(e,t),{code:d.too_big,type:"bigint",maximum:a.value,inclusive:a.inclusive,message:a.message}),n.dirty()):"multipleOf"===a.kind?e.data%a.value!==BigInt(0)&&(m(t=this._getOrReturnCtx(e,t),{code:d.not_multiple_of,multipleOf:a.value,message:a.message}),n.dirty()):s.assertNever(a);return{status:n.value,value:e.data}}_getInvalidInput(e){let t=this._getOrReturnCtx(e);return m(t,{code:d.invalid_type,expected:l.bigint,received:t.parsedType}),g}gte(e,t){return this.setLimit("min",e,!0,r.toString(t))}gt(e,t){return this.setLimit("min",e,!1,r.toString(t))}lte(e,t){return this.setLimit("max",e,!0,r.toString(t))}lt(e,t){return this.setLimit("max",e,!1,r.toString(t))}setLimit(e,t,n,s){return new X({...this._def,checks:[...this._def.checks,{kind:e,value:t,inclusive:n,message:r.toString(s)}]})}_addCheck(e){return new X({...this._def,checks:[...this._def.checks,e]})}positive(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!1,message:r.toString(e)})}negative(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!1,message:r.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!0,message:r.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!0,message:r.toString(e)})}multipleOf(e,t){return this._addCheck({kind:"multipleOf",value:e,message:r.toString(t)})}get minValue(){let e=null;for(let t of this._def.checks)"min"===t.kind&&(null===e||t.value>e)&&(e=t.value);return e}get maxValue(){let e=null;for(let t of this._def.checks)"max"===t.kind&&(null===e||t.value<e)&&(e=t.value);return e}}X.create=e=>new X({checks:[],typeName:i.ZodBigInt,coerce:e?.coerce??!1,...T(e)});class B extends I{_parse(e){if(this._def.coerce&&(e.data=!!e.data),this._getType(e)!==l.boolean){let t=this._getOrReturnCtx(e);return m(t,{code:d.invalid_type,expected:l.boolean,received:t.parsedType}),g}return _(e.data)}}B.create=e=>new B({typeName:i.ZodBoolean,coerce:e?.coerce||!1,...T(e)});class G extends I{_parse(e){let t;if(this._def.coerce&&(e.data=new Date(e.data)),this._getType(e)!==l.date){let t=this._getOrReturnCtx(e);return m(t,{code:d.invalid_type,expected:l.date,received:t.parsedType}),g}if(Number.isNaN(e.data.getTime()))return m(this._getOrReturnCtx(e),{code:d.invalid_date}),g;let n=new f;for(let a of this._def.checks)"min"===a.kind?e.data.getTime()<a.value&&(m(t=this._getOrReturnCtx(e,t),{code:d.too_small,message:a.message,inclusive:!0,exact:!1,minimum:a.value,type:"date"}),n.dirty()):"max"===a.kind?e.data.getTime()>a.value&&(m(t=this._getOrReturnCtx(e,t),{code:d.too_big,message:a.message,inclusive:!0,exact:!1,maximum:a.value,type:"date"}),n.dirty()):s.assertNever(a);return{status:n.value,value:new Date(e.data.getTime())}}_addCheck(e){return new G({...this._def,checks:[...this._def.checks,e]})}min(e,t){return this._addCheck({kind:"min",value:e.getTime(),message:r.toString(t)})}max(e,t){return this._addCheck({kind:"max",value:e.getTime(),message:r.toString(t)})}get minDate(){let e=null;for(let t of this._def.checks)"min"===t.kind&&(null===e||t.value>e)&&(e=t.value);return null!=e?new Date(e):null}get maxDate(){let e=null;for(let t of this._def.checks)"max"===t.kind&&(null===e||t.value<e)&&(e=t.value);return null!=e?new Date(e):null}}G.create=e=>new G({checks:[],coerce:e?.coerce||!1,typeName:i.ZodDate,...T(e)});class H extends I{_parse(e){if(this._getType(e)!==l.symbol){let t=this._getOrReturnCtx(e);return m(t,{code:d.invalid_type,expected:l.symbol,received:t.parsedType}),g}return _(e.data)}}H.create=e=>new H({typeName:i.ZodSymbol,...T(e)});class J extends I{_parse(e){if(this._getType(e)!==l.undefined){let t=this._getOrReturnCtx(e);return m(t,{code:d.invalid_type,expected:l.undefined,received:t.parsedType}),g}return _(e.data)}}J.create=e=>new J({typeName:i.ZodUndefined,...T(e)});class W extends I{_parse(e){if(this._getType(e)!==l.null){let t=this._getOrReturnCtx(e);return m(t,{code:d.invalid_type,expected:l.null,received:t.parsedType}),g}return _(e.data)}}W.create=e=>new W({typeName:i.ZodNull,...T(e)});class Y extends I{constructor(){super(...arguments),this._any=!0}_parse(e){return _(e.data)}}Y.create=e=>new Y({typeName:i.ZodAny,...T(e)});class Q extends I{constructor(){super(...arguments),this._unknown=!0}_parse(e){return _(e.data)}}Q.create=e=>new Q({typeName:i.ZodUnknown,...T(e)});class ee extends I{_parse(e){let t=this._getOrReturnCtx(e);return m(t,{code:d.invalid_type,expected:l.never,received:t.parsedType}),g}}ee.create=e=>new ee({typeName:i.ZodNever,...T(e)});class et extends I{_parse(e){if(this._getType(e)!==l.undefined){let t=this._getOrReturnCtx(e);return m(t,{code:d.invalid_type,expected:l.void,received:t.parsedType}),g}return _(e.data)}}et.create=e=>new et({typeName:i.ZodVoid,...T(e)});class en extends I{_parse(e){let{ctx:t,status:n}=this._processInputParams(e),s=this._def;if(t.parsedType!==l.array)return m(t,{code:d.invalid_type,expected:l.array,received:t.parsedType}),g;if(null!==s.exactLength){let e=t.data.length>s.exactLength.value,a=t.data.length<s.exactLength.value;(e||a)&&(m(t,{code:e?d.too_big:d.too_small,minimum:a?s.exactLength.value:void 0,maximum:e?s.exactLength.value:void 0,type:"array",inclusive:!0,exact:!0,message:s.exactLength.message}),n.dirty())}if(null!==s.minLength&&t.data.length<s.minLength.value&&(m(t,{code:d.too_small,minimum:s.minLength.value,type:"array",inclusive:!0,exact:!1,message:s.minLength.message}),n.dirty()),null!==s.maxLength&&t.data.length>s.maxLength.value&&(m(t,{code:d.too_big,maximum:s.maxLength.value,type:"array",inclusive:!0,exact:!1,message:s.maxLength.message}),n.dirty()),t.common.async)return Promise.all([...t.data].map((e,n)=>s.type._parseAsync(new w(t,e,t.path,n)))).then(e=>f.mergeArray(n,e));let a=[...t.data].map((e,n)=>s.type._parseSync(new w(t,e,t.path,n)));return f.mergeArray(n,a)}get element(){return this._def.type}min(e,t){return new en({...this._def,minLength:{value:e,message:r.toString(t)}})}max(e,t){return new en({...this._def,maxLength:{value:e,message:r.toString(t)}})}length(e,t){return new en({...this._def,exactLength:{value:e,message:r.toString(t)}})}nonempty(e){return this.min(1,e)}}en.create=(e,t)=>new en({type:e,minLength:null,maxLength:null,exactLength:null,typeName:i.ZodArray,...T(t)});class es extends I{constructor(){super(...arguments),this._cached=null,this.nonstrict=this.passthrough,this.augment=this.extend}_getCached(){if(null!==this._cached)return this._cached;let e=this._def.shape(),t=s.objectKeys(e);return this._cached={shape:e,keys:t},this._cached}_parse(e){if(this._getType(e)!==l.object){let t=this._getOrReturnCtx(e);return m(t,{code:d.invalid_type,expected:l.object,received:t.parsedType}),g}let{status:t,ctx:n}=this._processInputParams(e),{shape:s,keys:a}=this._getCached(),r=[];if(!(this._def.catchall instanceof ee&&"strip"===this._def.unknownKeys))for(let e in n.data)a.includes(e)||r.push(e);let i=[];for(let e of a){let t=s[e],a=n.data[e];i.push({key:{status:"valid",value:e},value:t._parse(new w(n,a,n.path,e)),alwaysSet:e in n.data})}if(this._def.catchall instanceof ee){let e=this._def.unknownKeys;if("passthrough"===e)for(let e of r)i.push({key:{status:"valid",value:e},value:{status:"valid",value:n.data[e]}});else if("strict"===e)r.length>0&&(m(n,{code:d.unrecognized_keys,keys:r}),t.dirty());else if("strip"===e);else throw Error("Internal ZodObject error: invalid unknownKeys value.")}else{let e=this._def.catchall;for(let t of r){let s=n.data[t];i.push({key:{status:"valid",value:t},value:e._parse(new w(n,s,n.path,t)),alwaysSet:t in n.data})}}return n.common.async?Promise.resolve().then(async()=>{let e=[];for(let t of i){let n=await t.key,s=await t.value;e.push({key:n,value:s,alwaysSet:t.alwaysSet})}return e}).then(e=>f.mergeObjectSync(t,e)):f.mergeObjectSync(t,i)}get shape(){return this._def.shape()}strict(e){return r.errToObj,new es({...this._def,unknownKeys:"strict",...void 0!==e?{errorMap:(t,n)=>{let s=this._def.errorMap?.(t,n).message??n.defaultError;return"unrecognized_keys"===t.code?{message:r.errToObj(e).message??s}:{message:s}}}:{}})}strip(){return new es({...this._def,unknownKeys:"strip"})}passthrough(){return new es({...this._def,unknownKeys:"passthrough"})}extend(e){return new es({...this._def,shape:()=>({...this._def.shape(),...e})})}merge(e){return new es({unknownKeys:e._def.unknownKeys,catchall:e._def.catchall,shape:()=>({...this._def.shape(),...e._def.shape()}),typeName:i.ZodObject})}setKey(e,t){return this.augment({[e]:t})}catchall(e){return new es({...this._def,catchall:e})}pick(e){let t={};for(let n of s.objectKeys(e))e[n]&&this.shape[n]&&(t[n]=this.shape[n]);return new es({...this._def,shape:()=>t})}omit(e){let t={};for(let n of s.objectKeys(this.shape))e[n]||(t[n]=this.shape[n]);return new es({...this._def,shape:()=>t})}deepPartial(){return function e(t){if(t instanceof es){let n={};for(let s in t.shape){let a=t.shape[s];n[s]=eb.create(e(a))}return new es({...t._def,shape:()=>n})}return t instanceof en?new en({...t._def,type:e(t.element)}):t instanceof eb?eb.create(e(t.unwrap())):t instanceof ex?ex.create(e(t.unwrap())):t instanceof el?el.create(t.items.map(t=>e(t))):t}(this)}partial(e){let t={};for(let n of s.objectKeys(this.shape)){let s=this.shape[n];e&&!e[n]?t[n]=s:t[n]=s.optional()}return new es({...this._def,shape:()=>t})}required(e){let t={};for(let n of s.objectKeys(this.shape))if(e&&!e[n])t[n]=this.shape[n];else{let e=this.shape[n];for(;e instanceof eb;)e=e._def.innerType;t[n]=e}return new es({...this._def,shape:()=>t})}keyof(){return ef(s.objectKeys(this.shape))}}es.create=(e,t)=>new es({shape:()=>e,unknownKeys:"strip",catchall:ee.create(),typeName:i.ZodObject,...T(t)}),es.strictCreate=(e,t)=>new es({shape:()=>e,unknownKeys:"strict",catchall:ee.create(),typeName:i.ZodObject,...T(t)}),es.lazycreate=(e,t)=>new es({shape:e,unknownKeys:"strip",catchall:ee.create(),typeName:i.ZodObject,...T(t)});class ea extends I{_parse(e){let{ctx:t}=this._processInputParams(e),n=this._def.options;if(t.common.async)return Promise.all(n.map(async e=>{let n={...t,common:{...t.common,issues:[]},parent:null};return{result:await e._parseAsync({data:t.data,path:t.path,parent:n}),ctx:n}})).then(function(e){for(let t of e)if("valid"===t.result.status)return t.result;for(let n of e)if("dirty"===n.result.status)return t.common.issues.push(...n.ctx.common.issues),n.result;let n=e.map(e=>new c(e.ctx.common.issues));return m(t,{code:d.invalid_union,unionErrors:n}),g});{let e;let s=[];for(let a of n){let n={...t,common:{...t.common,issues:[]},parent:null},r=a._parseSync({data:t.data,path:t.path,parent:n});if("valid"===r.status)return r;"dirty"!==r.status||e||(e={result:r,ctx:n}),n.common.issues.length&&s.push(n.common.issues)}if(e)return t.common.issues.push(...e.ctx.common.issues),e.result;let a=s.map(e=>new c(e));return m(t,{code:d.invalid_union,unionErrors:a}),g}}get options(){return this._def.options}}ea.create=(e,t)=>new ea({options:e,typeName:i.ZodUnion,...T(t)});let er=e=>{if(e instanceof eh)return er(e.schema);if(e instanceof ev)return er(e.innerType());if(e instanceof em)return[e.value];if(e instanceof eg)return e.options;if(e instanceof ey)return s.objectValues(e.enum);if(e instanceof ek)return er(e._def.innerType);if(e instanceof J)return[void 0];else if(e instanceof W)return[null];else if(e instanceof eb)return[void 0,...er(e.unwrap())];else if(e instanceof ex)return[null,...er(e.unwrap())];else if(e instanceof eT)return er(e.unwrap());else if(e instanceof eC)return er(e.unwrap());else if(e instanceof ew)return er(e._def.innerType);else return[]};class ei extends I{_parse(e){let{ctx:t}=this._processInputParams(e);if(t.parsedType!==l.object)return m(t,{code:d.invalid_type,expected:l.object,received:t.parsedType}),g;let n=this.discriminator,s=t.data[n],a=this.optionsMap.get(s);return a?t.common.async?a._parseAsync({data:t.data,path:t.path,parent:t}):a._parseSync({data:t.data,path:t.path,parent:t}):(m(t,{code:d.invalid_union_discriminator,options:Array.from(this.optionsMap.keys()),path:[n]}),g)}get discriminator(){return this._def.discriminator}get options(){return this._def.options}get optionsMap(){return this._def.optionsMap}static create(e,t,n){let s=new Map;for(let n of t){let t=er(n.shape[e]);if(!t.length)throw Error(`A discriminator value for key \`${e}\` could not be extracted from all schema options`);for(let a of t){if(s.has(a))throw Error(`Discriminator property ${String(e)} has duplicate value ${String(a)}`);s.set(a,n)}}return new ei({typeName:i.ZodDiscriminatedUnion,discriminator:e,options:t,optionsMap:s,...T(n)})}}class eo extends I{_parse(e){let{status:t,ctx:n}=this._processInputParams(e),a=(e,a)=>{if(v(e)||v(a))return g;let r=function e(t,n){let a=u(t),r=u(n);if(t===n)return{valid:!0,data:t};if(a===l.object&&r===l.object){let a=s.objectKeys(n),r=s.objectKeys(t).filter(e=>-1!==a.indexOf(e)),i={...t,...n};for(let s of r){let a=e(t[s],n[s]);if(!a.valid)return{valid:!1};i[s]=a.data}return{valid:!0,data:i}}if(a===l.array&&r===l.array){if(t.length!==n.length)return{valid:!1};let s=[];for(let a=0;a<t.length;a++){let r=e(t[a],n[a]);if(!r.valid)return{valid:!1};s.push(r.data)}return{valid:!0,data:s}}return a===l.date&&r===l.date&&+t==+n?{valid:!0,data:t}:{valid:!1}}(e.value,a.value);return r.valid?((b(e)||b(a))&&t.dirty(),{status:t.value,value:r.data}):(m(n,{code:d.invalid_intersection_types}),g)};return n.common.async?Promise.all([this._def.left._parseAsync({data:n.data,path:n.path,parent:n}),this._def.right._parseAsync({data:n.data,path:n.path,parent:n})]).then(([e,t])=>a(e,t)):a(this._def.left._parseSync({data:n.data,path:n.path,parent:n}),this._def.right._parseSync({data:n.data,path:n.path,parent:n}))}}eo.create=(e,t,n)=>new eo({left:e,right:t,typeName:i.ZodIntersection,...T(n)});class el extends I{_parse(e){let{status:t,ctx:n}=this._processInputParams(e);if(n.parsedType!==l.array)return m(n,{code:d.invalid_type,expected:l.array,received:n.parsedType}),g;if(n.data.length<this._def.items.length)return m(n,{code:d.too_small,minimum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),g;!this._def.rest&&n.data.length>this._def.items.length&&(m(n,{code:d.too_big,maximum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),t.dirty());let s=[...n.data].map((e,t)=>{let s=this._def.items[t]||this._def.rest;return s?s._parse(new w(n,e,n.path,t)):null}).filter(e=>!!e);return n.common.async?Promise.all(s).then(e=>f.mergeArray(t,e)):f.mergeArray(t,s)}get items(){return this._def.items}rest(e){return new el({...this._def,rest:e})}}el.create=(e,t)=>{if(!Array.isArray(e))throw Error("You must pass an array of schemas to z.tuple([ ... ])");return new el({items:e,typeName:i.ZodTuple,rest:null,...T(t)})};class eu extends I{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(e){let{status:t,ctx:n}=this._processInputParams(e);if(n.parsedType!==l.object)return m(n,{code:d.invalid_type,expected:l.object,received:n.parsedType}),g;let s=[],a=this._def.keyType,r=this._def.valueType;for(let e in n.data)s.push({key:a._parse(new w(n,e,n.path,e)),value:r._parse(new w(n,n.data[e],n.path,e)),alwaysSet:e in n.data});return n.common.async?f.mergeObjectAsync(t,s):f.mergeObjectSync(t,s)}get element(){return this._def.valueType}static create(e,t,n){return new eu(t instanceof I?{keyType:e,valueType:t,typeName:i.ZodRecord,...T(n)}:{keyType:z.create(),valueType:e,typeName:i.ZodRecord,...T(t)})}}class ed extends I{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(e){let{status:t,ctx:n}=this._processInputParams(e);if(n.parsedType!==l.map)return m(n,{code:d.invalid_type,expected:l.map,received:n.parsedType}),g;let s=this._def.keyType,a=this._def.valueType,r=[...n.data.entries()].map(([e,t],r)=>({key:s._parse(new w(n,e,n.path,[r,"key"])),value:a._parse(new w(n,t,n.path,[r,"value"]))}));if(n.common.async){let e=new Map;return Promise.resolve().then(async()=>{for(let n of r){let s=await n.key,a=await n.value;if("aborted"===s.status||"aborted"===a.status)return g;("dirty"===s.status||"dirty"===a.status)&&t.dirty(),e.set(s.value,a.value)}return{status:t.value,value:e}})}{let e=new Map;for(let n of r){let s=n.key,a=n.value;if("aborted"===s.status||"aborted"===a.status)return g;("dirty"===s.status||"dirty"===a.status)&&t.dirty(),e.set(s.value,a.value)}return{status:t.value,value:e}}}}ed.create=(e,t,n)=>new ed({valueType:t,keyType:e,typeName:i.ZodMap,...T(n)});class ec extends I{_parse(e){let{status:t,ctx:n}=this._processInputParams(e);if(n.parsedType!==l.set)return m(n,{code:d.invalid_type,expected:l.set,received:n.parsedType}),g;let s=this._def;null!==s.minSize&&n.data.size<s.minSize.value&&(m(n,{code:d.too_small,minimum:s.minSize.value,type:"set",inclusive:!0,exact:!1,message:s.minSize.message}),t.dirty()),null!==s.maxSize&&n.data.size>s.maxSize.value&&(m(n,{code:d.too_big,maximum:s.maxSize.value,type:"set",inclusive:!0,exact:!1,message:s.maxSize.message}),t.dirty());let a=this._def.valueType;function r(e){let n=new Set;for(let s of e){if("aborted"===s.status)return g;"dirty"===s.status&&t.dirty(),n.add(s.value)}return{status:t.value,value:n}}let i=[...n.data.values()].map((e,t)=>a._parse(new w(n,e,n.path,t)));return n.common.async?Promise.all(i).then(e=>r(e)):r(i)}min(e,t){return new ec({...this._def,minSize:{value:e,message:r.toString(t)}})}max(e,t){return new ec({...this._def,maxSize:{value:e,message:r.toString(t)}})}size(e,t){return this.min(e,t).max(e,t)}nonempty(e){return this.min(1,e)}}ec.create=(e,t)=>new ec({valueType:e,minSize:null,maxSize:null,typeName:i.ZodSet,...T(t)});class ep extends I{constructor(){super(...arguments),this.validate=this.implement}_parse(e){let{ctx:t}=this._processInputParams(e);if(t.parsedType!==l.function)return m(t,{code:d.invalid_type,expected:l.function,received:t.parsedType}),g;function n(e,n){return h({data:e,path:t.path,errorMaps:[t.common.contextualErrorMap,t.schemaErrorMap,p,p].filter(e=>!!e),issueData:{code:d.invalid_arguments,argumentsError:n}})}function s(e,n){return h({data:e,path:t.path,errorMaps:[t.common.contextualErrorMap,t.schemaErrorMap,p,p].filter(e=>!!e),issueData:{code:d.invalid_return_type,returnTypeError:n}})}let a={errorMap:t.common.contextualErrorMap},r=t.data;if(this._def.returns instanceof e_){let e=this;return _(async function(...t){let i=new c([]),o=await e._def.args.parseAsync(t,a).catch(e=>{throw i.addIssue(n(t,e)),i}),l=await Reflect.apply(r,this,o);return await e._def.returns._def.type.parseAsync(l,a).catch(e=>{throw i.addIssue(s(l,e)),i})})}{let e=this;return _(function(...t){let i=e._def.args.safeParse(t,a);if(!i.success)throw new c([n(t,i.error)]);let o=Reflect.apply(r,this,i.data),l=e._def.returns.safeParse(o,a);if(!l.success)throw new c([s(o,l.error)]);return l.data})}}parameters(){return this._def.args}returnType(){return this._def.returns}args(...e){return new ep({...this._def,args:el.create(e).rest(Q.create())})}returns(e){return new ep({...this._def,returns:e})}implement(e){return this.parse(e)}strictImplement(e){return this.parse(e)}static create(e,t,n){return new ep({args:e||el.create([]).rest(Q.create()),returns:t||Q.create(),typeName:i.ZodFunction,...T(n)})}}class eh extends I{get schema(){return this._def.getter()}_parse(e){let{ctx:t}=this._processInputParams(e);return this._def.getter()._parse({data:t.data,path:t.path,parent:t})}}eh.create=(e,t)=>new eh({getter:e,typeName:i.ZodLazy,...T(t)});class em extends I{_parse(e){if(e.data!==this._def.value){let t=this._getOrReturnCtx(e);return m(t,{received:t.data,code:d.invalid_literal,expected:this._def.value}),g}return{status:"valid",value:e.data}}get value(){return this._def.value}}function ef(e,t){return new eg({values:e,typeName:i.ZodEnum,...T(t)})}em.create=(e,t)=>new em({value:e,typeName:i.ZodLiteral,...T(t)});class eg extends I{_parse(e){if("string"!=typeof e.data){let t=this._getOrReturnCtx(e),n=this._def.values;return m(t,{expected:s.joinValues(n),received:t.parsedType,code:d.invalid_type}),g}if(this._cache||(this._cache=new Set(this._def.values)),!this._cache.has(e.data)){let t=this._getOrReturnCtx(e),n=this._def.values;return m(t,{received:t.data,code:d.invalid_enum_value,options:n}),g}return _(e.data)}get options(){return this._def.values}get enum(){let e={};for(let t of this._def.values)e[t]=t;return e}get Values(){let e={};for(let t of this._def.values)e[t]=t;return e}get Enum(){let e={};for(let t of this._def.values)e[t]=t;return e}extract(e,t=this._def){return eg.create(e,{...this._def,...t})}exclude(e,t=this._def){return eg.create(this.options.filter(t=>!e.includes(t)),{...this._def,...t})}}eg.create=ef;class ey extends I{_parse(e){let t=s.getValidEnumValues(this._def.values),n=this._getOrReturnCtx(e);if(n.parsedType!==l.string&&n.parsedType!==l.number){let e=s.objectValues(t);return m(n,{expected:s.joinValues(e),received:n.parsedType,code:d.invalid_type}),g}if(this._cache||(this._cache=new Set(s.getValidEnumValues(this._def.values))),!this._cache.has(e.data)){let e=s.objectValues(t);return m(n,{received:n.data,code:d.invalid_enum_value,options:e}),g}return _(e.data)}get enum(){return this._def.values}}ey.create=(e,t)=>new ey({values:e,typeName:i.ZodNativeEnum,...T(t)});class e_ extends I{unwrap(){return this._def.type}_parse(e){let{ctx:t}=this._processInputParams(e);return t.parsedType!==l.promise&&!1===t.common.async?(m(t,{code:d.invalid_type,expected:l.promise,received:t.parsedType}),g):_((t.parsedType===l.promise?t.data:Promise.resolve(t.data)).then(e=>this._def.type.parseAsync(e,{path:t.path,errorMap:t.common.contextualErrorMap})))}}e_.create=(e,t)=>new e_({type:e,typeName:i.ZodPromise,...T(t)});class ev extends I{innerType(){return this._def.schema}sourceType(){return this._def.schema._def.typeName===i.ZodEffects?this._def.schema.sourceType():this._def.schema}_parse(e){let{status:t,ctx:n}=this._processInputParams(e),a=this._def.effect||null,r={addIssue:e=>{m(n,e),e.fatal?t.abort():t.dirty()},get path(){return n.path}};if(r.addIssue=r.addIssue.bind(r),"preprocess"===a.type){let e=a.transform(n.data,r);if(n.common.async)return Promise.resolve(e).then(async e=>{if("aborted"===t.value)return g;let s=await this._def.schema._parseAsync({data:e,path:n.path,parent:n});return"aborted"===s.status?g:"dirty"===s.status||"dirty"===t.value?y(s.value):s});{if("aborted"===t.value)return g;let s=this._def.schema._parseSync({data:e,path:n.path,parent:n});return"aborted"===s.status?g:"dirty"===s.status||"dirty"===t.value?y(s.value):s}}if("refinement"===a.type){let e=e=>{let t=a.refinement(e,r);if(n.common.async)return Promise.resolve(t);if(t instanceof Promise)throw Error("Async refinement encountered during synchronous parse operation. Use .parseAsync instead.");return e};if(!1!==n.common.async)return this._def.schema._parseAsync({data:n.data,path:n.path,parent:n}).then(n=>"aborted"===n.status?g:("dirty"===n.status&&t.dirty(),e(n.value).then(()=>({status:t.value,value:n.value}))));{let s=this._def.schema._parseSync({data:n.data,path:n.path,parent:n});return"aborted"===s.status?g:("dirty"===s.status&&t.dirty(),e(s.value),{status:t.value,value:s.value})}}if("transform"===a.type){if(!1!==n.common.async)return this._def.schema._parseAsync({data:n.data,path:n.path,parent:n}).then(e=>x(e)?Promise.resolve(a.transform(e.value,r)).then(e=>({status:t.value,value:e})):g);{let e=this._def.schema._parseSync({data:n.data,path:n.path,parent:n});if(!x(e))return g;let s=a.transform(e.value,r);if(s instanceof Promise)throw Error("Asynchronous transform encountered during synchronous parse operation. Use .parseAsync instead.");return{status:t.value,value:s}}}s.assertNever(a)}}ev.create=(e,t,n)=>new ev({schema:e,typeName:i.ZodEffects,effect:t,...T(n)}),ev.createWithPreprocess=(e,t,n)=>new ev({schema:t,effect:{type:"preprocess",transform:e},typeName:i.ZodEffects,...T(n)});class eb extends I{_parse(e){return this._getType(e)===l.undefined?_(void 0):this._def.innerType._parse(e)}unwrap(){return this._def.innerType}}eb.create=(e,t)=>new eb({innerType:e,typeName:i.ZodOptional,...T(t)});class ex extends I{_parse(e){return this._getType(e)===l.null?_(null):this._def.innerType._parse(e)}unwrap(){return this._def.innerType}}ex.create=(e,t)=>new ex({innerType:e,typeName:i.ZodNullable,...T(t)});class ek extends I{_parse(e){let{ctx:t}=this._processInputParams(e),n=t.data;return t.parsedType===l.undefined&&(n=this._def.defaultValue()),this._def.innerType._parse({data:n,path:t.path,parent:t})}removeDefault(){return this._def.innerType}}ek.create=(e,t)=>new ek({innerType:e,typeName:i.ZodDefault,defaultValue:"function"==typeof t.default?t.default:()=>t.default,...T(t)});class ew extends I{_parse(e){let{ctx:t}=this._processInputParams(e),n={...t,common:{...t.common,issues:[]}},s=this._def.innerType._parse({data:n.data,path:n.path,parent:{...n}});return k(s)?s.then(e=>({status:"valid",value:"valid"===e.status?e.value:this._def.catchValue({get error(){return new c(n.common.issues)},input:n.data})})):{status:"valid",value:"valid"===s.status?s.value:this._def.catchValue({get error(){return new c(n.common.issues)},input:n.data})}}removeCatch(){return this._def.innerType}}ew.create=(e,t)=>new ew({innerType:e,typeName:i.ZodCatch,catchValue:"function"==typeof t.catch?t.catch:()=>t.catch,...T(t)});class eR extends I{_parse(e){if(this._getType(e)!==l.nan){let t=this._getOrReturnCtx(e);return m(t,{code:d.invalid_type,expected:l.nan,received:t.parsedType}),g}return{status:"valid",value:e.data}}}eR.create=e=>new eR({typeName:i.ZodNaN,...T(e)}),Symbol("zod_brand");class eT extends I{_parse(e){let{ctx:t}=this._processInputParams(e),n=t.data;return this._def.type._parse({data:n,path:t.path,parent:t})}unwrap(){return this._def.type}}class eI extends I{_parse(e){let{status:t,ctx:n}=this._processInputParams(e);if(n.common.async)return(async()=>{let e=await this._def.in._parseAsync({data:n.data,path:n.path,parent:n});return"aborted"===e.status?g:"dirty"===e.status?(t.dirty(),y(e.value)):this._def.out._parseAsync({data:e.value,path:n.path,parent:n})})();{let e=this._def.in._parseSync({data:n.data,path:n.path,parent:n});return"aborted"===e.status?g:"dirty"===e.status?(t.dirty(),{status:"dirty",value:e.value}):this._def.out._parseSync({data:e.value,path:n.path,parent:n})}}static create(e,t){return new eI({in:e,out:t,typeName:i.ZodPipeline})}}class eC extends I{_parse(e){let t=this._def.innerType._parse(e),n=e=>(x(e)&&(e.value=Object.freeze(e.value)),e);return k(t)?t.then(e=>n(e)):n(t)}unwrap(){return this._def.innerType}}function eZ(e,t){let n="function"==typeof e?e(t):"string"==typeof e?{message:e}:e;return"string"==typeof n?{message:n}:n}function eS(e,t={},n){return e?Y.create().superRefine((s,a)=>{let r=e(s);if(r instanceof Promise)return r.then(e=>{if(!e){let e=eZ(t,s),r=e.fatal??n??!0;a.addIssue({code:"custom",...e,fatal:r})}});if(!r){let e=eZ(t,s),r=e.fatal??n??!0;a.addIssue({code:"custom",...e,fatal:r})}}):Y.create()}eC.create=(e,t)=>new eC({innerType:e,typeName:i.ZodReadonly,...T(t)}),es.lazycreate,function(e){e.ZodString="ZodString",e.ZodNumber="ZodNumber",e.ZodNaN="ZodNaN",e.ZodBigInt="ZodBigInt",e.ZodBoolean="ZodBoolean",e.ZodDate="ZodDate",e.ZodSymbol="ZodSymbol",e.ZodUndefined="ZodUndefined",e.ZodNull="ZodNull",e.ZodAny="ZodAny",e.ZodUnknown="ZodUnknown",e.ZodNever="ZodNever",e.ZodVoid="ZodVoid",e.ZodArray="ZodArray",e.ZodObject="ZodObject",e.ZodUnion="ZodUnion",e.ZodDiscriminatedUnion="ZodDiscriminatedUnion",e.ZodIntersection="ZodIntersection",e.ZodTuple="ZodTuple",e.ZodRecord="ZodRecord",e.ZodMap="ZodMap",e.ZodSet="ZodSet",e.ZodFunction="ZodFunction",e.ZodLazy="ZodLazy",e.ZodLiteral="ZodLiteral",e.ZodEnum="ZodEnum",e.ZodEffects="ZodEffects",e.ZodNativeEnum="ZodNativeEnum",e.ZodOptional="ZodOptional",e.ZodNullable="ZodNullable",e.ZodDefault="ZodDefault",e.ZodCatch="ZodCatch",e.ZodPromise="ZodPromise",e.ZodBranded="ZodBranded",e.ZodPipeline="ZodPipeline",e.ZodReadonly="ZodReadonly"}(i||(i={}));let eN=(e,t={message:`Input not instance of ${e.name}`})=>eS(t=>t instanceof e,t),eO=z.create,eA=K.create;eR.create,X.create;let ej=B.create;G.create,H.create,J.create;let eE=W.create,e$=Y.create,eM=Q.create;ee.create,et.create;let eP=en.create,eq=es.create;es.strictCreate;let eF=ea.create,eU=ei.create;eo.create;let eD=el.create,eL=eu.create;ed.create,ec.create,ep.create;let eV=eh.create,ez=em.create,eK=eg.create;ey.create,e_.create,ev.create;let eX=eb.create;ex.create,ev.createWithPreprocess,eI.create}};