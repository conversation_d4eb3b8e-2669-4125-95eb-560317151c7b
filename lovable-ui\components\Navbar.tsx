'use client';

import React, { useState } from "react";
import AISettings from "./ai-config/AISettings";

export default function Navbar() {
  const [isSettingsOpen, setIsSettingsOpen] = useState(false);

  return (
    <>
      <nav className="absolute top-0 left-0 right-0 z-20 flex items-center justify-between px-6 py-4">
        {/* Logo & main navigation */}
        <div className="flex items-center gap-10">
          <a
            href="/"
            className="flex items-center gap-2 text-2xl font-semibold text-white hover:opacity-90 transition-opacity"
          >
            {/* Simple gradient square to mimic Lovable logo */}
            <span className="inline-block w-6 h-6 rounded-sm bg-gradient-to-br from-orange-400 via-pink-500 to-blue-500" />
            Lovable
          </a>

          <div className="hidden md:flex items-center gap-8 text-sm text-gray-300">
            <a href="#" className="hover:text-white transition-colors">
              Community
            </a>
            <a href="#" className="hover:text-white transition-colors">
              Enterprise
            </a>
            <a href="#" className="hover:text-white transition-colors">
              Learn
            </a>
            <a href="#" className="hover:text-white transition-colors">
              Shipped
            </a>
          </div>
        </div>

        {/* Auth buttons and settings */}
        <div className="flex items-center gap-4 text-sm">
          <button
            onClick={() => setIsSettingsOpen(true)}
            className="text-gray-300 hover:text-white transition-colors flex items-center gap-2"
            title="AI Settings"
          >
            <svg
              className="w-5 h-5"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"
              />
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
              />
            </svg>
            <span className="hidden sm:inline">AI Settings</span>
          </button>
          <a
            href="#"
            className="text-gray-300 hover:text-white transition-colors"
          >
            Log in
          </a>
          <a
            href="#"
            className="px-4 py-2 bg-white text-black rounded-lg font-semibold hover:bg-gray-100 transition-colors"
          >
            Get started
          </a>
        </div>
      </nav>

      {/* AI Settings Modal */}
      <AISettings
        isOpen={isSettingsOpen}
        onClose={() => setIsSettingsOpen(false)}
      />
    </>
  );
}
