(()=>{var e={};e.id=409,e.ids=[409],e.modules={7849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1950:(e,o,t)=>{"use strict";t.r(o),t.d(o,{GlobalError:()=>s.a,__next_app__:()=>c,originalPathname:()=>p,pages:()=>u,routeModule:()=>f,tree:()=>l}),t(7352),t(5866),t(9058);var r=t(3191),n=t(8716),i=t(7922),s=t.n(i),a=t(5231),d={};for(let e in a)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>a[e]);t.d(o,d);let l=["",{children:["/_not-found",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.t.bind(t,5866,23)),"next/dist/client/components/not-found-error"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,9058)),"C:\\Users\\<USER>\\Documents\\github\\lovable-clone\\lovable-ui\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,5866,23)),"next/dist/client/components/not-found-error"]}],u=[],p="/_not-found/page",c={require:t,loadChunk:()=>Promise.resolve()},f=new r.AppPageRouteModule({definition:{kind:n.x.APP_PAGE,page:"/_not-found/page",pathname:"/_not-found",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},5130:(e,o,t)=>{Promise.resolve().then(t.bind(t,6719))},2688:(e,o,t)=>{Promise.resolve().then(t.t.bind(t,2994,23)),Promise.resolve().then(t.t.bind(t,6114,23)),Promise.resolve().then(t.t.bind(t,9727,23)),Promise.resolve().then(t.t.bind(t,9671,23)),Promise.resolve().then(t.t.bind(t,1868,23)),Promise.resolve().then(t.t.bind(t,4759,23))},6719:(e,o,t)=>{"use strict";t.d(o,{AIConfigProvider:()=>u,m:()=>p});var r=t(326),n=t(7577),i=t(729);function s(e,o){switch(o.type){case"SET_CONFIG":return{...e,config:o.payload,error:null};case"ADD_PROVIDER":return{...e,config:{...e.config,providers:[...e.config.providers,o.payload]}};case"UPDATE_PROVIDER":return{...e,config:{...e.config,providers:e.config.providers.map(e=>e.id===o.payload.id?{...e,...o.payload.config}:e)}};case"REMOVE_PROVIDER":let t=e.config.providers.filter(e=>e.id!==o.payload);return{...e,config:{...e.config,providers:t,defaultProvider:e.config.defaultProvider===o.payload?t[0]?.id:e.config.defaultProvider}};case"SET_DEFAULT_PROVIDER":return{...e,config:{...e.config,defaultProvider:o.payload}};case"SET_DEFAULT_MODEL":return{...e,config:{...e.config,defaultModel:o.payload}};case"UPDATE_DEFAULT_PARAMETERS":return{...e,config:{...e.config,defaultParameters:{...e.config.defaultParameters,...o.payload}}};case"TOGGLE_PROVIDER":return{...e,config:{...e.config,providers:e.config.providers.map(e=>e.id===o.payload?{...e,enabled:!e.enabled}:e)}};case"RESET_CONFIG":return{...e,config:i.T,error:null};default:return e}}let a={config:i.T,isLoading:!1,error:null},d=(0,n.createContext)(void 0),l="ai-config";function u({children:e}){let[o,t]=(0,n.useReducer)(s,a),i=async()=>{try{localStorage.setItem(l,JSON.stringify(o.config))}catch(e){console.error("Failed to save AI config:",e)}},u=async()=>{try{let e=localStorage.getItem(l);if(e){let o=JSON.parse(e);t({type:"SET_CONFIG",payload:o})}}catch(e){console.error("Failed to load AI config:",e)}},p=async e=>{let t=o.config.providers.find(o=>o.id===e);if(!t)return{success:!1,error:"Provider not found"};try{let e=await fetch("/api/ai/test-connection",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({provider:t})});return await e.json()}catch(e){return{success:!1,error:e instanceof Error?e.message:"Connection test failed"}}};return r.jsx(d.Provider,{value:{state:o,actions:{setConfig:e=>t({type:"SET_CONFIG",payload:e}),addProvider:e=>t({type:"ADD_PROVIDER",payload:e}),updateProvider:(e,o)=>t({type:"UPDATE_PROVIDER",payload:{id:e,config:o}}),removeProvider:e=>t({type:"REMOVE_PROVIDER",payload:e}),setDefaultProvider:e=>t({type:"SET_DEFAULT_PROVIDER",payload:e}),setDefaultModel:e=>t({type:"SET_DEFAULT_MODEL",payload:e}),updateDefaultParameters:e=>t({type:"UPDATE_DEFAULT_PARAMETERS",payload:e}),toggleProvider:e=>t({type:"TOGGLE_PROVIDER",payload:e}),resetConfig:()=>t({type:"RESET_CONFIG"}),saveConfig:i,loadConfig:u,testConnection:p,validateProvider:e=>{let o=[];return e.name.trim()||o.push("Provider name is required"),e.apiKey.trim()||o.push("API key is required"),"custom"!==e.provider||(e.baseURL?.trim()||o.push("Base URL is required for custom providers"),e.modelName?.trim()||o.push("Model name is required for custom providers")),{isValid:0===o.length,errors:o}}}},children:e})}function p(){let e=(0,n.useContext)(d);if(void 0===e)throw Error("useAIConfig must be used within an AIConfigProvider");return e}},729:(e,o,t)=>{"use strict";t.d(o,{T:()=>n,k:()=>r});let r={openai:[{id:"gpt-4o",name:"GPT-4o",provider:"openai",maxTokens:4096,supportsStreaming:!0,supportsTools:!0,supportsVision:!0,contextWindow:128e3},{id:"gpt-4o-mini",name:"GPT-4o Mini",provider:"openai",maxTokens:16384,supportsStreaming:!0,supportsTools:!0,supportsVision:!0,contextWindow:128e3},{id:"gpt-4-turbo",name:"GPT-4 Turbo",provider:"openai",maxTokens:4096,supportsStreaming:!0,supportsTools:!0,supportsVision:!0,contextWindow:128e3},{id:"gpt-3.5-turbo",name:"GPT-3.5 Turbo",provider:"openai",maxTokens:4096,supportsStreaming:!0,supportsTools:!0,supportsVision:!1,contextWindow:16385}],anthropic:[{id:"claude-3-5-sonnet-20241022",name:"Claude 3.5 Sonnet",provider:"anthropic",maxTokens:8192,supportsStreaming:!0,supportsTools:!0,supportsVision:!0,contextWindow:2e5},{id:"claude-3-5-haiku-20241022",name:"Claude 3.5 Haiku",provider:"anthropic",maxTokens:8192,supportsStreaming:!0,supportsTools:!0,supportsVision:!0,contextWindow:2e5},{id:"claude-3-opus-20240229",name:"Claude 3 Opus",provider:"anthropic",maxTokens:4096,supportsStreaming:!0,supportsTools:!0,supportsVision:!0,contextWindow:2e5}],google:[{id:"gemini-1.5-pro",name:"Gemini 1.5 Pro",provider:"google",maxTokens:8192,supportsStreaming:!0,supportsTools:!0,supportsVision:!0,contextWindow:2e6},{id:"gemini-1.5-flash",name:"Gemini 1.5 Flash",provider:"google",maxTokens:8192,supportsStreaming:!0,supportsTools:!0,supportsVision:!0,contextWindow:1e6}],cohere:[{id:"command-r-plus",name:"Command R+",provider:"cohere",maxTokens:4096,supportsStreaming:!0,supportsTools:!0,supportsVision:!1,contextWindow:128e3},{id:"command-r",name:"Command R",provider:"cohere",maxTokens:4096,supportsStreaming:!0,supportsTools:!0,supportsVision:!1,contextWindow:128e3}],mistral:[{id:"mistral-large-latest",name:"Mistral Large",provider:"mistral",maxTokens:8192,supportsStreaming:!0,supportsTools:!0,supportsVision:!1,contextWindow:128e3},{id:"mistral-medium-latest",name:"Mistral Medium",provider:"mistral",maxTokens:8192,supportsStreaming:!0,supportsTools:!0,supportsVision:!1,contextWindow:32e3}],custom:[]},n={providers:[],defaultParameters:{temperature:.7,maxTokens:4096,topP:1,frequencyPenalty:0,presencePenalty:0}}},9058:(e,o,t)=>{"use strict";t.r(o),t.d(o,{default:()=>c,metadata:()=>p});var r=t(9510),n=t(7366),i=t.n(n);t(7272);var s=t(8570);let a=(0,s.createProxy)(String.raw`C:\Users\<USER>\Documents\github\lovable-clone\lovable-ui\lib\contexts\ai-config-context.tsx`),{__esModule:d,$$typeof:l}=a;a.default;let u=(0,s.createProxy)(String.raw`C:\Users\<USER>\Documents\github\lovable-clone\lovable-ui\lib\contexts\ai-config-context.tsx#AIConfigProvider`);(0,s.createProxy)(String.raw`C:\Users\<USER>\Documents\github\lovable-clone\lovable-ui\lib\contexts\ai-config-context.tsx#useAIConfig`);let p={title:"Lovable Clone - AI-Powered Code Generation",description:"Build applications faster with AI-powered code generation"};function c({children:e}){return r.jsx("html",{lang:"en",children:r.jsx("body",{className:i().className,children:r.jsx(u,{children:e})})})}},6399:(e,o)=>{"use strict";Object.defineProperty(o,"__esModule",{value:!0}),function(e,o){for(var t in o)Object.defineProperty(e,t,{enumerable:!0,get:o[t]})}(o,{isNotFoundError:function(){return n},notFound:function(){return r}});let t="NEXT_NOT_FOUND";function r(){let e=Error(t);throw e.digest=t,e}function n(e){return"object"==typeof e&&null!==e&&"digest"in e&&e.digest===t}("function"==typeof o.default||"object"==typeof o.default&&null!==o.default)&&void 0===o.default.__esModule&&(Object.defineProperty(o.default,"__esModule",{value:!0}),Object.assign(o.default,o),e.exports=o.default)},7352:(e,o,t)=>{"use strict";Object.defineProperty(o,"__esModule",{value:!0}),function(e,o){for(var t in o)Object.defineProperty(e,t,{enumerable:!0,get:o[t]})}(o,{PARALLEL_ROUTE_DEFAULT_PATH:function(){return n},default:function(){return i}});let r=t(6399),n="next/dist/client/components/parallel-route-default.js";function i(){(0,r.notFound)()}("function"==typeof o.default||"object"==typeof o.default&&null!==o.default)&&void 0===o.default.__esModule&&(Object.defineProperty(o.default,"__esModule",{value:!0}),Object.assign(o.default,o),e.exports=o.default)},7272:()=>{}};var o=require("../../webpack-runtime.js");o.C(e);var t=e=>o(o.s=e),r=o.X(0,[948,82],()=>t(1950));module.exports=r})();