"use client";

import { useState, useEffect, Suspense } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import MessageDisplay from "@/components/MessageDisplay";

interface Message {
  type: string;
  content?: string;
  name?: string;
  input?: any;
  result?: any;
}

function GeneratePage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [messages, setMessages] = useState<Message[]>([]);
  const [isGenerating, setIsGenerating] = useState(false);
  const [error, setError] = useState("");

  const prompt = searchParams?.get("prompt") || "";

  useEffect(() => {
    if (!prompt) {
      router.push("/");
      return;
    }

    const generateCode = async () => {
      setIsGenerating(true);
      setMessages([]);
      setError("");

      try {
        const response = await fetch("/api/generate", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ prompt }),
        });

        if (!response.ok) {
          throw new Error(`Error: ${response.status}`);
        }

        if (!response.body) {
          throw new Error("ReadableStream not supported");
        }

        // Set up SSE
        const reader = response.body.getReader();
        const decoder = new TextDecoder();

        while (true) {
          const { done, value } = await reader.read();
          if (done) break;

          const text = decoder.decode(value);
          const lines = text.split("\n\n");

          for (const line of lines) {
            if (line.startsWith("data: ")) {
              try {
                const data = JSON.parse(line.substring(6));
                setMessages((prev) => [...prev, data as Message]);
              } catch (e) {
                console.error("Failed to parse SSE message:", e);
              }
            }
          }
        }
      } catch (err: any) {
        console.error("Generation error:", err);
        setError(err.message || "Failed to generate code");
      } finally {
        setIsGenerating(false);
      }
    };

    generateCode();
  }, [prompt, router]);

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-6">Generating Code</h1>
      <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
        <h2 className="font-semibold mb-2">Your Prompt:</h2>
        <p className="text-gray-700">{prompt}</p>
      </div>

      {isGenerating && (
        <div className="mb-6 flex items-center">
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500 mr-3"></div>
          <p>Generating code... This may take a minute.</p>
        </div>
      )}

      {error && (
        <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg text-red-700">
          {error}
        </div>
      )}

      <MessageDisplay messages={messages} />
    </div>
  );
}

export default function GeneratePageWithSuspense() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <GeneratePage />
    </Suspense>
  );
}
