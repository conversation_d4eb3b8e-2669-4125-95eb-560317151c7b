(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[938],{1544:function(e,t,r){Promise.resolve().then(r.bind(r,2088))},2088:function(e,t,r){"use strict";r.r(t),r.d(t,{default:function(){return m}});var s=r(7437),a=r(2265),o=r(552),i=r(2383);function n(e){let{provider:t,isDefault:r,onEdit:o,onDelete:i,onTest:n,onToggle:l,onSetDefault:d}=e,[c,m]=(0,a.useState)(!1),[u,x]=(0,a.useState)(null),p=async()=>{m(!0),x(null);try{let e=await n();x(e)}catch(e){x({success:!1,error:e instanceof Error?e.message:"Test failed"})}finally{m(!1)}};return(0,s.jsxs)("div",{className:"bg-gray-800 rounded-lg border border-gray-700 p-4",children:[(0,s.jsxs)("div",{className:"flex items-start justify-between mb-4",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsx)("div",{className:"text-2xl",children:(e=>{switch(e){case"openai":default:return"\uD83E\uDD16";case"anthropic":return"\uD83E\uDDE0";case"google":return"\uD83D\uDD0D";case"cohere":return"\uD83D\uDCAC";case"mistral":return"\uD83C\uDF2A️";case"custom":return"⚙️"}})(t.provider)}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-white",children:t.name}),r&&(0,s.jsx)("span",{className:"bg-blue-600 text-white text-xs px-2 py-1 rounded-full",children:"Default"}),!t.enabled&&(0,s.jsx)("span",{className:"bg-gray-600 text-white text-xs px-2 py-1 rounded-full",children:"Disabled"})]}),(0,s.jsxs)("p",{className:"text-gray-400 text-sm",children:[(e=>{switch(e){case"openai":return"OpenAI";case"anthropic":return"Anthropic";case"google":return"Google";case"cohere":return"Cohere";case"mistral":return"Mistral";case"custom":return"Custom";default:return e}})(t.provider),"custom"===t.provider&&t.baseURL&&(0,s.jsxs)("span",{className:"ml-1",children:["• ",new URL(t.baseURL).hostname]})]})]})]}),(0,s.jsx)("div",{className:"flex items-center space-x-2",children:(0,s.jsx)("button",{onClick:l,className:"w-12 h-6 rounded-full transition-colors ".concat(t.enabled?"bg-green-600":"bg-gray-600"),children:(0,s.jsx)("div",{className:"w-5 h-5 bg-white rounded-full transition-transform ".concat(t.enabled?"translate-x-6":"translate-x-1")})})})]}),(0,s.jsx)("div",{className:"mb-4",children:(0,s.jsxs)("div",{className:"flex items-center space-x-2 text-sm",children:[(0,s.jsx)("span",{className:"text-gray-400",children:"API Key:"}),(0,s.jsx)("span",{className:"text-green-400",children:t.apiKey?"••••••••":"Not set"})]})}),u&&(0,s.jsx)("div",{className:"mb-4 p-3 rounded-lg text-sm ".concat(u.success?"bg-green-900 border border-green-700 text-green-100":"bg-red-900 border border-red-700 text-red-100"),children:u.success?(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"font-medium",children:"✓ Connection successful"}),u.latency&&(0,s.jsxs)("div",{className:"text-xs opacity-75 mt-1",children:["Response time: ",u.latency,"ms"]})]}):(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"font-medium",children:"✗ Connection failed"}),u.error&&(0,s.jsx)("div",{className:"text-xs opacity-75 mt-1",children:u.error})]})}),(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("button",{onClick:p,disabled:c||!t.enabled,className:"bg-gray-700 hover:bg-gray-600 disabled:bg-gray-800 disabled:text-gray-500 text-white px-3 py-1 rounded text-sm font-medium transition-colors",children:c?"Testing...":"Test"}),!r&&t.enabled&&(0,s.jsx)("button",{onClick:d,className:"bg-blue-700 hover:bg-blue-600 text-white px-3 py-1 rounded text-sm font-medium transition-colors",children:"Set Default"})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("button",{onClick:o,className:"text-gray-400 hover:text-white transition-colors",title:"Edit provider",children:(0,s.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})})}),(0,s.jsx)("button",{onClick:i,className:"text-gray-400 hover:text-red-400 transition-colors",title:"Delete provider",children:(0,s.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})})})]})]})]})}function l(e){let{provider:t,onSave:r,onCancel:o}=e,[i,n]=(0,a.useState)({name:"",provider:"openai",apiKey:"",baseURL:"",organization:"",project:"",modelName:"",headers:{},enabled:!0}),[l,d]=(0,a.useState)({});(0,a.useEffect)(()=>{t&&n({name:t.name,provider:t.provider,apiKey:t.apiKey,baseURL:t.baseURL||"",organization:t.organization||"",project:t.project||"",modelName:t.modelName||"",headers:t.headers||{},enabled:t.enabled})},[t]);let c=()=>{let e={};return i.name.trim()||(e.name="Provider name is required"),i.apiKey.trim()||(e.apiKey="API key is required"),"custom"!==i.provider||(i.baseURL.trim()||(e.baseURL="Base URL is required for custom providers"),i.modelName.trim()||(e.modelName="Model name is required for custom providers")),d(e),0===Object.keys(e).length},m=(e,t)=>{n(r=>({...r,[e]:t})),l[e]&&d(t=>({...t,[e]:""}))};return(0,s.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,s.jsxs)("div",{className:"bg-gray-900 rounded-lg border border-gray-800 w-full max-w-2xl max-h-[90vh] overflow-y-auto",children:[(0,s.jsx)("div",{className:"p-6 border-b border-gray-800",children:(0,s.jsx)("h2",{className:"text-xl font-semibold text-white",children:t?"Edit Provider":"Add AI Provider"})}),(0,s.jsxs)("form",{onSubmit:e=>{e.preventDefault(),c()&&r({id:(null==t?void 0:t.id)||"".concat(i.provider,"-").concat(Date.now()),name:i.name,provider:i.provider,apiKey:i.apiKey,enabled:i.enabled,...i.baseURL&&{baseURL:i.baseURL},...i.organization&&{organization:i.organization},...i.project&&{project:i.project},...i.modelName&&{modelName:i.modelName},...Object.keys(i.headers).length>0&&{headers:i.headers}})},className:"p-6 space-y-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Provider Type"}),(0,s.jsxs)("select",{value:i.provider,onChange:e=>m("provider",e.target.value),className:"w-full bg-gray-800 border border-gray-700 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500",disabled:!!t,children:[(0,s.jsx)("option",{value:"openai",children:"OpenAI"}),(0,s.jsx)("option",{value:"anthropic",children:"Anthropic"}),(0,s.jsx)("option",{value:"google",children:"Google"}),(0,s.jsx)("option",{value:"cohere",children:"Cohere"}),(0,s.jsx)("option",{value:"mistral",children:"Mistral"}),(0,s.jsx)("option",{value:"custom",children:"Custom (OpenAI-compatible)"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Provider Name"}),(0,s.jsx)("input",{type:"text",value:i.name,onChange:e=>m("name",e.target.value),className:"w-full bg-gray-800 border rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 ".concat(l.name?"border-red-500":"border-gray-700"),placeholder:"e.g., My OpenAI Account"}),l.name&&(0,s.jsx)("p",{className:"text-red-400 text-sm mt-1",children:l.name})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"API Key"}),(0,s.jsx)("input",{type:"password",value:i.apiKey,onChange:e=>m("apiKey",e.target.value),className:"w-full bg-gray-800 border rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 ".concat(l.apiKey?"border-red-500":"border-gray-700"),placeholder:"Enter your API key"}),l.apiKey&&(0,s.jsx)("p",{className:"text-red-400 text-sm mt-1",children:l.apiKey})]}),("custom"===i.provider||i.baseURL)&&(0,s.jsxs)("div",{children:[(0,s.jsxs)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:["Base URL ","custom"===i.provider&&(0,s.jsx)("span",{className:"text-red-400",children:"*"})]}),(0,s.jsx)("input",{type:"url",value:i.baseURL,onChange:e=>m("baseURL",e.target.value),className:"w-full bg-gray-800 border rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 ".concat(l.baseURL?"border-red-500":"border-gray-700"),placeholder:"https://api.example.com/v1"}),l.baseURL&&(0,s.jsx)("p",{className:"text-red-400 text-sm mt-1",children:l.baseURL})]}),"custom"===i.provider&&(0,s.jsxs)("div",{children:[(0,s.jsxs)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:["Model Name ",(0,s.jsx)("span",{className:"text-red-400",children:"*"})]}),(0,s.jsx)("input",{type:"text",value:i.modelName,onChange:e=>m("modelName",e.target.value),className:"w-full bg-gray-800 border rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 ".concat(l.modelName?"border-red-500":"border-gray-700"),placeholder:"e.g., gpt-3.5-turbo"}),l.modelName&&(0,s.jsx)("p",{className:"text-red-400 text-sm mt-1",children:l.modelName})]}),"openai"===i.provider&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Organization ID (Optional)"}),(0,s.jsx)("input",{type:"text",value:i.organization,onChange:e=>m("organization",e.target.value),className:"w-full bg-gray-800 border border-gray-700 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"org-..."})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Project ID (Optional)"}),(0,s.jsx)("input",{type:"text",value:i.project,onChange:e=>m("project",e.target.value),className:"w-full bg-gray-800 border border-gray-700 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"proj_..."})]})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-300",children:"Enable Provider"}),(0,s.jsx)("button",{type:"button",onClick:()=>m("enabled",!i.enabled),className:"w-12 h-6 rounded-full transition-colors ".concat(i.enabled?"bg-green-600":"bg-gray-600"),children:(0,s.jsx)("div",{className:"w-5 h-5 bg-white rounded-full transition-transform ".concat(i.enabled?"translate-x-6":"translate-x-1")})})]}),(0,s.jsxs)("div",{className:"flex items-center justify-end space-x-3 pt-6 border-t border-gray-800",children:[(0,s.jsx)("button",{type:"button",onClick:o,className:"px-4 py-2 text-gray-400 hover:text-white transition-colors",children:"Cancel"}),(0,s.jsx)("button",{type:"submit",className:"bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg font-medium transition-colors",children:t?"Update Provider":"Add Provider"})]})]})]})})}var d=r(8506);function c(){let{state:e,actions:t}=(0,i.m)(),[r,o]=(0,a.useState)(!1),[n,l]=(0,a.useState)(e.config.defaultParameters),c=(()=>{let t=e.config.providers.filter(e=>e.enabled),r=[];return t.forEach(e=>{(d.k[e.provider]||[]).forEach(t=>{r.push({id:t.id,name:"".concat(t.name," (").concat(e.name,")"),provider:e.id})})}),r})(),m=e.config.providers.find(t=>t.id===e.config.defaultProvider);return(0,s.jsxs)("div",{className:"bg-gray-900 rounded-lg border border-gray-800 p-6 mb-6",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h2",{className:"text-xl font-semibold text-white mb-1",children:"Default Settings"}),(0,s.jsx)("p",{className:"text-gray-400 text-sm",children:"Configure default AI model and parameters for code generation."})]}),(0,s.jsx)("button",{onClick:()=>o(!r),className:"text-gray-400 hover:text-white transition-colors",children:(0,s.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})})})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("h3",{className:"text-lg font-medium text-white",children:"Provider & Model"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Default Provider"}),(0,s.jsxs)("select",{value:e.config.defaultProvider||"",onChange:e=>t.setDefaultProvider(e.target.value),className:"w-full bg-gray-800 border border-gray-700 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500",disabled:0===e.config.providers.length,children:[(0,s.jsx)("option",{value:"",children:"Select a provider"}),e.config.providers.filter(e=>e.enabled).map(e=>(0,s.jsx)("option",{value:e.id,children:e.name},e.id))]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Default Model"}),(0,s.jsxs)("select",{value:e.config.defaultModel||"",onChange:e=>t.setDefaultModel(e.target.value),className:"w-full bg-gray-800 border border-gray-700 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500",disabled:0===c.length,children:[(0,s.jsx)("option",{value:"",children:"Select a model"}),c.map(e=>(0,s.jsx)("option",{value:e.id,children:e.name},"".concat(e.provider,"-").concat(e.id)))]})]})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("h3",{className:"text-lg font-medium text-white",children:"Model Parameters"}),r?(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:["Temperature (",n.temperature,")"]}),(0,s.jsx)("input",{type:"range",min:"0",max:"2",step:"0.1",value:n.temperature,onChange:e=>l(t=>({...t,temperature:parseFloat(e.target.value)})),className:"w-full"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Max Tokens"}),(0,s.jsx)("input",{type:"number",min:"1",max:"32000",value:n.maxTokens,onChange:e=>l(t=>({...t,maxTokens:parseInt(e.target.value)})),className:"w-full bg-gray-800 border border-gray-700 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:["Top P (",n.topP,")"]}),(0,s.jsx)("input",{type:"range",min:"0",max:"1",step:"0.1",value:n.topP,onChange:e=>l(t=>({...t,topP:parseFloat(e.target.value)})),className:"w-full"})]}),(0,s.jsxs)("div",{className:"flex space-x-2",children:[(0,s.jsx)("button",{onClick:()=>{t.updateDefaultParameters(n),o(!1)},className:"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors",children:"Save"}),(0,s.jsx)("button",{onClick:()=>{l(e.config.defaultParameters),o(!1)},className:"bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded-lg font-medium transition-colors",children:"Cancel"})]})]}):(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsx)("span",{className:"text-gray-400",children:"Temperature:"}),(0,s.jsx)("span",{className:"text-white",children:e.config.defaultParameters.temperature})]}),(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsx)("span",{className:"text-gray-400",children:"Max Tokens:"}),(0,s.jsx)("span",{className:"text-white",children:e.config.defaultParameters.maxTokens})]}),(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsx)("span",{className:"text-gray-400",children:"Top P:"}),(0,s.jsx)("span",{className:"text-white",children:e.config.defaultParameters.topP})]})]})]})]}),(0,s.jsx)("div",{className:"mt-6 pt-6 border-t border-gray-800",children:(0,s.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,s.jsx)("span",{className:"text-gray-400",children:"Current Configuration:"}),(0,s.jsx)("span",{className:"text-white",children:m?(0,s.jsxs)(s.Fragment,{children:[m.name,e.config.defaultModel&&" • ".concat(e.config.defaultModel)]}):"No default provider set"})]})})]})}function m(){let{state:e,actions:t}=(0,i.m)(),[r,d]=(0,a.useState)(!1),[m,u]=(0,a.useState)(null),x=e=>{u(e),d(!0)},p=e=>{confirm("Are you sure you want to delete this provider?")&&t.removeProvider(e)},g=async e=>await t.testConnection(e),h=e=>{t.toggleProvider(e)},f=e=>{t.setDefaultProvider(e)};return(0,s.jsxs)("main",{className:"min-h-screen bg-black",children:[(0,s.jsx)(o.Z,{}),(0,s.jsx)("div",{className:"h-16"})," ",(0,s.jsxs)("div",{className:"container mx-auto px-4 py-8 max-w-6xl",children:[(0,s.jsxs)("div",{className:"mb-8",children:[(0,s.jsx)("h1",{className:"text-3xl font-bold text-white mb-2",children:"AI Configuration"}),(0,s.jsx)("p",{className:"text-gray-400",children:"Configure AI providers, models, and default settings for code generation."})]}),(0,s.jsx)(c,{}),(0,s.jsxs)("div",{className:"bg-gray-900 rounded-lg border border-gray-800 p-6 mb-6",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h2",{className:"text-xl font-semibold text-white mb-1",children:"AI Providers"}),(0,s.jsx)("p",{className:"text-gray-400 text-sm",children:"Add and configure AI providers for code generation."})]}),(0,s.jsx)("button",{onClick:()=>d(!0),className:"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors",children:"Add Provider"})]}),0===e.config.providers.length?(0,s.jsxs)("div",{className:"text-center py-12",children:[(0,s.jsx)("div",{className:"w-16 h-16 bg-gray-800 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,s.jsx)("svg",{className:"w-8 h-8 text-gray-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})})}),(0,s.jsx)("h3",{className:"text-lg font-medium text-white mb-2",children:"No providers configured"}),(0,s.jsx)("p",{className:"text-gray-400 mb-4",children:"Add your first AI provider to start generating code."}),(0,s.jsx)("button",{onClick:()=>d(!0),className:"bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg font-medium transition-colors",children:"Add Provider"})]}):(0,s.jsx)("div",{className:"grid gap-4",children:e.config.providers.map(t=>(0,s.jsx)(n,{provider:t,isDefault:e.config.defaultProvider===t.id,onEdit:()=>x(t),onDelete:()=>p(t.id),onTest:()=>g(t.id),onToggle:()=>h(t.id),onSetDefault:()=>f(t.id)},t.id))})]}),(0,s.jsxs)("div",{className:"bg-gray-900 rounded-lg border border-gray-800 p-6",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"Configuration Status"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,s.jsxs)("div",{className:"bg-gray-800 rounded-lg p-4",children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-white mb-1",children:e.config.providers.length}),(0,s.jsx)("div",{className:"text-gray-400 text-sm",children:"Total Providers"})]}),(0,s.jsxs)("div",{className:"bg-gray-800 rounded-lg p-4",children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-white mb-1",children:e.config.providers.filter(e=>e.enabled).length}),(0,s.jsx)("div",{className:"text-gray-400 text-sm",children:"Active Providers"})]}),(0,s.jsxs)("div",{className:"bg-gray-800 rounded-lg p-4",children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-white mb-1",children:e.config.defaultProvider?"✓":"✗"}),(0,s.jsx)("div",{className:"text-gray-400 text-sm",children:"Default Set"})]})]})]})]}),r&&(0,s.jsx)(l,{provider:m,onSave:m?e=>{m&&(t.updateProvider(m.id,e),u(null),d(!1))}:e=>{t.addProvider(e),d(!1)},onCancel:()=>{d(!1),u(null)}})]})}},552:function(e,t,r){"use strict";r.d(t,{Z:function(){return a}});var s=r(7437);function a(){return(0,s.jsxs)("nav",{className:"absolute top-0 left-0 right-0 z-20 flex items-center justify-between px-6 py-4",children:[(0,s.jsxs)("div",{className:"flex items-center gap-10",children:[(0,s.jsxs)("a",{href:"/",className:"flex items-center gap-2 text-2xl font-semibold text-white hover:opacity-90 transition-opacity",children:[(0,s.jsx)("span",{className:"inline-block w-6 h-6 rounded-sm bg-gradient-to-br from-orange-400 via-pink-500 to-blue-500"}),"Lovable"]}),(0,s.jsxs)("div",{className:"hidden md:flex items-center gap-8 text-sm text-gray-300",children:[(0,s.jsx)("a",{href:"#",className:"hover:text-white transition-colors",children:"Community"}),(0,s.jsx)("a",{href:"#",className:"hover:text-white transition-colors",children:"Enterprise"}),(0,s.jsx)("a",{href:"#",className:"hover:text-white transition-colors",children:"Learn"}),(0,s.jsx)("a",{href:"#",className:"hover:text-white transition-colors",children:"Shipped"})]})]}),(0,s.jsxs)("div",{className:"flex items-center gap-4 text-sm",children:[(0,s.jsxs)("a",{href:"/settings",className:"text-gray-300 hover:text-white transition-colors flex items-center gap-1",title:"AI Settings",children:[(0,s.jsxs)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:[(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"}),(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"})]}),"Settings"]}),(0,s.jsx)("a",{href:"#",className:"text-gray-300 hover:text-white transition-colors",children:"Log in"}),(0,s.jsx)("a",{href:"#",className:"px-4 py-2 bg-white text-black rounded-lg font-semibold hover:bg-gray-100 transition-colors",children:"Get started"})]})]})}r(2265)},2383:function(e,t,r){"use strict";r.d(t,{AIConfigProvider:function(){return c},m:function(){return m}});var s=r(7437),a=r(2265),o=r(8506);function i(e,t){switch(t.type){case"SET_CONFIG":return{...e,config:t.payload,error:null};case"ADD_PROVIDER":return{...e,config:{...e.config,providers:[...e.config.providers,t.payload]}};case"UPDATE_PROVIDER":return{...e,config:{...e.config,providers:e.config.providers.map(e=>e.id===t.payload.id?{...e,...t.payload.config}:e)}};case"REMOVE_PROVIDER":var r;let s=e.config.providers.filter(e=>e.id!==t.payload);return{...e,config:{...e.config,providers:s,defaultProvider:e.config.defaultProvider===t.payload?null===(r=s[0])||void 0===r?void 0:r.id:e.config.defaultProvider}};case"SET_DEFAULT_PROVIDER":return{...e,config:{...e.config,defaultProvider:t.payload}};case"SET_DEFAULT_MODEL":return{...e,config:{...e.config,defaultModel:t.payload}};case"UPDATE_DEFAULT_PARAMETERS":return{...e,config:{...e.config,defaultParameters:{...e.config.defaultParameters,...t.payload}}};case"TOGGLE_PROVIDER":return{...e,config:{...e.config,providers:e.config.providers.map(e=>e.id===t.payload?{...e,enabled:!e.enabled}:e)}};case"RESET_CONFIG":return{...e,config:o.T,error:null};default:return e}}let n={config:o.T,isLoading:!1,error:null},l=(0,a.createContext)(void 0),d="ai-config";function c(e){let{children:t}=e,[r,c]=(0,a.useReducer)(i,n);(0,a.useEffect)(()=>{u()},[]),(0,a.useEffect)(()=>{r.config!==o.T&&m()},[r.config]);let m=async()=>{try{localStorage.setItem(d,JSON.stringify(r.config))}catch(e){console.error("Failed to save AI config:",e)}},u=async()=>{try{let e=localStorage.getItem(d);if(e){let t=JSON.parse(e);c({type:"SET_CONFIG",payload:t})}}catch(e){console.error("Failed to load AI config:",e)}},x=async e=>{let t=r.config.providers.find(t=>t.id===e);if(!t)return{success:!1,error:"Provider not found"};try{let e=await fetch("/api/ai/test-connection",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({provider:t})});return await e.json()}catch(e){return{success:!1,error:e instanceof Error?e.message:"Connection test failed"}}};return(0,s.jsx)(l.Provider,{value:{state:r,actions:{setConfig:e=>c({type:"SET_CONFIG",payload:e}),addProvider:e=>c({type:"ADD_PROVIDER",payload:e}),updateProvider:(e,t)=>c({type:"UPDATE_PROVIDER",payload:{id:e,config:t}}),removeProvider:e=>c({type:"REMOVE_PROVIDER",payload:e}),setDefaultProvider:e=>c({type:"SET_DEFAULT_PROVIDER",payload:e}),setDefaultModel:e=>c({type:"SET_DEFAULT_MODEL",payload:e}),updateDefaultParameters:e=>c({type:"UPDATE_DEFAULT_PARAMETERS",payload:e}),toggleProvider:e=>c({type:"TOGGLE_PROVIDER",payload:e}),resetConfig:()=>c({type:"RESET_CONFIG"}),saveConfig:m,loadConfig:u,testConnection:x,validateProvider:e=>{let t=[];if(e.name.trim()||t.push("Provider name is required"),e.apiKey.trim()||t.push("API key is required"),"custom"===e.provider){var r,s;(null===(r=e.baseURL)||void 0===r?void 0:r.trim())||t.push("Base URL is required for custom providers"),(null===(s=e.modelName)||void 0===s?void 0:s.trim())||t.push("Model name is required for custom providers")}return{isValid:0===t.length,errors:t}}}},children:t})}function m(){let e=(0,a.useContext)(l);if(void 0===e)throw Error("useAIConfig must be used within an AIConfigProvider");return e}},8506:function(e,t,r){"use strict";r.d(t,{T:function(){return a},k:function(){return s}});let s={openai:[{id:"gpt-4o",name:"GPT-4o",provider:"openai",maxTokens:4096,supportsStreaming:!0,supportsTools:!0,supportsVision:!0,contextWindow:128e3},{id:"gpt-4o-mini",name:"GPT-4o Mini",provider:"openai",maxTokens:16384,supportsStreaming:!0,supportsTools:!0,supportsVision:!0,contextWindow:128e3},{id:"gpt-4-turbo",name:"GPT-4 Turbo",provider:"openai",maxTokens:4096,supportsStreaming:!0,supportsTools:!0,supportsVision:!0,contextWindow:128e3},{id:"gpt-3.5-turbo",name:"GPT-3.5 Turbo",provider:"openai",maxTokens:4096,supportsStreaming:!0,supportsTools:!0,supportsVision:!1,contextWindow:16385}],anthropic:[{id:"claude-3-5-sonnet-20241022",name:"Claude 3.5 Sonnet",provider:"anthropic",maxTokens:8192,supportsStreaming:!0,supportsTools:!0,supportsVision:!0,contextWindow:2e5},{id:"claude-3-5-haiku-20241022",name:"Claude 3.5 Haiku",provider:"anthropic",maxTokens:8192,supportsStreaming:!0,supportsTools:!0,supportsVision:!0,contextWindow:2e5},{id:"claude-3-opus-20240229",name:"Claude 3 Opus",provider:"anthropic",maxTokens:4096,supportsStreaming:!0,supportsTools:!0,supportsVision:!0,contextWindow:2e5}],google:[{id:"gemini-1.5-pro",name:"Gemini 1.5 Pro",provider:"google",maxTokens:8192,supportsStreaming:!0,supportsTools:!0,supportsVision:!0,contextWindow:2e6},{id:"gemini-1.5-flash",name:"Gemini 1.5 Flash",provider:"google",maxTokens:8192,supportsStreaming:!0,supportsTools:!0,supportsVision:!0,contextWindow:1e6}],cohere:[{id:"command-r-plus",name:"Command R+",provider:"cohere",maxTokens:4096,supportsStreaming:!0,supportsTools:!0,supportsVision:!1,contextWindow:128e3},{id:"command-r",name:"Command R",provider:"cohere",maxTokens:4096,supportsStreaming:!0,supportsTools:!0,supportsVision:!1,contextWindow:128e3}],mistral:[{id:"mistral-large-latest",name:"Mistral Large",provider:"mistral",maxTokens:8192,supportsStreaming:!0,supportsTools:!0,supportsVision:!1,contextWindow:128e3},{id:"mistral-medium-latest",name:"Mistral Medium",provider:"mistral",maxTokens:8192,supportsStreaming:!0,supportsTools:!0,supportsVision:!1,contextWindow:32e3}],custom:[]},a={providers:[],defaultParameters:{temperature:.7,maxTokens:4096,topP:1,frequencyPenalty:0,presencePenalty:0}}}},function(e){e.O(0,[971,23,744],function(){return e(e.s=1544)}),_N_E=e.O()}]);