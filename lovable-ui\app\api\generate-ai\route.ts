import { NextRequest } from "next/server";
import { AI<PERSON>rovider, ProviderConfig } from "@/lib/ai-config";
import { createAIClient, getModelParameters } from "@/lib/ai-config/client";
import { CoreMessage, streamText } from "ai";

export async function POST(req: NextRequest) {
  try {
    const { prompt, providerId, providerConfig } = await req.json();
    
    if (!prompt) {
      return new Response(
        JSON.stringify({ error: "Prompt is required" }),
        { status: 400, headers: { "Content-Type": "application/json" } }
      );
    }
    
    // We need either a providerId or a providerConfig
    if (!providerId && !providerConfig) {
      return new Response(
        JSON.stringify({ error: "Either providerId or providerConfig is required" }),
        { status: 400, headers: { "Content-Type": "application/json" } }
      );
    }
    
    console.log("[API] Starting AI generation for prompt:", prompt);
    
    // Create a streaming response
    const encoder = new TextEncoder();
    const stream = new TransformStream();
    const writer = stream.writable.getWriter();
    
    // Start the async generation
    (async () => {
      try {
        let config: ProviderConfig;
        
        // If a provider config is provided directly, use it
        if (providerConfig) {
          config = providerConfig as ProviderConfig;
        } else {
          // Otherwise, we need to get the provider from storage
          // Since this is server-side, we can't use the storage directly
          // We'll need to pass the provider config from the client
          throw new Error("Direct provider configuration is required for server-side generation");
        }
        
        // Create the AI client
        const client = createAIClient(config);
        const model = client(config.model);
        const parameters = getModelParameters(config);
        
        // Create the message
        const messages: CoreMessage[] = [
          {
            role: 'user',
            content: prompt,
          },
        ];
        
        // Stream the response
        const aiStream = await streamText({
          model,
          messages,
          ...parameters,
        });
        
        // Forward the stream to the client
        for await (const chunk of aiStream) {
          await writer.write(
            encoder.encode(`data: ${JSON.stringify({ 
              type: "text", 
              content: chunk 
            })}\n\n`)
          );
        }
        
        // Send completion signal
        await writer.write(encoder.encode("data: [DONE]\n\n"));
      } catch (error: any) {
        console.error("[API] Error during generation:", error);
        await writer.write(
          encoder.encode(`data: ${JSON.stringify({ 
            type: "error", 
            message: error.message || "Unknown error" 
          })}\n\n`)
        );
        await writer.write(encoder.encode("data: [DONE]\n\n"));
      } finally {
        await writer.close();
      }
    })();
    
    return new Response(stream.readable, {
      headers: {
        "Content-Type": "text/event-stream",
        "Cache-Control": "no-cache",
        "Connection": "keep-alive",
      },
    });
    
  } catch (error: any) {
    console.error("[API] Error:", error);
    return new Response(
      JSON.stringify({ error: error.message || "Internal server error" }),
      { status: 500, headers: { "Content-Type": "application/json" } }
    );
  }
}
