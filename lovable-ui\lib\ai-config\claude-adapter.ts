/**
 * Claude Adapter
 *
 * This file provides compatibility with the existing Claude Code SDK.
 * It adapts the new AI configuration system to work with code that expects the Claude Code SDK.
 * Now uses opencoder as the underlying implementation.
 */

import { v4 as uuidv4 } from 'uuid';
import { aiConfigManager, AIProvider } from './index';
import { query as opencoderQuery } from '../opencoder-adapter';

/**
 * Adapter function to mimic the Claude Code SDK's query function
 * This allows existing code to continue working with the new AI configuration system
 */
export async function* query(options: {
  prompt: string;
  abortController?: AbortController;
  options?: {
    maxTurns?: number;
    allowedTools?: string[];
  };
}) {
  // Use the opencoder adapter which handles the AI configuration system
  for await (const message of opencoderQuery(options)) {
    yield message;
  }
}

/**
 * Initialize the Claude adapter with an API key
 * This function creates an Anthropic provider configuration if one doesn't exist
 */
export function initializeClaudeAdapter(apiKey: string) {
  if (!apiKey) {
    console.warn('No API key provided for Claude adapter');
    return;
  }
  
  // Check if an Anthropic provider already exists
  const providers = aiConfigManager.getProviders();
  const anthropicProvider = providers.find(p => p.provider === AIProvider.ANTHROPIC);
  
  if (anthropicProvider) {
    // Update the API key if it's different
    if (anthropicProvider.apiKey !== apiKey) {
      aiConfigManager.saveProvider({
        ...anthropicProvider,
        apiKey,
      });
    }
    
    // Set as default if not already
    if (!anthropicProvider.isDefault) {
      aiConfigManager.setDefault(anthropicProvider.id!);
    }
  } else {
    // Create a new Anthropic provider
    aiConfigManager.saveProvider({
      id: uuidv4(),
      name: 'Claude',
      provider: AIProvider.ANTHROPIC,
      apiKey,
      model: 'claude-3-sonnet-20240229',
      temperature: 0.7,
      maxTokens: 4000,
      topP: 1,
      topK: 0,
      isDefault: true,
    });
  }
}
