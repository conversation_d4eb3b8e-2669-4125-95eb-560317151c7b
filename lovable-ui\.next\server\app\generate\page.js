(()=>{var e={};e.id=811,e.ids=[811],e.modules={7849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},5123:(e,r,s)=>{"use strict";s.r(r),s.d(r,{GlobalError:()=>l.a,__next_app__:()=>u,originalPathname:()=>x,pages:()=>c,routeModule:()=>p,tree:()=>d}),s(5092),s(9058),s(5866);var t=s(3191),a=s(8716),n=s(7922),l=s.n(n),i=s(5231),o={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>i[e]);s.d(r,o);let d=["",{children:["generate",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,5092)),"C:\\Users\\<USER>\\Documents\\github\\lovable-clone\\lovable-ui\\app\\generate\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,9058)),"C:\\Users\\<USER>\\Documents\\github\\lovable-clone\\lovable-ui\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,5866,23)),"next/dist/client/components/not-found-error"]}],c=["C:\\Users\\<USER>\\Documents\\github\\lovable-clone\\lovable-ui\\app\\generate\\page.tsx"],x="/generate/page",u={require:s,loadChunk:()=>Promise.resolve()},p=new t.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/generate/page",pathname:"/generate",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},4461:(e,r,s)=>{Promise.resolve().then(s.bind(s,1139))},1139:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>i});var t=s(326),a=s(7577),n=s(5047),l=s(6361);function i(){let e=(0,n.useSearchParams)();(0,n.useRouter)();let r=e.get("prompt")||"",[s,i]=(0,a.useState)([]),[o,d]=(0,a.useState)(null),[c,x]=(0,a.useState)(!1),[u,p]=(0,a.useState)(null),m=(0,a.useRef)(null);(0,a.useRef)(!1);let g=e=>{if(!e)return"";if(e.file_path)return`File: ${e.file_path}`;if(e.command)return`Command: ${e.command}`;if(e.pattern)return`Pattern: ${e.pattern}`;if(e.prompt)return`Prompt: ${e.prompt.substring(0,100)}...`;let r=Object.keys(e);if(r.length>0){let s=r[0],t=e[s];return"string"==typeof t&&t.length>100?`${s}: ${t.substring(0,100)}...`:`${s}: ${t}`}return JSON.stringify(e).substring(0,100)+"..."};return(0,t.jsxs)("main",{className:"h-screen bg-black flex flex-col overflow-hidden relative",children:[t.jsx(l.Z,{}),t.jsx("div",{className:"h-16"}),(0,t.jsxs)("div",{className:"flex-1 flex overflow-hidden",children:[(0,t.jsxs)("div",{className:"w-[30%] flex flex-col border-r border-gray-800",children:[(0,t.jsxs)("div",{className:"p-4 border-b border-gray-800",children:[t.jsx("h2",{className:"text-white font-semibold",children:"Lovable"}),t.jsx("p",{className:"text-gray-400 text-sm mt-1 break-words",children:r})]}),(0,t.jsxs)("div",{className:"flex-1 overflow-y-auto p-4 space-y-4 overflow-x-hidden",children:[s.map((e,r)=>(0,t.jsxs)("div",{children:["claude_message"===e.type&&(0,t.jsxs)("div",{className:"bg-gray-900 rounded-lg p-4",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[t.jsx("div",{className:"w-6 h-6 bg-purple-600 rounded-full flex items-center justify-center",children:t.jsx("span",{className:"text-white text-xs",children:"L"})}),t.jsx("span",{className:"text-white font-medium",children:"Lovable"})]}),t.jsx("p",{className:"text-gray-300 whitespace-pre-wrap break-words",children:e.content})]}),"tool_use"===e.type&&t.jsx("div",{className:"bg-gray-900/50 rounded-lg p-3 border border-gray-800 overflow-hidden",children:(0,t.jsxs)("div",{className:"flex items-start gap-2 text-sm",children:[(0,t.jsxs)("span",{className:"text-blue-400 flex-shrink-0",children:["\uD83D\uDD27 ",e.name]}),t.jsx("span",{className:"text-gray-500 break-all",children:g(e.input)})]})}),"progress"===e.type&&t.jsx("div",{className:"text-gray-500 text-sm font-mono break-all",children:e.message})]},r)),c&&(0,t.jsxs)("div",{className:"flex items-center gap-2 text-gray-400",children:[t.jsx("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-gray-400"}),t.jsx("span",{children:"Working..."})]}),u&&t.jsx("div",{className:"bg-red-900/20 border border-red-700 rounded-lg p-4",children:t.jsx("p",{className:"text-red-400",children:u})}),t.jsx("div",{ref:m})]}),t.jsx("div",{className:"p-4 border-t border-gray-800",children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[t.jsx("input",{type:"text",placeholder:"Ask Lovable...",className:"flex-1 px-4 py-2 bg-gray-900 text-white rounded-lg border border-gray-800 focus:outline-none focus:border-gray-700",disabled:c}),t.jsx("button",{className:"p-2 text-gray-400 hover:text-gray-300",children:t.jsx("svg",{className:"w-5 h-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:t.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 4v16m8-8H4"})})}),t.jsx("button",{className:"p-2 text-gray-400 hover:text-gray-300",children:t.jsx("svg",{className:"w-5 h-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:t.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"})})})]})})]}),(0,t.jsxs)("div",{className:"w-[70%] bg-gray-950 flex items-center justify-center",children:[!o&&c&&(0,t.jsxs)("div",{className:"text-center",children:[t.jsx("div",{className:"w-16 h-16 bg-gray-800 rounded-2xl flex items-center justify-center mb-4",children:t.jsx("div",{className:"w-12 h-12 bg-gray-700 rounded-xl animate-pulse"})}),t.jsx("p",{className:"text-gray-400",children:"Spinning up preview..."})]}),o&&t.jsx("iframe",{src:o,className:"w-full h-full",title:"Website Preview"}),!o&&!c&&t.jsx("div",{className:"text-center",children:t.jsx("p",{className:"text-gray-400",children:"Preview will appear here"})})]})]})]})}},5092:(e,r,s)=>{"use strict";s.r(r),s.d(r,{$$typeof:()=>l,__esModule:()=>n,default:()=>i});var t=s(8570);let a=(0,t.createProxy)(String.raw`C:\Users\<USER>\Documents\github\lovable-clone\lovable-ui\app\generate\page.tsx`),{__esModule:n,$$typeof:l}=a;a.default;let i=(0,t.createProxy)(String.raw`C:\Users\<USER>\Documents\github\lovable-clone\lovable-ui\app\generate\page.tsx#default`)}};var r=require("../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[948,82,860],()=>s(5123));module.exports=t})();