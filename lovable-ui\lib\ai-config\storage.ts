/**
 * AI Configuration Storage
 * 
 * This file provides utilities for storing and retrieving AI provider configurations.
 * It uses localStorage for client-side persistence.
 */

import { v4 as uuidv4 } from 'uuid';
import { AIProvider, ProviderConfig, DEFAULT_CONFIGS } from './types';

// Storage keys
const STORAGE_KEY = 'lovable_ai_providers';
const DEFAULT_PROVIDER_KEY = 'lovable_default_provider';

/**
 * Save a provider configuration to storage
 */
export const saveProviderConfig = (config: ProviderConfig): ProviderConfig => {
  // Ensure we're on the client side
  if (typeof window === 'undefined') {
    throw new Error('Cannot save provider config on server side');
  }

  // Get existing configs
  const existingConfigs = getProviderConfigs();
  
  // Generate ID if not provided
  const configWithId = {
    ...config,
    id: config.id || uuidv4(),
    updatedAt: new Date(),
    createdAt: config.createdAt || new Date(),
  };

  // Check if this is an update or new config
  const index = existingConfigs.findIndex(c => c.id === configWithId.id);
  
  if (index >= 0) {
    // Update existing config
    existingConfigs[index] = configWithId;
  } else {
    // Add new config
    existingConfigs.push(configWithId);
  }

  // Save to localStorage
  localStorage.setItem(STORAGE_KEY, JSON.stringify(existingConfigs));

  // If this is the default provider, update the default provider setting
  if (configWithId.isDefault) {
    // Set all other providers to non-default
    existingConfigs.forEach(c => {
      if (c.id !== configWithId.id) {
        c.isDefault = false;
      }
    });
    localStorage.setItem(STORAGE_KEY, JSON.stringify(existingConfigs));
    localStorage.setItem(DEFAULT_PROVIDER_KEY, configWithId.id);
  }

  return configWithId;
};

/**
 * Get all provider configurations from storage
 */
export const getProviderConfigs = (): ProviderConfig[] => {
  // Ensure we're on the client side
  if (typeof window === 'undefined') {
    return [];
  }

  try {
    const configs = localStorage.getItem(STORAGE_KEY);
    return configs ? JSON.parse(configs) : [];
  } catch (error) {
    console.error('Error retrieving provider configs:', error);
    return [];
  }
};

/**
 * Get a provider configuration by ID
 */
export const getProviderConfigById = (id: string): ProviderConfig | null => {
  const configs = getProviderConfigs();
  return configs.find(config => config.id === id) || null;
};

/**
 * Delete a provider configuration
 */
export const deleteProviderConfig = (id: string): boolean => {
  // Ensure we're on the client side
  if (typeof window === 'undefined') {
    return false;
  }

  try {
    const configs = getProviderConfigs();
    const filteredConfigs = configs.filter(config => config.id !== id);
    
    // If we're deleting the default provider, update the default
    const defaultProviderId = localStorage.getItem(DEFAULT_PROVIDER_KEY);
    if (defaultProviderId === id && filteredConfigs.length > 0) {
      filteredConfigs[0].isDefault = true;
      localStorage.setItem(DEFAULT_PROVIDER_KEY, filteredConfigs[0].id);
    }
    
    localStorage.setItem(STORAGE_KEY, JSON.stringify(filteredConfigs));
    return true;
  } catch (error) {
    console.error('Error deleting provider config:', error);
    return false;
  }
};

/**
 * Get the default provider configuration
 */
export const getDefaultProviderConfig = (): ProviderConfig | null => {
  // Ensure we're on the client side
  if (typeof window === 'undefined') {
    return null;
  }

  try {
    const defaultProviderId = localStorage.getItem(DEFAULT_PROVIDER_KEY);
    if (defaultProviderId) {
      return getProviderConfigById(defaultProviderId);
    }
    
    // If no default is set, use the first provider or return null
    const configs = getProviderConfigs();
    return configs.length > 0 ? configs[0] : null;
  } catch (error) {
    console.error('Error retrieving default provider config:', error);
    return null;
  }
};

/**
 * Set a provider as the default
 */
export const setDefaultProvider = (id: string): boolean => {
  // Ensure we're on the client side
  if (typeof window === 'undefined') {
    return false;
  }

  try {
    const configs = getProviderConfigs();
    const provider = configs.find(config => config.id === id);
    
    if (!provider) {
      return false;
    }
    
    // Update all providers
    configs.forEach(config => {
      config.isDefault = config.id === id;
    });
    
    localStorage.setItem(STORAGE_KEY, JSON.stringify(configs));
    localStorage.setItem(DEFAULT_PROVIDER_KEY, id);
    return true;
  } catch (error) {
    console.error('Error setting default provider:', error);
    return false;
  }
};

/**
 * Initialize default providers if none exist
 */
export const initializeDefaultProviders = (): void => {
  // Ensure we're on the client side
  if (typeof window === 'undefined') {
    return;
  }

  const configs = getProviderConfigs();
  
  // If no configs exist, create default ones
  if (configs.length === 0) {
    // Create a default OpenAI config
    const defaultOpenAIConfig: ProviderConfig = {
      ...DEFAULT_CONFIGS[AIProvider.OPENAI],
      id: uuidv4(),
      name: 'OpenAI',
      provider: AIProvider.OPENAI,
      apiKey: '',
      isDefault: true,
      createdAt: new Date(),
      updatedAt: new Date(),
    } as ProviderConfig;
    
    // Create a default Anthropic config
    const defaultAnthropicConfig: ProviderConfig = {
      ...DEFAULT_CONFIGS[AIProvider.ANTHROPIC],
      id: uuidv4(),
      name: 'Anthropic',
      provider: AIProvider.ANTHROPIC,
      apiKey: '',
      isDefault: false,
      createdAt: new Date(),
      updatedAt: new Date(),
    } as ProviderConfig;
    
    // Save the configs
    saveProviderConfig(defaultOpenAIConfig);
    saveProviderConfig(defaultAnthropicConfig);
    
    // Set the default provider
    localStorage.setItem(DEFAULT_PROVIDER_KEY, defaultOpenAIConfig.id);
  }
};
