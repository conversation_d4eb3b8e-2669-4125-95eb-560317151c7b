(()=>{var e={};e.id=931,e.ids=[931],e.modules={7849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},8389:(e,t,a)=>{"use strict";a.r(t),a.d(t,{GlobalError:()=>o.a,__next_app__:()=>x,originalPathname:()=>p,pages:()=>u,routeModule:()=>d,tree:()=>c}),a(908),a(9058),a(5866);var r=a(3191),s=a(8716),n=a(7922),o=a.n(n),l=a(5231),i={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>l[e]);a.d(t,i);let c=["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,908)),"C:\\Users\\<USER>\\Documents\\github\\lovable-clone\\lovable-ui\\app\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(a.bind(a,9058)),"C:\\Users\\<USER>\\Documents\\github\\lovable-clone\\lovable-ui\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,5866,23)),"next/dist/client/components/not-found-error"]}],u=["C:\\Users\\<USER>\\Documents\\github\\lovable-clone\\lovable-ui\\app\\page.tsx"],p="/page",x={require:a,loadChunk:()=>Promise.resolve()},d=new r.AppPageRouteModule({definition:{kind:s.x.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},2007:(e,t,a)=>{Promise.resolve().then(a.bind(a,8743))},8743:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>c});var r=a(326),s=a(7626),n=a.n(s),o=a(7577),l=a(5047),i=a(6361);function c(){let e=(0,l.useRouter)(),[t,a]=(0,o.useState)(""),s=()=>{t.trim()&&e.push(`/generate?prompt=${encodeURIComponent(t)}`)};return(0,r.jsxs)("main",{className:"jsx-24f01c58ae8ac726 min-h-screen relative overflow-hidden bg-black",children:[r.jsx(i.Z,{}),r.jsx("div",{style:{backgroundImage:"url('/gradient.png')"},className:"jsx-24f01c58ae8ac726 absolute inset-0 z-0 bg-cover bg-center"}),r.jsx("div",{className:"jsx-24f01c58ae8ac726 relative z-10 flex flex-col items-center justify-center min-h-screen px-4 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"jsx-24f01c58ae8ac726 max-w-4xl mx-auto text-center",children:[r.jsx("h1",{className:"jsx-24f01c58ae8ac726 text-4xl sm:text-4xl md:text-4xl font-bold text-white mb-6",children:"Build something with Lovable-clone"}),r.jsx("h3",{className:"jsx-24f01c58ae8ac726 text-xl sm:text-xl text-gray-300 mb-12 max-w-2xl mx-auto",children:"BUILT WITH CLAUDE CODE"}),r.jsx("p",{className:"jsx-24f01c58ae8ac726 text-xl sm:text-xl text-gray-300 mb-12 max-w-2xl mx-auto",children:"Turn your ideas into production-ready code in minutes. Powered by Claude's advanced AI capabilities."}),(0,r.jsxs)("div",{className:"jsx-24f01c58ae8ac726 relative max-w-2xl mx-auto",children:[(0,r.jsxs)("div",{className:"jsx-24f01c58ae8ac726 relative flex items-center bg-black rounded-2xl border border-gray-800 shadow-2xl px-2",children:[r.jsx("textarea",{placeholder:"Ask Lovable to create a prototype...",value:t,onChange:e=>a(e.target.value),onKeyDown:e=>{"Enter"!==e.key||e.shiftKey||(e.preventDefault(),s())},rows:3,className:"jsx-24f01c58ae8ac726 flex-1 px-5 py-4 bg-transparent text-white placeholder-gray-500 focus:outline-none text-lg resize-none min-h-[120px] max-h-[300px]"}),r.jsx("button",{onClick:s,disabled:!t.trim(),className:"jsx-24f01c58ae8ac726 flex-shrink-0 mr-3 p-3 bg-gray-800 hover:bg-gray-700 text-white rounded-xl focus:outline-none focus:ring-2 focus:ring-gray-600 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 group",children:r.jsx("svg",{fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",className:"jsx-24f01c58ae8ac726 h-5 w-5 group-hover:scale-110 transition-transform",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 10l7-7m0 0l7 7m-7-7v18",className:"jsx-24f01c58ae8ac726"})})})]}),(0,r.jsxs)("div",{className:"jsx-24f01c58ae8ac726 mt-8 flex flex-wrap justify-center gap-3",children:[r.jsx("button",{onClick:()=>a("Create a modern blog website with markdown support"),className:"jsx-24f01c58ae8ac726 px-4 py-2 text-sm text-gray-400 bg-gray-800/50 backdrop-blur-sm rounded-full hover:bg-gray-700/50 transition-colors border border-gray-700",children:"Blog website"}),r.jsx("button",{onClick:()=>a("Build a portfolio website with project showcase"),className:"jsx-24f01c58ae8ac726 px-4 py-2 text-sm text-gray-400 bg-gray-800/50 backdrop-blur-sm rounded-full hover:bg-gray-700/50 transition-colors border border-gray-700",children:"Portfolio site"}),r.jsx("button",{onClick:()=>a("Create an e-commerce product catalog with shopping cart"),className:"jsx-24f01c58ae8ac726 px-4 py-2 text-sm text-gray-400 bg-gray-800/50 backdrop-blur-sm rounded-full hover:bg-gray-700/50 transition-colors border border-gray-700",children:"E-commerce"}),r.jsx("button",{onClick:()=>a("Build a dashboard with charts and data visualization"),className:"jsx-24f01c58ae8ac726 px-4 py-2 text-sm text-gray-400 bg-gray-800/50 backdrop-blur-sm rounded-full hover:bg-gray-700/50 transition-colors border border-gray-700",children:"Dashboard"})]})]})]})}),r.jsx(n(),{id:"24f01c58ae8ac726",children:"@-webkit-keyframes blob{0%{-webkit-transform:translate(0px,0px)scale(1);transform:translate(0px,0px)scale(1)}33%{-webkit-transform:translate(30px,-50px)scale(1.1);transform:translate(30px,-50px)scale(1.1)}66%{-webkit-transform:translate(-20px,20px)scale(.9);transform:translate(-20px,20px)scale(.9)}100%{-webkit-transform:translate(0px,0px)scale(1);transform:translate(0px,0px)scale(1)}}@-moz-keyframes blob{0%{-moz-transform:translate(0px,0px)scale(1);transform:translate(0px,0px)scale(1)}33%{-moz-transform:translate(30px,-50px)scale(1.1);transform:translate(30px,-50px)scale(1.1)}66%{-moz-transform:translate(-20px,20px)scale(.9);transform:translate(-20px,20px)scale(.9)}100%{-moz-transform:translate(0px,0px)scale(1);transform:translate(0px,0px)scale(1)}}@-o-keyframes blob{0%{-o-transform:translate(0px,0px)scale(1);transform:translate(0px,0px)scale(1)}33%{-o-transform:translate(30px,-50px)scale(1.1);transform:translate(30px,-50px)scale(1.1)}66%{-o-transform:translate(-20px,20px)scale(.9);transform:translate(-20px,20px)scale(.9)}100%{-o-transform:translate(0px,0px)scale(1);transform:translate(0px,0px)scale(1)}}@keyframes blob{0%{-webkit-transform:translate(0px,0px)scale(1);-moz-transform:translate(0px,0px)scale(1);-o-transform:translate(0px,0px)scale(1);transform:translate(0px,0px)scale(1)}33%{-webkit-transform:translate(30px,-50px)scale(1.1);-moz-transform:translate(30px,-50px)scale(1.1);-o-transform:translate(30px,-50px)scale(1.1);transform:translate(30px,-50px)scale(1.1)}66%{-webkit-transform:translate(-20px,20px)scale(.9);-moz-transform:translate(-20px,20px)scale(.9);-o-transform:translate(-20px,20px)scale(.9);transform:translate(-20px,20px)scale(.9)}100%{-webkit-transform:translate(0px,0px)scale(1);-moz-transform:translate(0px,0px)scale(1);-o-transform:translate(0px,0px)scale(1);transform:translate(0px,0px)scale(1)}}.animate-blob.jsx-24f01c58ae8ac726{-webkit-animation:blob 7s infinite;-moz-animation:blob 7s infinite;-o-animation:blob 7s infinite;animation:blob 7s infinite}.animation-delay-2000.jsx-24f01c58ae8ac726{-webkit-animation-delay:2s;-moz-animation-delay:2s;-o-animation-delay:2s;animation-delay:2s}.animation-delay-4000.jsx-24f01c58ae8ac726{-webkit-animation-delay:4s;-moz-animation-delay:4s;-o-animation-delay:4s;animation-delay:4s}"})]})}},8472:()=>{},1596:(e,t,a)=>{"use strict";a(8472);var r=a(7577),s=function(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}(r),n="undefined"!=typeof process&&process.env&&!0,o=function(e){return"[object String]"===Object.prototype.toString.call(e)},l=function(){function e(e){var t=void 0===e?{}:e,a=t.name,r=void 0===a?"stylesheet":a,s=t.optimizeForSpeed,l=void 0===s?n:s;i(o(r),"`name` must be a string"),this._name=r,this._deletedRulePlaceholder="#"+r+"-deleted-rule____{}",i("boolean"==typeof l,"`optimizeForSpeed` must be a boolean"),this._optimizeForSpeed=l,this._serverSheet=void 0,this._tags=[],this._injected=!1,this._rulesCount=0,this._nonce=null}var t=e.prototype;return t.setOptimizeForSpeed=function(e){i("boolean"==typeof e,"`setOptimizeForSpeed` accepts a boolean"),i(0===this._rulesCount,"optimizeForSpeed cannot be when rules have already been inserted"),this.flush(),this._optimizeForSpeed=e,this.inject()},t.isOptimizeForSpeed=function(){return this._optimizeForSpeed},t.inject=function(){var e=this;i(!this._injected,"sheet already injected"),this._injected=!0,this._serverSheet={cssRules:[],insertRule:function(t,a){return"number"==typeof a?e._serverSheet.cssRules[a]={cssText:t}:e._serverSheet.cssRules.push({cssText:t}),a},deleteRule:function(t){e._serverSheet.cssRules[t]=null}}},t.getSheetForTag=function(e){if(e.sheet)return e.sheet;for(var t=0;t<document.styleSheets.length;t++)if(document.styleSheets[t].ownerNode===e)return document.styleSheets[t]},t.getSheet=function(){return this.getSheetForTag(this._tags[this._tags.length-1])},t.insertRule=function(e,t){return i(o(e),"`insertRule` accepts only strings"),"number"!=typeof t&&(t=this._serverSheet.cssRules.length),this._serverSheet.insertRule(e,t),this._rulesCount++},t.replaceRule=function(e,t){this._optimizeForSpeed;var a=this._serverSheet;if(t.trim()||(t=this._deletedRulePlaceholder),!a.cssRules[e])return e;a.deleteRule(e);try{a.insertRule(t,e)}catch(r){n||console.warn("StyleSheet: illegal rule: \n\n"+t+"\n\nSee https://stackoverflow.com/q/20007992 for more info"),a.insertRule(this._deletedRulePlaceholder,e)}return e},t.deleteRule=function(e){this._serverSheet.deleteRule(e)},t.flush=function(){this._injected=!1,this._rulesCount=0,this._serverSheet.cssRules=[]},t.cssRules=function(){return this._serverSheet.cssRules},t.makeStyleTag=function(e,t,a){t&&i(o(t),"makeStyleTag accepts only strings as second parameter");var r=document.createElement("style");this._nonce&&r.setAttribute("nonce",this._nonce),r.type="text/css",r.setAttribute("data-"+e,""),t&&r.appendChild(document.createTextNode(t));var s=document.head||document.getElementsByTagName("head")[0];return a?s.insertBefore(r,a):s.appendChild(r),r},function(e,t){for(var a=0;a<t.length;a++){var r=t[a];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}(e.prototype,[{key:"length",get:function(){return this._rulesCount}}]),e}();function i(e,t){if(!e)throw Error("StyleSheet: "+t+".")}var c=function(e){for(var t=5381,a=e.length;a;)t=33*t^e.charCodeAt(--a);return t>>>0},u={};function p(e,t){if(!t)return"jsx-"+e;var a=String(t),r=e+a;return u[r]||(u[r]="jsx-"+c(e+"-"+a)),u[r]}function x(e,t){var a=e+(t=t.replace(/\/style/gi,"\\/style"));return u[a]||(u[a]=t.replace(/__jsx-style-dynamic-selector/g,e)),u[a]}var d=r.createContext(null);d.displayName="StyleSheetContext",s.default.useInsertionEffect||s.default.useLayoutEffect;var m=void 0;function f(e){var t=m||r.useContext(d);return t&&t.add(e),null}f.dynamic=function(e){return e.map(function(e){return p(e[0],e[1])}).join(" ")},t.style=f},7626:(e,t,a)=>{"use strict";e.exports=a(1596).style},908:(e,t,a)=>{"use strict";a.r(t),a.d(t,{$$typeof:()=>o,__esModule:()=>n,default:()=>l});var r=a(8570);let s=(0,r.createProxy)(String.raw`C:\Users\<USER>\Documents\github\lovable-clone\lovable-ui\app\page.tsx`),{__esModule:n,$$typeof:o}=s;s.default;let l=(0,r.createProxy)(String.raw`C:\Users\<USER>\Documents\github\lovable-clone\lovable-ui\app\page.tsx#default`)}};var t=require("../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),r=t.X(0,[948,82,860],()=>a(8389));module.exports=r})();