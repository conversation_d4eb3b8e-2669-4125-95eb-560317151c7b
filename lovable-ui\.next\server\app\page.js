/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CUser%5CDocuments%5Cgithub%5Clovable-clone%5Clovable-ui%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CDocuments%5Cgithub%5Clovable-clone%5Clovable-ui&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CUser%5CDocuments%5Cgithub%5Clovable-clone%5Clovable-ui%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CDocuments%5Cgithub%5Clovable-clone%5Clovable-ui&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(rsc)/./app/page.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\app\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CUser%5CDocuments%5Cgithub%5Clovable-clone%5Clovable-ui%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CDocuments%5Cgithub%5Clovable-clone%5Clovable-ui&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5Cgithub%5C%5Clovable-clone%5C%5Clovable-ui%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5Cgithub%5C%5Clovable-clone%5C%5Clovable-ui%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(ssr)/./app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q1VzZXIlNUMlNUNEb2N1bWVudHMlNUMlNUNnaXRodWIlNUMlNUNsb3ZhYmxlLWNsb25lJTVDJTVDbG92YWJsZS11aSU1QyU1Q2FwcCU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSx3SUFBaUgiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9sb3ZhYmxlLXVpLz81YjRhIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcVXNlclxcXFxEb2N1bWVudHNcXFxcZ2l0aHViXFxcXGxvdmFibGUtY2xvbmVcXFxcbG92YWJsZS11aVxcXFxhcHBcXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5Cgithub%5C%5Clovable-clone%5C%5Clovable-ui%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5Cgithub%5C%5Clovable-clone%5C%5Clovable-ui%5C%5Clib%5C%5Cai-config%5C%5Ccontext.tsx%22%2C%22ids%22%3A%5B%22AIConfigProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5Cgithub%5C%5Clovable-clone%5C%5Clovable-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5Cgithub%5C%5Clovable-clone%5C%5Clovable-ui%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5Cgithub%5C%5Clovable-clone%5C%5Clovable-ui%5C%5Clib%5C%5Cai-config%5C%5Ccontext.tsx%22%2C%22ids%22%3A%5B%22AIConfigProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5Cgithub%5C%5Clovable-clone%5C%5Clovable-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5Cgithub%5C%5Clovable-clone%5C%5Clovable-ui%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./lib/ai-config/context.tsx */ \"(ssr)/./lib/ai-config/context.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q1VzZXIlNUMlNUNEb2N1bWVudHMlNUMlNUNnaXRodWIlNUMlNUNsb3ZhYmxlLWNsb25lJTVDJTVDbG92YWJsZS11aSU1QyU1Q2xpYiU1QyU1Q2FpLWNvbmZpZyU1QyU1Q2NvbnRleHQudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyQUlDb25maWdQcm92aWRlciUyMiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNVc2VyJTVDJTVDRG9jdW1lbnRzJTVDJTVDZ2l0aHViJTVDJTVDbG92YWJsZS1jbG9uZSU1QyU1Q2xvdmFibGUtdWklNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZm9udCU1QyU1Q2dvb2dsZSU1QyU1Q3RhcmdldC5jc3MlM0YlN0IlNUMlMjJwYXRoJTVDJTIyJTNBJTVDJTIyYXBwJTVDJTVDJTVDJTVDbGF5b3V0LnRzeCU1QyUyMiUyQyU1QyUyMmltcG9ydCU1QyUyMiUzQSU1QyUyMkludGVyJTVDJTIyJTJDJTVDJTIyYXJndW1lbnRzJTVDJTIyJTNBJTVCJTdCJTVDJTIyc3Vic2V0cyU1QyUyMiUzQSU1QiU1QyUyMmxhdGluJTVDJTIyJTVEJTdEJTVEJTJDJTVDJTIydmFyaWFibGVOYW1lJTVDJTIyJTNBJTVDJTIyaW50ZXIlNUMlMjIlN0QlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDVXNlciU1QyU1Q0RvY3VtZW50cyU1QyU1Q2dpdGh1YiU1QyU1Q2xvdmFibGUtY2xvbmUlNUMlNUNsb3ZhYmxlLXVpJTVDJTVDYXBwJTVDJTVDZ2xvYmFscy5jc3MlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGtLQUFxSyIsInNvdXJjZXMiOlsid2VicGFjazovL2xvdmFibGUtdWkvPzAzYjUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJBSUNvbmZpZ1Byb3ZpZGVyXCJdICovIFwiQzpcXFxcVXNlcnNcXFxcVXNlclxcXFxEb2N1bWVudHNcXFxcZ2l0aHViXFxcXGxvdmFibGUtY2xvbmVcXFxcbG92YWJsZS11aVxcXFxsaWJcXFxcYWktY29uZmlnXFxcXGNvbnRleHQudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5Cgithub%5C%5Clovable-clone%5C%5Clovable-ui%5C%5Clib%5C%5Cai-config%5C%5Ccontext.tsx%22%2C%22ids%22%3A%5B%22AIConfigProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5Cgithub%5C%5Clovable-clone%5C%5Clovable-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5Cgithub%5C%5Clovable-clone%5C%5Clovable-ui%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5Cgithub%5C%5Clovable-clone%5C%5Clovable-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5Cgithub%5C%5Clovable-clone%5C%5Clovable-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5Cgithub%5C%5Clovable-clone%5C%5Clovable-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5Cgithub%5C%5Clovable-clone%5C%5Clovable-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5Cgithub%5C%5Clovable-clone%5C%5Clovable-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5Cgithub%5C%5Clovable-clone%5C%5Clovable-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5Cgithub%5C%5Clovable-clone%5C%5Clovable-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5Cgithub%5C%5Clovable-clone%5C%5Clovable-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5Cgithub%5C%5Clovable-clone%5C%5Clovable-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5Cgithub%5C%5Clovable-clone%5C%5Clovable-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5Cgithub%5C%5Clovable-clone%5C%5Clovable-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5Cgithub%5C%5Clovable-clone%5C%5Clovable-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5Cgithub%5C%5Clovable-clone%5C%5Clovable-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5Cgithub%5C%5Clovable-clone%5C%5Clovable-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5Cgithub%5C%5Clovable-clone%5C%5Clovable-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5Cgithub%5C%5Clovable-clone%5C%5Clovable-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5Cgithub%5C%5Clovable-clone%5C%5Clovable-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5Cgithub%5C%5Clovable-clone%5C%5Clovable-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(ssr)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_Navbar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/Navbar */ \"(ssr)/./components/Navbar.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction Home() {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const [prompt, setPrompt] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const handleGenerate = ()=>{\n        if (!prompt.trim()) return;\n        // Navigate to new AI generate page with prompt\n        router.push(`/generate-ai?prompt=${encodeURIComponent(prompt)}`);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        className: \"jsx-24f01c58ae8ac726\" + \" \" + \"min-h-screen relative overflow-hidden bg-black\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Navbar__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\app\\\\page.tsx\",\n                lineNumber: 21,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    backgroundImage: \"url('/gradient.png')\"\n                },\n                className: \"jsx-24f01c58ae8ac726\" + \" \" + \"absolute inset-0 z-0 bg-cover bg-center\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\app\\\\page.tsx\",\n                lineNumber: 24,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-24f01c58ae8ac726\" + \" \" + \"relative z-10 flex flex-col items-center justify-center min-h-screen px-4 sm:px-6 lg:px-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"jsx-24f01c58ae8ac726\" + \" \" + \"max-w-4xl mx-auto text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"jsx-24f01c58ae8ac726\" + \" \" + \"text-4xl sm:text-4xl md:text-4xl font-bold text-white mb-6\",\n                            children: \"Build something with Lovable-clone\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\app\\\\page.tsx\",\n                            lineNumber: 33,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"jsx-24f01c58ae8ac726\" + \" \" + \"text-xl sm:text-xl text-gray-300 mb-12 max-w-2xl mx-auto\",\n                            children: \"POWERED BY CONFIGURABLE AI\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\app\\\\page.tsx\",\n                            lineNumber: 36,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"jsx-24f01c58ae8ac726\" + \" \" + \"text-xl sm:text-xl text-gray-300 mb-12 max-w-2xl mx-auto\",\n                            children: \"Turn your ideas into production-ready code in minutes. Choose from multiple AI providers including OpenAI, Anthropic, Google, and more.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\app\\\\page.tsx\",\n                            lineNumber: 40,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-24f01c58ae8ac726\" + \" \" + \"relative max-w-2xl mx-auto\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-24f01c58ae8ac726\" + \" \" + \"relative flex items-center bg-black rounded-2xl border border-gray-800 shadow-2xl px-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                            placeholder: \"Ask Lovable to create a prototype...\",\n                                            value: prompt,\n                                            onChange: (e)=>setPrompt(e.target.value),\n                                            onKeyDown: (e)=>{\n                                                if (e.key === \"Enter\" && !e.shiftKey) {\n                                                    e.preventDefault();\n                                                    handleGenerate();\n                                                }\n                                            },\n                                            rows: 3,\n                                            className: \"jsx-24f01c58ae8ac726\" + \" \" + \"flex-1 px-5 py-4 bg-transparent text-white placeholder-gray-500 focus:outline-none text-lg resize-none min-h-[120px] max-h-[300px]\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\app\\\\page.tsx\",\n                                            lineNumber: 49,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleGenerate,\n                                            disabled: !prompt.trim(),\n                                            className: \"jsx-24f01c58ae8ac726\" + \" \" + \"flex-shrink-0 mr-3 p-3 bg-gray-800 hover:bg-gray-700 text-white rounded-xl focus:outline-none focus:ring-2 focus:ring-gray-600 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 group\",\n                                            children:  false ? /*#__PURE__*/ 0 : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                fill: \"none\",\n                                                viewBox: \"0 0 24 24\",\n                                                stroke: \"currentColor\",\n                                                className: \"jsx-24f01c58ae8ac726\" + \" \" + \"h-5 w-5 group-hover:scale-110 transition-transform\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M5 10l7-7m0 0l7 7m-7-7v18\",\n                                                    className: \"jsx-24f01c58ae8ac726\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\app\\\\page.tsx\",\n                                                    lineNumber: 97,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\app\\\\page.tsx\",\n                                                lineNumber: 91,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\app\\\\page.tsx\",\n                                            lineNumber: 64,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\app\\\\page.tsx\",\n                                    lineNumber: 47,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-24f01c58ae8ac726\" + \" \" + \"mt-8 flex flex-wrap justify-center gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setPrompt(\"Create a modern blog website with markdown support\"),\n                                            className: \"jsx-24f01c58ae8ac726\" + \" \" + \"px-4 py-2 text-sm text-gray-400 bg-gray-800/50 backdrop-blur-sm rounded-full hover:bg-gray-700/50 transition-colors border border-gray-700\",\n                                            children: \"Blog website\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\app\\\\page.tsx\",\n                                            lineNumber: 110,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setPrompt(\"Build a portfolio website with project showcase\"),\n                                            className: \"jsx-24f01c58ae8ac726\" + \" \" + \"px-4 py-2 text-sm text-gray-400 bg-gray-800/50 backdrop-blur-sm rounded-full hover:bg-gray-700/50 transition-colors border border-gray-700\",\n                                            children: \"Portfolio site\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\app\\\\page.tsx\",\n                                            lineNumber: 120,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setPrompt(\"Create an e-commerce product catalog with shopping cart\"),\n                                            className: \"jsx-24f01c58ae8ac726\" + \" \" + \"px-4 py-2 text-sm text-gray-400 bg-gray-800/50 backdrop-blur-sm rounded-full hover:bg-gray-700/50 transition-colors border border-gray-700\",\n                                            children: \"E-commerce\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\app\\\\page.tsx\",\n                                            lineNumber: 128,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setPrompt(\"Build a dashboard with charts and data visualization\"),\n                                            className: \"jsx-24f01c58ae8ac726\" + \" \" + \"px-4 py-2 text-sm text-gray-400 bg-gray-800/50 backdrop-blur-sm rounded-full hover:bg-gray-700/50 transition-colors border border-gray-700\",\n                                            children: \"Dashboard\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\app\\\\page.tsx\",\n                                            lineNumber: 138,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\app\\\\page.tsx\",\n                                    lineNumber: 109,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\app\\\\page.tsx\",\n                            lineNumber: 46,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\app\\\\page.tsx\",\n                    lineNumber: 31,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\app\\\\page.tsx\",\n                lineNumber: 30,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"24f01c58ae8ac726\",\n                children: \"@-webkit-keyframes blob{0%{-webkit-transform:translate(0px,0px)scale(1);transform:translate(0px,0px)scale(1)}33%{-webkit-transform:translate(30px,-50px)scale(1.1);transform:translate(30px,-50px)scale(1.1)}66%{-webkit-transform:translate(-20px,20px)scale(.9);transform:translate(-20px,20px)scale(.9)}100%{-webkit-transform:translate(0px,0px)scale(1);transform:translate(0px,0px)scale(1)}}@-moz-keyframes blob{0%{-moz-transform:translate(0px,0px)scale(1);transform:translate(0px,0px)scale(1)}33%{-moz-transform:translate(30px,-50px)scale(1.1);transform:translate(30px,-50px)scale(1.1)}66%{-moz-transform:translate(-20px,20px)scale(.9);transform:translate(-20px,20px)scale(.9)}100%{-moz-transform:translate(0px,0px)scale(1);transform:translate(0px,0px)scale(1)}}@-o-keyframes blob{0%{-o-transform:translate(0px,0px)scale(1);transform:translate(0px,0px)scale(1)}33%{-o-transform:translate(30px,-50px)scale(1.1);transform:translate(30px,-50px)scale(1.1)}66%{-o-transform:translate(-20px,20px)scale(.9);transform:translate(-20px,20px)scale(.9)}100%{-o-transform:translate(0px,0px)scale(1);transform:translate(0px,0px)scale(1)}}@keyframes blob{0%{-webkit-transform:translate(0px,0px)scale(1);-moz-transform:translate(0px,0px)scale(1);-o-transform:translate(0px,0px)scale(1);transform:translate(0px,0px)scale(1)}33%{-webkit-transform:translate(30px,-50px)scale(1.1);-moz-transform:translate(30px,-50px)scale(1.1);-o-transform:translate(30px,-50px)scale(1.1);transform:translate(30px,-50px)scale(1.1)}66%{-webkit-transform:translate(-20px,20px)scale(.9);-moz-transform:translate(-20px,20px)scale(.9);-o-transform:translate(-20px,20px)scale(.9);transform:translate(-20px,20px)scale(.9)}100%{-webkit-transform:translate(0px,0px)scale(1);-moz-transform:translate(0px,0px)scale(1);-o-transform:translate(0px,0px)scale(1);transform:translate(0px,0px)scale(1)}}.animate-blob.jsx-24f01c58ae8ac726{-webkit-animation:blob 7s infinite;-moz-animation:blob 7s infinite;-o-animation:blob 7s infinite;animation:blob 7s infinite}.animation-delay-2000.jsx-24f01c58ae8ac726{-webkit-animation-delay:2s;-moz-animation-delay:2s;-o-animation-delay:2s;animation-delay:2s}.animation-delay-4000.jsx-24f01c58ae8ac726{-webkit-animation-delay:4s;-moz-animation-delay:4s;-o-animation-delay:4s;animation-delay:4s}\"\n            }, void 0, false, void 0, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\app\\\\page.tsx\",\n        lineNumber: 19,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./components/Navbar.tsx":
/*!*******************************!*\
  !*** ./components/Navbar.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Navbar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _ai_config_AISettings__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ai-config/AISettings */ \"(ssr)/./components/ai-config/AISettings.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction Navbar() {\n    const [isSettingsOpen, setIsSettingsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"absolute top-0 left-0 right-0 z-20 flex items-center justify-between px-6 py-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-10\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"/\",\n                                className: \"flex items-center gap-2 text-2xl font-semibold text-white hover:opacity-90 transition-opacity\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"inline-block w-6 h-6 rounded-sm bg-gradient-to-br from-orange-400 via-pink-500 to-blue-500\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 19,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Lovable\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 14,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"hidden md:flex items-center gap-8 text-sm text-gray-300\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#\",\n                                        className: \"hover:text-white transition-colors\",\n                                        children: \"Community\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 24,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#\",\n                                        className: \"hover:text-white transition-colors\",\n                                        children: \"Enterprise\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 27,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#\",\n                                        className: \"hover:text-white transition-colors\",\n                                        children: \"Learn\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 30,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#\",\n                                        className: \"hover:text-white transition-colors\",\n                                        children: \"Shipped\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 33,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 23,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\Navbar.tsx\",\n                        lineNumber: 13,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-4 text-sm\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setIsSettingsOpen(true),\n                                className: \"text-gray-300 hover:text-white transition-colors flex items-center gap-2\",\n                                title: \"AI Settings\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-5 h-5\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\Navbar.tsx\",\n                                                lineNumber: 52,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\Navbar.tsx\",\n                                                lineNumber: 58,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 46,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"hidden sm:inline\",\n                                        children: \"AI Settings\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 65,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 41,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"#\",\n                                className: \"text-gray-300 hover:text-white transition-colors\",\n                                children: \"Log in\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 67,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"#\",\n                                className: \"px-4 py-2 bg-white text-black rounded-lg font-semibold hover:bg-gray-100 transition-colors\",\n                                children: \"Get started\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 73,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\Navbar.tsx\",\n                        lineNumber: 40,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\Navbar.tsx\",\n                lineNumber: 11,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ai_config_AISettings__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                isOpen: isSettingsOpen,\n                onClose: ()=>setIsSettingsOpen(false)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\Navbar.tsx\",\n                lineNumber: 83,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/Navbar.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ai-config/AISettings.tsx":
/*!*********************************************!*\
  !*** ./components/ai-config/AISettings.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_ai_config_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/ai-config/context */ \"(ssr)/./lib/ai-config/context.tsx\");\n/* harmony import */ var _Modal__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./Modal */ \"(ssr)/./components/ai-config/Modal.tsx\");\n/* harmony import */ var _ProviderForm__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ProviderForm */ \"(ssr)/./components/ai-config/ProviderForm.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nconst AISettings = ({ isOpen, onClose })=>{\n    const { providers, defaultProvider, saveProvider, deleteProvider, setDefaultProvider, isLoading, error } = (0,_lib_ai_config_context__WEBPACK_IMPORTED_MODULE_2__.useAIConfig)();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"providers\");\n    const [isAddingProvider, setIsAddingProvider] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingProvider, setEditingProvider] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [confirmDelete, setConfirmDelete] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [exportData, setExportData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [importData, setImportData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [importError, setImportError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const handleAddProvider = ()=>{\n        setEditingProvider(null);\n        setIsAddingProvider(true);\n    };\n    const handleEditProvider = (provider)=>{\n        setEditingProvider(provider);\n        setIsAddingProvider(true);\n    };\n    const handleSaveProvider = async (config)=>{\n        try {\n            const result = await saveProvider(config);\n            if (result.success) {\n                setIsAddingProvider(false);\n                setEditingProvider(null);\n            } else {\n                throw new Error(result.errors?.join(\", \") || \"Failed to save provider\");\n            }\n        } catch (error) {\n            console.error(\"Error saving provider:\", error);\n            throw error;\n        }\n    };\n    const handleDeleteProvider = (id)=>{\n        setConfirmDelete(id);\n    };\n    const confirmDeleteProvider = ()=>{\n        if (confirmDelete) {\n            deleteProvider(confirmDelete);\n            setConfirmDelete(null);\n        }\n    };\n    const handleSetDefault = (id)=>{\n        setDefaultProvider(id);\n    };\n    const handleExport = ()=>{\n        try {\n            const data = JSON.stringify(providers, null, 2);\n            setExportData(data);\n        } catch (error) {\n            console.error(\"Error exporting data:\", error);\n        }\n    };\n    const handleImport = ()=>{\n        try {\n            setImportError(null);\n            const data = JSON.parse(importData);\n            if (!Array.isArray(data)) {\n                throw new Error(\"Import data must be an array of provider configurations\");\n            }\n            // Import each provider\n            data.forEach((config)=>{\n                saveProvider(config);\n            });\n            setImportData(\"\");\n        } catch (error) {\n            setImportError(error.message || \"Invalid import data\");\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Modal__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        isOpen: isOpen,\n        onClose: onClose,\n        title: \"AI Provider Settings\",\n        size: \"lg\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-b border-gray-200 mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                    className: \"-mb-px flex space-x-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setActiveTab(\"providers\"),\n                            className: `py-4 px-1 border-b-2 font-medium text-sm ${activeTab === \"providers\" ? \"border-blue-500 text-blue-600\" : \"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300\"}`,\n                            children: \"Providers\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\AISettings.tsx\",\n                            lineNumber: 112,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setActiveTab(\"import-export\"),\n                            className: `py-4 px-1 border-b-2 font-medium text-sm ${activeTab === \"import-export\" ? \"border-blue-500 text-blue-600\" : \"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300\"}`,\n                            children: \"Import/Export\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\AISettings.tsx\",\n                            lineNumber: 122,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\AISettings.tsx\",\n                    lineNumber: 111,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\AISettings.tsx\",\n                lineNumber: 110,\n                columnNumber: 7\n            }, undefined),\n            activeTab === \"providers\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: isAddingProvider ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ProviderForm__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    provider: editingProvider || undefined,\n                    onSave: handleSaveProvider,\n                    onCancel: ()=>{\n                        setIsAddingProvider(false);\n                        setEditingProvider(null);\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\AISettings.tsx\",\n                    lineNumber: 139,\n                    columnNumber: 13\n                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between items-center mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-medium text-gray-900\",\n                                    children: \"AI Providers\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\AISettings.tsx\",\n                                    lineNumber: 150,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleAddProvider,\n                                    className: \"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700\",\n                                    children: \"Add Provider\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\AISettings.tsx\",\n                                    lineNumber: 153,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\AISettings.tsx\",\n                            lineNumber: 149,\n                            columnNumber: 15\n                        }, undefined),\n                        isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center py-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\AISettings.tsx\",\n                                    lineNumber: 163,\n                                    columnNumber: 19\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: \"Loading providers...\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\AISettings.tsx\",\n                                    lineNumber: 164,\n                                    columnNumber: 19\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\AISettings.tsx\",\n                            lineNumber: 162,\n                            columnNumber: 17\n                        }, undefined) : error ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-red-50 border border-red-200 rounded-md p-4 mb-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-red-700\",\n                                children: error\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\AISettings.tsx\",\n                                lineNumber: 168,\n                                columnNumber: 19\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\AISettings.tsx\",\n                            lineNumber: 167,\n                            columnNumber: 17\n                        }, undefined) : providers.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center py-8 bg-gray-50 rounded-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-500 mb-4\",\n                                    children: \"No AI providers configured\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\AISettings.tsx\",\n                                    lineNumber: 172,\n                                    columnNumber: 19\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleAddProvider,\n                                    className: \"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700\",\n                                    children: \"Add Your First Provider\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\AISettings.tsx\",\n                                    lineNumber: 173,\n                                    columnNumber: 19\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\AISettings.tsx\",\n                            lineNumber: 171,\n                            columnNumber: 17\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: providers.map((provider)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"border rounded-lg p-4 hover:shadow-md transition-shadow\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-start\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"text-lg font-medium text-gray-900\",\n                                                                children: provider.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\AISettings.tsx\",\n                                                                lineNumber: 190,\n                                                                columnNumber: 29\n                                                            }, undefined),\n                                                            provider.isDefault && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"ml-2 px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full\",\n                                                                children: \"Default\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\AISettings.tsx\",\n                                                                lineNumber: 194,\n                                                                columnNumber: 31\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\AISettings.tsx\",\n                                                        lineNumber: 189,\n                                                        columnNumber: 27\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500 mt-1\",\n                                                        children: [\n                                                            provider.provider,\n                                                            \" • \",\n                                                            provider.model\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\AISettings.tsx\",\n                                                        lineNumber: 199,\n                                                        columnNumber: 27\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\AISettings.tsx\",\n                                                lineNumber: 188,\n                                                columnNumber: 25\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-2\",\n                                                children: [\n                                                    !provider.isDefault && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>handleSetDefault(provider.id),\n                                                        className: \"px-3 py-1 text-sm text-blue-600 border border-blue-600 rounded hover:bg-blue-50\",\n                                                        children: \"Set Default\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\AISettings.tsx\",\n                                                        lineNumber: 205,\n                                                        columnNumber: 29\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>handleEditProvider(provider),\n                                                        className: \"px-3 py-1 text-sm text-gray-600 border border-gray-300 rounded hover:bg-gray-50\",\n                                                        children: \"Edit\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\AISettings.tsx\",\n                                                        lineNumber: 212,\n                                                        columnNumber: 27\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>handleDeleteProvider(provider.id),\n                                                        className: \"px-3 py-1 text-sm text-red-600 border border-red-300 rounded hover:bg-red-50\",\n                                                        children: \"Delete\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\AISettings.tsx\",\n                                                        lineNumber: 218,\n                                                        columnNumber: 27\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\AISettings.tsx\",\n                                                lineNumber: 203,\n                                                columnNumber: 25\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\AISettings.tsx\",\n                                        lineNumber: 187,\n                                        columnNumber: 23\n                                    }, undefined)\n                                }, provider.id, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\AISettings.tsx\",\n                                    lineNumber: 183,\n                                    columnNumber: 21\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\AISettings.tsx\",\n                            lineNumber: 181,\n                            columnNumber: 17\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\AISettings.tsx\",\n                    lineNumber: 148,\n                    columnNumber: 13\n                }, undefined)\n            }, void 0, false),\n            activeTab === \"import-export\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-medium text-gray-900 mb-4\",\n                                children: \"Export Providers\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\AISettings.tsx\",\n                                lineNumber: 239,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-500 mb-4\",\n                                children: \"Export your provider configurations to save or share them.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\AISettings.tsx\",\n                                lineNumber: 242,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleExport,\n                                        className: \"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700\",\n                                        children: \"Export\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\AISettings.tsx\",\n                                        lineNumber: 246,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    exportData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>{\n                                            navigator.clipboard.writeText(exportData);\n                                        },\n                                        className: \"px-4 py-2 text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300\",\n                                        children: \"Copy to Clipboard\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\AISettings.tsx\",\n                                        lineNumber: 253,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\AISettings.tsx\",\n                                lineNumber: 245,\n                                columnNumber: 13\n                            }, undefined),\n                            exportData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                    value: exportData,\n                                    readOnly: true,\n                                    className: \"w-full h-40 p-2 border border-gray-300 rounded-md font-mono text-sm\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\AISettings.tsx\",\n                                    lineNumber: 265,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\AISettings.tsx\",\n                                lineNumber: 264,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\AISettings.tsx\",\n                        lineNumber: 238,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"border-t pt-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-medium text-gray-900 mb-4\",\n                                children: \"Import Providers\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\AISettings.tsx\",\n                                lineNumber: 276,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-500 mb-4\",\n                                children: \"Import provider configurations from JSON.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\AISettings.tsx\",\n                                lineNumber: 279,\n                                columnNumber: 13\n                            }, undefined),\n                            importError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-red-50 border border-red-200 rounded-md p-4 mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-red-700\",\n                                    children: importError\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\AISettings.tsx\",\n                                    lineNumber: 284,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\AISettings.tsx\",\n                                lineNumber: 283,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                        value: importData,\n                                        onChange: (e)=>setImportData(e.target.value),\n                                        placeholder: \"Paste your provider configuration JSON here...\",\n                                        className: \"w-full h-40 p-2 border border-gray-300 rounded-md font-mono text-sm\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\AISettings.tsx\",\n                                        lineNumber: 288,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleImport,\n                                        disabled: !importData.trim(),\n                                        className: \"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                        children: \"Import\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\AISettings.tsx\",\n                                        lineNumber: 294,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\AISettings.tsx\",\n                                lineNumber: 287,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\AISettings.tsx\",\n                        lineNumber: 275,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\AISettings.tsx\",\n                lineNumber: 236,\n                columnNumber: 9\n            }, undefined),\n            confirmDelete && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-50 overflow-y-auto bg-gray-900 bg-opacity-75 flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg p-6 max-w-md w-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-medium text-gray-900 mb-4\",\n                            children: \"Confirm Delete\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\AISettings.tsx\",\n                            lineNumber: 310,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-500 mb-6\",\n                            children: \"Are you sure you want to delete this provider? This action cannot be undone.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\AISettings.tsx\",\n                            lineNumber: 313,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setConfirmDelete(null),\n                                    className: \"px-4 py-2 text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300\",\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\AISettings.tsx\",\n                                    lineNumber: 317,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: confirmDeleteProvider,\n                                    className: \"px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700\",\n                                    children: \"Delete\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\AISettings.tsx\",\n                                    lineNumber: 323,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\AISettings.tsx\",\n                            lineNumber: 316,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\AISettings.tsx\",\n                    lineNumber: 309,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\AISettings.tsx\",\n                lineNumber: 308,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\AISettings.tsx\",\n        lineNumber: 103,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AISettings);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ai-config/AISettings.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ai-config/Modal.tsx":
/*!****************************************!*\
  !*** ./components/ai-config/Modal.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nconst Modal = ({ isOpen, onClose, title, children, size = \"md\" })=>{\n    if (!isOpen) return null;\n    const sizeClasses = {\n        sm: \"max-w-md\",\n        md: \"max-w-lg\",\n        lg: \"max-w-2xl\",\n        xl: \"max-w-4xl\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 z-50 overflow-y-auto\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"fixed inset-0 transition-opacity bg-gray-900 bg-opacity-75\",\n                    onClick: onClose,\n                    \"aria-hidden\": \"true\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\Modal.tsx\",\n                    lineNumber: 33,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"hidden sm:inline-block sm:align-middle sm:h-screen\",\n                    \"aria-hidden\": \"true\",\n                    children: \"​\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\Modal.tsx\",\n                    lineNumber: 40,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: `inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle ${sizeClasses[size]} w-full`,\n                    role: \"dialog\",\n                    \"aria-modal\": \"true\",\n                    \"aria-labelledby\": \"modal-headline\",\n                    onClick: (e)=>e.stopPropagation(),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between px-6 py-4 border-b border-gray-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-medium text-gray-900\",\n                                    id: \"modal-headline\",\n                                    children: title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\Modal.tsx\",\n                                    lineNumber: 57,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    className: \"text-gray-400 hover:text-gray-500 focus:outline-none\",\n                                    onClick: onClose,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"sr-only\",\n                                            children: \"Close\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\Modal.tsx\",\n                                            lineNumber: 68,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-6 h-6\",\n                                            fill: \"none\",\n                                            viewBox: \"0 0 24 24\",\n                                            stroke: \"currentColor\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M6 18L18 6M6 6l12 12\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\Modal.tsx\",\n                                                lineNumber: 75,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\Modal.tsx\",\n                                            lineNumber: 69,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\Modal.tsx\",\n                                    lineNumber: 63,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\Modal.tsx\",\n                            lineNumber: 56,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-6 py-4\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\Modal.tsx\",\n                            lineNumber: 86,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\Modal.tsx\",\n                    lineNumber: 48,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\Modal.tsx\",\n            lineNumber: 31,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\Modal.tsx\",\n        lineNumber: 30,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Modal);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ai-config/Modal.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ai-config/ProviderForm.tsx":
/*!***********************************************!*\
  !*** ./components/ai-config/ProviderForm.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_ai_config__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/ai-config */ \"(ssr)/./lib/ai-config/index.ts\");\n/* harmony import */ var _lib_ai_config_context__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/ai-config/context */ \"(ssr)/./lib/ai-config/context.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst ProviderForm = ({ provider, onSave, onCancel })=>{\n    const { getDefaultConfig, testProvider } = (0,_lib_ai_config_context__WEBPACK_IMPORTED_MODULE_3__.useAIConfig)();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: \"\",\n        provider: _lib_ai_config__WEBPACK_IMPORTED_MODULE_2__.AIProvider.OPENAI,\n        apiKey: \"\",\n        model: \"\",\n        temperature: 0.7,\n        maxTokens: 4000,\n        isDefault: false\n    });\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isTesting, setIsTesting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [testResult, setTestResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (provider) {\n            setFormData(provider);\n        } else {\n            // Set default values for new provider\n            const defaultConfig = getDefaultConfig(_lib_ai_config__WEBPACK_IMPORTED_MODULE_2__.AIProvider.OPENAI);\n            setFormData({\n                name: \"\",\n                provider: _lib_ai_config__WEBPACK_IMPORTED_MODULE_2__.AIProvider.OPENAI,\n                apiKey: \"\",\n                model: _lib_ai_config__WEBPACK_IMPORTED_MODULE_2__.DEFAULT_MODELS[_lib_ai_config__WEBPACK_IMPORTED_MODULE_2__.AIProvider.OPENAI][0],\n                ...defaultConfig,\n                isDefault: false\n            });\n        }\n    }, [\n        provider,\n        getDefaultConfig\n    ]);\n    const handleProviderChange = (newProvider)=>{\n        const defaultConfig = getDefaultConfig(newProvider);\n        const defaultModel = _lib_ai_config__WEBPACK_IMPORTED_MODULE_2__.DEFAULT_MODELS[newProvider][0] || \"\";\n        setFormData((prev)=>({\n                ...prev,\n                provider: newProvider,\n                model: defaultModel,\n                ...defaultConfig\n            }));\n    };\n    const handleInputChange = (field, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n        setErrors([]);\n        setTestResult(null);\n    };\n    const handleTestConnection = async ()=>{\n        if (!formData.apiKey || !formData.model) {\n            setErrors([\n                \"API key and model are required for testing\"\n            ]);\n            return;\n        }\n        setIsTesting(true);\n        setTestResult(null);\n        try {\n            const testConfig = formData;\n            const result = await testProvider(testConfig);\n            setTestResult(result);\n        } catch (error) {\n            setTestResult({\n                success: false,\n                error: error.message || \"Test failed\"\n            });\n        } finally{\n            setIsTesting(false);\n        }\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setIsLoading(true);\n        setErrors([]);\n        try {\n            await onSave(formData);\n        } catch (error) {\n            setErrors([\n                error.message || \"Failed to save provider\"\n            ]);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const availableModels = _lib_ai_config__WEBPACK_IMPORTED_MODULE_2__.DEFAULT_MODELS[formData.provider] || [];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n        onSubmit: handleSubmit,\n        className: \"space-y-6\",\n        children: [\n            errors.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-red-50 border border-red-200 rounded-md p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-red-700\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                        className: \"list-disc list-inside space-y-1\",\n                        children: errors.map((error, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: error\n                            }, index, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\ProviderForm.tsx\",\n                                lineNumber: 113,\n                                columnNumber: 17\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\ProviderForm.tsx\",\n                        lineNumber: 111,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\ProviderForm.tsx\",\n                    lineNumber: 110,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\ProviderForm.tsx\",\n                lineNumber: 109,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                children: \"Provider Name\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\ProviderForm.tsx\",\n                                lineNumber: 123,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                value: formData.name || \"\",\n                                onChange: (e)=>handleInputChange(\"name\", e.target.value),\n                                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                placeholder: \"My OpenAI Provider\",\n                                required: true\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\ProviderForm.tsx\",\n                                lineNumber: 126,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\ProviderForm.tsx\",\n                        lineNumber: 122,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                children: \"Provider Type\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\ProviderForm.tsx\",\n                                lineNumber: 137,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                value: formData.provider,\n                                onChange: (e)=>handleProviderChange(e.target.value),\n                                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: _lib_ai_config__WEBPACK_IMPORTED_MODULE_2__.AIProvider.OPENAI,\n                                        children: \"OpenAI\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\ProviderForm.tsx\",\n                                        lineNumber: 145,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: _lib_ai_config__WEBPACK_IMPORTED_MODULE_2__.AIProvider.ANTHROPIC,\n                                        children: \"Anthropic\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\ProviderForm.tsx\",\n                                        lineNumber: 146,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: _lib_ai_config__WEBPACK_IMPORTED_MODULE_2__.AIProvider.GOOGLE,\n                                        children: \"Google\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\ProviderForm.tsx\",\n                                        lineNumber: 147,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: _lib_ai_config__WEBPACK_IMPORTED_MODULE_2__.AIProvider.COHERE,\n                                        children: \"Cohere\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\ProviderForm.tsx\",\n                                        lineNumber: 148,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: _lib_ai_config__WEBPACK_IMPORTED_MODULE_2__.AIProvider.CUSTOM_OPENAI,\n                                        children: \"Custom OpenAI-Compatible\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\ProviderForm.tsx\",\n                                        lineNumber: 149,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\ProviderForm.tsx\",\n                                lineNumber: 140,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\ProviderForm.tsx\",\n                        lineNumber: 136,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                children: \"API Key\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\ProviderForm.tsx\",\n                                lineNumber: 154,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"password\",\n                                value: formData.apiKey || \"\",\n                                onChange: (e)=>handleInputChange(\"apiKey\", e.target.value),\n                                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                placeholder: \"sk-...\",\n                                required: true\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\ProviderForm.tsx\",\n                                lineNumber: 157,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\ProviderForm.tsx\",\n                        lineNumber: 153,\n                        columnNumber: 9\n                    }, undefined),\n                    formData.provider === _lib_ai_config__WEBPACK_IMPORTED_MODULE_2__.AIProvider.CUSTOM_OPENAI && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                children: \"Base URL\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\ProviderForm.tsx\",\n                                lineNumber: 169,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"url\",\n                                value: formData.baseUrl || \"\",\n                                onChange: (e)=>handleInputChange(\"baseUrl\", e.target.value),\n                                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                placeholder: \"https://api.example.com/v1\",\n                                required: true\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\ProviderForm.tsx\",\n                                lineNumber: 172,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\ProviderForm.tsx\",\n                        lineNumber: 168,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                children: \"Model\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\ProviderForm.tsx\",\n                                lineNumber: 184,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                value: formData.model || \"\",\n                                onChange: (e)=>handleInputChange(\"model\", e.target.value),\n                                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                required: true,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"\",\n                                        children: \"Select a model\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\ProviderForm.tsx\",\n                                        lineNumber: 193,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    availableModels.map((model)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: model,\n                                            children: model\n                                        }, model, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\ProviderForm.tsx\",\n                                            lineNumber: 195,\n                                            columnNumber: 15\n                                        }, undefined))\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\ProviderForm.tsx\",\n                                lineNumber: 187,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\ProviderForm.tsx\",\n                        lineNumber: 183,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\ProviderForm.tsx\",\n                lineNumber: 121,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-t pt-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"text-lg font-medium text-gray-900 mb-4\",\n                        children: \"Model Parameters\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\ProviderForm.tsx\",\n                        lineNumber: 205,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                        children: [\n                                            \"Temperature (\",\n                                            formData.temperature,\n                                            \")\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\ProviderForm.tsx\",\n                                        lineNumber: 208,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"range\",\n                                        min: \"0\",\n                                        max: \"2\",\n                                        step: \"0.1\",\n                                        value: formData.temperature || 0.7,\n                                        onChange: (e)=>handleInputChange(\"temperature\", parseFloat(e.target.value)),\n                                        className: \"w-full\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\ProviderForm.tsx\",\n                                        lineNumber: 211,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\ProviderForm.tsx\",\n                                lineNumber: 207,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                        children: \"Max Tokens\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\ProviderForm.tsx\",\n                                        lineNumber: 223,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"number\",\n                                        min: \"1\",\n                                        max: \"100000\",\n                                        value: formData.maxTokens || 4000,\n                                        onChange: (e)=>handleInputChange(\"maxTokens\", parseInt(e.target.value)),\n                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\ProviderForm.tsx\",\n                                        lineNumber: 226,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\ProviderForm.tsx\",\n                                lineNumber: 222,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\ProviderForm.tsx\",\n                        lineNumber: 206,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\ProviderForm.tsx\",\n                lineNumber: 204,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-t pt-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"text-lg font-medium text-gray-900\",\n                                children: \"Test Connection\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\ProviderForm.tsx\",\n                                lineNumber: 241,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: handleTestConnection,\n                                disabled: isTesting || !formData.apiKey || !formData.model,\n                                className: \"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                children: isTesting ? \"Testing...\" : \"Test Connection\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\ProviderForm.tsx\",\n                                lineNumber: 242,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\ProviderForm.tsx\",\n                        lineNumber: 240,\n                        columnNumber: 9\n                    }, undefined),\n                    testResult && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `p-4 rounded-md ${testResult.success ? \"bg-green-50 border border-green-200\" : \"bg-red-50 border border-red-200\"}`,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: testResult.success ? \"text-green-700\" : \"text-red-700\",\n                            children: testResult.success ? \"✓ Connection successful!\" : `✗ Connection failed: ${testResult.error}`\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\ProviderForm.tsx\",\n                            lineNumber: 254,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\ProviderForm.tsx\",\n                        lineNumber: 253,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\ProviderForm.tsx\",\n                lineNumber: 239,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-t pt-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                    className: \"flex items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"checkbox\",\n                            checked: formData.isDefault || false,\n                            onChange: (e)=>handleInputChange(\"isDefault\", e.target.checked),\n                            className: \"mr-2\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\ProviderForm.tsx\",\n                            lineNumber: 264,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-sm font-medium text-gray-700\",\n                            children: \"Set as default provider\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\ProviderForm.tsx\",\n                            lineNumber: 270,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\ProviderForm.tsx\",\n                    lineNumber: 263,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\ProviderForm.tsx\",\n                lineNumber: 262,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-end space-x-4 pt-6 border-t\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        type: \"button\",\n                        onClick: onCancel,\n                        className: \"px-4 py-2 text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300\",\n                        children: \"Cancel\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\ProviderForm.tsx\",\n                        lineNumber: 278,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        type: \"submit\",\n                        disabled: isLoading,\n                        className: \"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed\",\n                        children: isLoading ? \"Saving...\" : \"Save Provider\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\ProviderForm.tsx\",\n                        lineNumber: 285,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\ProviderForm.tsx\",\n                lineNumber: 277,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\components\\\\ai-config\\\\ProviderForm.tsx\",\n        lineNumber: 107,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ProviderForm);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ai-config/ProviderForm.tsx\n");

/***/ }),

/***/ "(ssr)/./lib/ai-config/client.ts":
/*!*********************************!*\
  !*** ./lib/ai-config/client.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createAIClient: () => (/* binding */ createAIClient),\n/* harmony export */   generateAIText: () => (/* binding */ generateAIText),\n/* harmony export */   getAvailableModels: () => (/* binding */ getAvailableModels),\n/* harmony export */   getModelParameters: () => (/* binding */ getModelParameters),\n/* harmony export */   streamAIText: () => (/* binding */ streamAIText),\n/* harmony export */   testProviderConnection: () => (/* binding */ testProviderConnection),\n/* harmony export */   validateProviderConfig: () => (/* binding */ validateProviderConfig)\n/* harmony export */ });\n/* harmony import */ var _ai_sdk_openai__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @ai-sdk/openai */ \"(ssr)/./node_modules/@ai-sdk/openai/dist/index.mjs\");\n/* harmony import */ var _ai_sdk_anthropic__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @ai-sdk/anthropic */ \"(ssr)/./node_modules/@ai-sdk/anthropic/dist/index.mjs\");\n/* harmony import */ var _ai_sdk_google__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @ai-sdk/google */ \"(ssr)/./node_modules/@ai-sdk/google/dist/index.mjs\");\n/* harmony import */ var _ai_sdk_cohere__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @ai-sdk/cohere */ \"(ssr)/./node_modules/@ai-sdk/cohere/dist/index.mjs\");\n/* harmony import */ var ai__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ai */ \"(ssr)/./node_modules/ai/dist/index.mjs\");\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./types */ \"(ssr)/./lib/ai-config/types.ts\");\n/**\r\n * AI Client Factory\r\n * \r\n * This file provides utilities for creating AI clients based on provider configurations.\r\n */ \n\n\n\n\n\n/**\r\n * Create an AI client based on the provider configuration\r\n */ const createAIClient = (config)=>{\n    switch(config.provider){\n        case _types__WEBPACK_IMPORTED_MODULE_0__.AIProvider.OPENAI:\n            return (0,_ai_sdk_openai__WEBPACK_IMPORTED_MODULE_1__.openai)({\n                apiKey: config.apiKey\n            });\n        case _types__WEBPACK_IMPORTED_MODULE_0__.AIProvider.ANTHROPIC:\n            return (0,_ai_sdk_anthropic__WEBPACK_IMPORTED_MODULE_2__.anthropic)({\n                apiKey: config.apiKey\n            });\n        case _types__WEBPACK_IMPORTED_MODULE_0__.AIProvider.GOOGLE:\n            return (0,_ai_sdk_google__WEBPACK_IMPORTED_MODULE_3__.google)({\n                apiKey: config.apiKey\n            });\n        case _types__WEBPACK_IMPORTED_MODULE_0__.AIProvider.COHERE:\n            return (0,_ai_sdk_cohere__WEBPACK_IMPORTED_MODULE_4__.cohere)({\n                apiKey: config.apiKey\n            });\n        case _types__WEBPACK_IMPORTED_MODULE_0__.AIProvider.CUSTOM_OPENAI:\n            const customConfig = config;\n            return (0,_ai_sdk_openai__WEBPACK_IMPORTED_MODULE_1__.openai)({\n                apiKey: customConfig.apiKey,\n                baseURL: customConfig.baseUrl\n            });\n        default:\n            throw new Error(`Unsupported provider: ${config.provider}`);\n    }\n};\n/**\r\n * Get model parameters based on provider configuration\r\n */ const getModelParameters = (config)=>{\n    const baseParams = {\n        temperature: config.temperature,\n        maxTokens: config.maxTokens\n    };\n    switch(config.provider){\n        case _types__WEBPACK_IMPORTED_MODULE_0__.AIProvider.OPENAI:\n        case _types__WEBPACK_IMPORTED_MODULE_0__.AIProvider.CUSTOM_OPENAI:\n            const openaiConfig = config;\n            return {\n                ...baseParams,\n                topP: openaiConfig.topP,\n                frequencyPenalty: openaiConfig.frequencyPenalty,\n                presencePenalty: openaiConfig.presencePenalty\n            };\n        case _types__WEBPACK_IMPORTED_MODULE_0__.AIProvider.ANTHROPIC:\n            const anthropicConfig = config;\n            return {\n                ...baseParams,\n                topP: anthropicConfig.topP,\n                topK: anthropicConfig.topK\n            };\n        case _types__WEBPACK_IMPORTED_MODULE_0__.AIProvider.GOOGLE:\n            const googleConfig = config;\n            return {\n                ...baseParams,\n                topP: googleConfig.topP,\n                topK: googleConfig.topK\n            };\n        case _types__WEBPACK_IMPORTED_MODULE_0__.AIProvider.COHERE:\n            const cohereConfig = config;\n            return {\n                ...baseParams,\n                topP: cohereConfig.topP,\n                frequencyPenalty: cohereConfig.frequencyPenalty,\n                presencePenalty: cohereConfig.presencePenalty\n            };\n        default:\n            return baseParams;\n    }\n};\n/**\r\n * Generate text using the configured AI provider\r\n */ const generateAIText = async (config, messages)=>{\n    try {\n        const client = createAIClient(config);\n        const model = client(config.model);\n        const parameters = getModelParameters(config);\n        const result = await (0,ai__WEBPACK_IMPORTED_MODULE_5__.generateText)({\n            model,\n            messages,\n            ...parameters\n        });\n        return result.text;\n    } catch (error) {\n        console.error(\"Error generating AI text:\", error);\n        throw new Error(`Failed to generate text with ${config.provider}: ${error.message}`);\n    }\n};\n/**\r\n * Stream text using the configured AI provider\r\n */ const streamAIText = async (config, messages)=>{\n    try {\n        const client = createAIClient(config);\n        const model = client(config.model);\n        const parameters = getModelParameters(config);\n        const result = await (0,ai__WEBPACK_IMPORTED_MODULE_5__.streamText)({\n            model,\n            messages,\n            ...parameters\n        });\n        return result;\n    } catch (error) {\n        console.error(\"Error streaming AI text:\", error);\n        throw new Error(`Failed to stream text with ${config.provider}: ${error.message}`);\n    }\n};\n/**\r\n * Test connection to an AI provider\r\n */ const testProviderConnection = async (config)=>{\n    try {\n        const testMessage = {\n            role: \"user\",\n            content: 'Hello, this is a connection test. Please respond with \"Connection successful\".'\n        };\n        const response = await generateAIText(config, [\n            testMessage\n        ]);\n        return {\n            success: true\n        };\n    } catch (error) {\n        return {\n            success: false,\n            error: error.message || \"Unknown error occurred\"\n        };\n    }\n};\n/**\r\n * Get available models for a provider (this would typically come from the provider's API)\r\n */ const getAvailableModels = async (provider, apiKey)=>{\n    // For now, return default models. In a real implementation, you might query the provider's API\n    // to get the actual available models for the given API key.\n    const { DEFAULT_MODELS } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ./types */ \"(ssr)/./lib/ai-config/types.ts\"));\n    return DEFAULT_MODELS[provider] || [];\n};\n/**\r\n * Validate provider configuration\r\n */ const validateProviderConfig = (config)=>{\n    const errors = [];\n    // Basic validation\n    if (!config.name?.trim()) {\n        errors.push(\"Name is required\");\n    }\n    if (!config.apiKey?.trim()) {\n        errors.push(\"API key is required\");\n    }\n    if (!config.model?.trim()) {\n        errors.push(\"Model is required\");\n    }\n    // Provider-specific validation\n    if (config.provider === _types__WEBPACK_IMPORTED_MODULE_0__.AIProvider.CUSTOM_OPENAI) {\n        const customConfig = config;\n        if (!customConfig.baseUrl?.trim()) {\n            errors.push(\"Base URL is required for custom OpenAI providers\");\n        } else {\n            try {\n                new URL(customConfig.baseUrl);\n            } catch  {\n                errors.push(\"Base URL must be a valid URL\");\n            }\n        }\n    }\n    // Parameter validation\n    if (config.temperature < 0 || config.temperature > 2) {\n        errors.push(\"Temperature must be between 0 and 2\");\n    }\n    if (config.maxTokens < 1 || config.maxTokens > 100000) {\n        errors.push(\"Max tokens must be between 1 and 100,000\");\n    }\n    return {\n        isValid: errors.length === 0,\n        errors\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/ai-config/client.ts\n");

/***/ }),

/***/ "(ssr)/./lib/ai-config/context.tsx":
/*!***********************************!*\
  !*** ./lib/ai-config/context.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AIConfigProvider: () => (/* binding */ AIConfigProvider),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   useAIConfig: () => (/* binding */ useAIConfig)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _index__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./index */ \"(ssr)/./lib/ai-config/index.ts\");\n/* __next_internal_client_entry_do_not_use__ AIConfigProvider,useAIConfig,default auto */ \n\n\nconst AIConfigContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst AIConfigProvider = ({ children })=>{\n    const [providers, setProviders] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [defaultProvider, setDefaultProvider] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const refreshProviders = ()=>{\n        try {\n            const allProviders = _index__WEBPACK_IMPORTED_MODULE_2__.aiConfigManager.getProviders();\n            const defaultProv = _index__WEBPACK_IMPORTED_MODULE_2__.aiConfigManager.getDefaultProvider();\n            setProviders(allProviders);\n            setDefaultProvider(defaultProv);\n            setError(null);\n        } catch (err) {\n            setError(\"Failed to load AI providers: \" + (err.message || \"Unknown error\"));\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Initialize providers on client-side only\n        if (false) {}\n    }, []);\n    const saveProvider = async (config)=>{\n        const result = await _index__WEBPACK_IMPORTED_MODULE_2__.aiConfigManager.saveProvider(config);\n        if (result.success) {\n            refreshProviders();\n        }\n        return result;\n    };\n    const deleteProviderHandler = (id)=>{\n        const result = _index__WEBPACK_IMPORTED_MODULE_2__.aiConfigManager.deleteProvider(id);\n        if (result) {\n            refreshProviders();\n        }\n        return result;\n    };\n    const setDefaultProviderHandler = (id)=>{\n        const result = _index__WEBPACK_IMPORTED_MODULE_2__.aiConfigManager.setDefault(id);\n        if (result) {\n            refreshProviders();\n        }\n        return result;\n    };\n    const testProvider = async (config)=>{\n        return _index__WEBPACK_IMPORTED_MODULE_2__.aiConfigManager.testProvider(config);\n    };\n    const getModels = async (provider, apiKey)=>{\n        return _index__WEBPACK_IMPORTED_MODULE_2__.aiConfigManager.getModels(provider, apiKey);\n    };\n    const getDefaultConfig = (provider)=>{\n        return _index__WEBPACK_IMPORTED_MODULE_2__.aiConfigManager.getDefaultConfig(provider);\n    };\n    const value = {\n        providers,\n        defaultProvider,\n        isLoading,\n        error,\n        saveProvider,\n        deleteProvider: deleteProviderHandler,\n        setDefaultProvider: setDefaultProviderHandler,\n        testProvider,\n        getModels,\n        getDefaultConfig,\n        refreshProviders\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AIConfigContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\lib\\\\ai-config\\\\context.tsx\",\n        lineNumber: 108,\n        columnNumber: 5\n    }, undefined);\n};\nconst useAIConfig = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AIConfigContext);\n    if (context === undefined) {\n        throw new Error(\"useAIConfig must be used within an AIConfigProvider\");\n    }\n    return context;\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AIConfigContext);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/ai-config/context.tsx\n");

/***/ }),

/***/ "(ssr)/./lib/ai-config/index.ts":
/*!********************************!*\
  !*** ./lib/ai-config/index.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AIConfigManager: () => (/* binding */ AIConfigManager),\n/* harmony export */   AIProvider: () => (/* reexport safe */ _types__WEBPACK_IMPORTED_MODULE_0__.AIProvider),\n/* harmony export */   AnthropicConfigSchema: () => (/* reexport safe */ _types__WEBPACK_IMPORTED_MODULE_0__.AnthropicConfigSchema),\n/* harmony export */   BaseProviderConfigSchema: () => (/* reexport safe */ _types__WEBPACK_IMPORTED_MODULE_0__.BaseProviderConfigSchema),\n/* harmony export */   CohereConfigSchema: () => (/* reexport safe */ _types__WEBPACK_IMPORTED_MODULE_0__.CohereConfigSchema),\n/* harmony export */   CustomOpenAIConfigSchema: () => (/* reexport safe */ _types__WEBPACK_IMPORTED_MODULE_0__.CustomOpenAIConfigSchema),\n/* harmony export */   DEFAULT_CONFIGS: () => (/* reexport safe */ _types__WEBPACK_IMPORTED_MODULE_0__.DEFAULT_CONFIGS),\n/* harmony export */   DEFAULT_MODELS: () => (/* reexport safe */ _types__WEBPACK_IMPORTED_MODULE_0__.DEFAULT_MODELS),\n/* harmony export */   GoogleConfigSchema: () => (/* reexport safe */ _types__WEBPACK_IMPORTED_MODULE_0__.GoogleConfigSchema),\n/* harmony export */   OpenAIConfigSchema: () => (/* reexport safe */ _types__WEBPACK_IMPORTED_MODULE_0__.OpenAIConfigSchema),\n/* harmony export */   ProviderConfigSchema: () => (/* reexport safe */ _types__WEBPACK_IMPORTED_MODULE_0__.ProviderConfigSchema),\n/* harmony export */   aiConfigManager: () => (/* binding */ aiConfigManager),\n/* harmony export */   createAIClient: () => (/* reexport safe */ _client__WEBPACK_IMPORTED_MODULE_2__.createAIClient),\n/* harmony export */   deleteProviderConfig: () => (/* reexport safe */ _storage__WEBPACK_IMPORTED_MODULE_1__.deleteProviderConfig),\n/* harmony export */   generateAIText: () => (/* reexport safe */ _client__WEBPACK_IMPORTED_MODULE_2__.generateAIText),\n/* harmony export */   getAvailableModels: () => (/* reexport safe */ _client__WEBPACK_IMPORTED_MODULE_2__.getAvailableModels),\n/* harmony export */   getDefaultProviderConfig: () => (/* reexport safe */ _storage__WEBPACK_IMPORTED_MODULE_1__.getDefaultProviderConfig),\n/* harmony export */   getModelParameters: () => (/* reexport safe */ _client__WEBPACK_IMPORTED_MODULE_2__.getModelParameters),\n/* harmony export */   getProviderConfigById: () => (/* reexport safe */ _storage__WEBPACK_IMPORTED_MODULE_1__.getProviderConfigById),\n/* harmony export */   getProviderConfigs: () => (/* reexport safe */ _storage__WEBPACK_IMPORTED_MODULE_1__.getProviderConfigs),\n/* harmony export */   initializeDefaultProviders: () => (/* reexport safe */ _storage__WEBPACK_IMPORTED_MODULE_1__.initializeDefaultProviders),\n/* harmony export */   saveProviderConfig: () => (/* reexport safe */ _storage__WEBPACK_IMPORTED_MODULE_1__.saveProviderConfig),\n/* harmony export */   setDefaultProvider: () => (/* reexport safe */ _storage__WEBPACK_IMPORTED_MODULE_1__.setDefaultProvider),\n/* harmony export */   streamAIText: () => (/* reexport safe */ _client__WEBPACK_IMPORTED_MODULE_2__.streamAIText),\n/* harmony export */   testProviderConnection: () => (/* reexport safe */ _client__WEBPACK_IMPORTED_MODULE_2__.testProviderConnection),\n/* harmony export */   validateProviderConfig: () => (/* reexport safe */ _client__WEBPACK_IMPORTED_MODULE_2__.validateProviderConfig)\n/* harmony export */ });\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./types */ \"(ssr)/./lib/ai-config/types.ts\");\n/* harmony import */ var _storage__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./storage */ \"(ssr)/./lib/ai-config/storage.ts\");\n/* harmony import */ var _client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./client */ \"(ssr)/./lib/ai-config/client.ts\");\n/**\r\n * AI Configuration Manager\r\n * \r\n * This is the main entry point for the AI configuration system.\r\n * It provides a unified interface for managing AI providers and configurations.\r\n */ \n\n\n\n\n\n/**\r\n * AI Configuration Manager Class\r\n * \r\n * This class provides a high-level interface for managing AI configurations.\r\n */ class AIConfigManager {\n    constructor(){\n        // Initialize default providers if none exist\n        if (false) {}\n    }\n    /**\r\n   * Get the singleton instance\r\n   */ static getInstance() {\n        if (!AIConfigManager.instance) {\n            AIConfigManager.instance = new AIConfigManager();\n        }\n        return AIConfigManager.instance;\n    }\n    /**\r\n   * Get all provider configurations\r\n   */ getProviders() {\n        return (0,_storage__WEBPACK_IMPORTED_MODULE_1__.getProviderConfigs)();\n    }\n    /**\r\n   * Get a provider by ID\r\n   */ getProvider(id) {\n        return (0,_storage__WEBPACK_IMPORTED_MODULE_1__.getProviderConfigById)(id);\n    }\n    /**\r\n   * Get the default provider\r\n   */ getDefaultProvider() {\n        return (0,_storage__WEBPACK_IMPORTED_MODULE_1__.getDefaultProviderConfig)();\n    }\n    /**\r\n   * Save a provider configuration\r\n   */ async saveProvider(config) {\n        try {\n            // Validate the configuration\n            const validationResult = _types__WEBPACK_IMPORTED_MODULE_0__.ProviderConfigSchema.safeParse(config);\n            if (!validationResult.success) {\n                return {\n                    success: false,\n                    errors: validationResult.error.errors.map((e)=>e.message)\n                };\n            }\n            const validatedConfig = validationResult.data;\n            // Additional validation\n            const { isValid, errors } = (0,_client__WEBPACK_IMPORTED_MODULE_2__.validateProviderConfig)(validatedConfig);\n            if (!isValid) {\n                return {\n                    success: false,\n                    errors\n                };\n            }\n            // Save the configuration\n            const savedProvider = (0,_storage__WEBPACK_IMPORTED_MODULE_1__.saveProviderConfig)(validatedConfig);\n            return {\n                success: true,\n                provider: savedProvider\n            };\n        } catch (error) {\n            return {\n                success: false,\n                errors: [\n                    error.message || \"Failed to save provider configuration\"\n                ]\n            };\n        }\n    }\n    /**\r\n   * Delete a provider configuration\r\n   */ deleteProvider(id) {\n        return (0,_storage__WEBPACK_IMPORTED_MODULE_1__.deleteProviderConfig)(id);\n    }\n    /**\r\n   * Set a provider as default\r\n   */ setDefault(id) {\n        return (0,_storage__WEBPACK_IMPORTED_MODULE_1__.setDefaultProvider)(id);\n    }\n    /**\r\n   * Test a provider connection\r\n   */ async testProvider(config) {\n        return (0,_client__WEBPACK_IMPORTED_MODULE_2__.testProviderConnection)(config);\n    }\n    /**\r\n   * Generate text using the default provider\r\n   */ async generateText(prompt) {\n        const defaultProvider = this.getDefaultProvider();\n        if (!defaultProvider) {\n            throw new Error(\"No default provider configured\");\n        }\n        const messages = [\n            {\n                role: \"user\",\n                content: prompt\n            }\n        ];\n        return (0,_client__WEBPACK_IMPORTED_MODULE_2__.generateAIText)(defaultProvider, messages);\n    }\n    /**\r\n   * Generate text using a specific provider\r\n   */ async generateTextWithProvider(providerId, prompt) {\n        const provider = this.getProvider(providerId);\n        if (!provider) {\n            throw new Error(`Provider with ID ${providerId} not found`);\n        }\n        const messages = [\n            {\n                role: \"user\",\n                content: prompt\n            }\n        ];\n        return (0,_client__WEBPACK_IMPORTED_MODULE_2__.generateAIText)(provider, messages);\n    }\n    /**\r\n   * Stream text using the default provider\r\n   */ async streamText(prompt) {\n        const defaultProvider = this.getDefaultProvider();\n        if (!defaultProvider) {\n            throw new Error(\"No default provider configured\");\n        }\n        const messages = [\n            {\n                role: \"user\",\n                content: prompt\n            }\n        ];\n        return (0,_client__WEBPACK_IMPORTED_MODULE_2__.streamAIText)(defaultProvider, messages);\n    }\n    /**\r\n   * Stream text using a specific provider\r\n   */ async streamTextWithProvider(providerId, prompt) {\n        const provider = this.getProvider(providerId);\n        if (!provider) {\n            throw new Error(`Provider with ID ${providerId} not found`);\n        }\n        const messages = [\n            {\n                role: \"user\",\n                content: prompt\n            }\n        ];\n        return (0,_client__WEBPACK_IMPORTED_MODULE_2__.streamAIText)(provider, messages);\n    }\n    /**\r\n   * Get available models for a provider\r\n   */ async getModels(provider, apiKey) {\n        return (0,_client__WEBPACK_IMPORTED_MODULE_2__.getAvailableModels)(provider, apiKey);\n    }\n    /**\r\n   * Get default configuration for a provider\r\n   */ getDefaultConfig(provider) {\n        return _types__WEBPACK_IMPORTED_MODULE_0__.DEFAULT_CONFIGS[provider];\n    }\n    /**\r\n   * Import configurations from JSON\r\n   */ importConfigurations(configs) {\n        const errors = [];\n        let imported = 0;\n        for (const config of configs){\n            try {\n                const result = _types__WEBPACK_IMPORTED_MODULE_0__.ProviderConfigSchema.safeParse(config);\n                if (result.success) {\n                    (0,_storage__WEBPACK_IMPORTED_MODULE_1__.saveProviderConfig)(result.data);\n                    imported++;\n                } else {\n                    errors.push(`Invalid configuration for ${config.name}: ${result.error.message}`);\n                }\n            } catch (error) {\n                errors.push(`Failed to import ${config.name}: ${error.message}`);\n            }\n        }\n        return {\n            success: errors.length === 0,\n            imported,\n            errors\n        };\n    }\n    /**\r\n   * Export configurations to JSON\r\n   */ exportConfigurations() {\n        return this.getProviders();\n    }\n}\n/**\r\n * Get the global AI configuration manager instance\r\n */ const aiConfigManager = AIConfigManager.getInstance();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/ai-config/index.ts\n");

/***/ }),

/***/ "(ssr)/./lib/ai-config/storage.ts":
/*!**********************************!*\
  !*** ./lib/ai-config/storage.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   deleteProviderConfig: () => (/* binding */ deleteProviderConfig),\n/* harmony export */   getDefaultProviderConfig: () => (/* binding */ getDefaultProviderConfig),\n/* harmony export */   getProviderConfigById: () => (/* binding */ getProviderConfigById),\n/* harmony export */   getProviderConfigs: () => (/* binding */ getProviderConfigs),\n/* harmony export */   initializeDefaultProviders: () => (/* binding */ initializeDefaultProviders),\n/* harmony export */   saveProviderConfig: () => (/* binding */ saveProviderConfig),\n/* harmony export */   setDefaultProvider: () => (/* binding */ setDefaultProvider)\n/* harmony export */ });\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! uuid */ \"(ssr)/./node_modules/uuid/dist/esm/v4.js\");\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./types */ \"(ssr)/./lib/ai-config/types.ts\");\n/**\r\n * AI Configuration Storage\r\n * \r\n * This file provides utilities for storing and retrieving AI provider configurations.\r\n * It uses localStorage for client-side persistence.\r\n */ \n\n// Storage keys\nconst STORAGE_KEY = \"lovable_ai_providers\";\nconst DEFAULT_PROVIDER_KEY = \"lovable_default_provider\";\n/**\r\n * Save a provider configuration to storage\r\n */ const saveProviderConfig = (config)=>{\n    // Ensure we're on the client side\n    if (true) {\n        throw new Error(\"Cannot save provider config on server side\");\n    }\n    // Get existing configs\n    const existingConfigs = getProviderConfigs();\n    // Generate ID if not provided\n    const configWithId = {\n        ...config,\n        id: config.id || (0,uuid__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(),\n        updatedAt: new Date(),\n        createdAt: config.createdAt || new Date()\n    };\n    // Check if this is an update or new config\n    const index = existingConfigs.findIndex((c)=>c.id === configWithId.id);\n    if (index >= 0) {\n        // Update existing config\n        existingConfigs[index] = configWithId;\n    } else {\n        // Add new config\n        existingConfigs.push(configWithId);\n    }\n    // Save to localStorage\n    localStorage.setItem(STORAGE_KEY, JSON.stringify(existingConfigs));\n    // If this is the default provider, update the default provider setting\n    if (configWithId.isDefault) {\n        // Set all other providers to non-default\n        existingConfigs.forEach((c)=>{\n            if (c.id !== configWithId.id) {\n                c.isDefault = false;\n            }\n        });\n        localStorage.setItem(STORAGE_KEY, JSON.stringify(existingConfigs));\n        localStorage.setItem(DEFAULT_PROVIDER_KEY, configWithId.id);\n    }\n    return configWithId;\n};\n/**\r\n * Get all provider configurations from storage\r\n */ const getProviderConfigs = ()=>{\n    // Ensure we're on the client side\n    if (true) {\n        return [];\n    }\n    try {\n        const configs = localStorage.getItem(STORAGE_KEY);\n        return configs ? JSON.parse(configs) : [];\n    } catch (error) {\n        console.error(\"Error retrieving provider configs:\", error);\n        return [];\n    }\n};\n/**\r\n * Get a provider configuration by ID\r\n */ const getProviderConfigById = (id)=>{\n    const configs = getProviderConfigs();\n    return configs.find((config)=>config.id === id) || null;\n};\n/**\r\n * Delete a provider configuration\r\n */ const deleteProviderConfig = (id)=>{\n    // Ensure we're on the client side\n    if (true) {\n        return false;\n    }\n    try {\n        const configs = getProviderConfigs();\n        const filteredConfigs = configs.filter((config)=>config.id !== id);\n        // If we're deleting the default provider, update the default\n        const defaultProviderId = localStorage.getItem(DEFAULT_PROVIDER_KEY);\n        if (defaultProviderId === id && filteredConfigs.length > 0) {\n            filteredConfigs[0].isDefault = true;\n            localStorage.setItem(DEFAULT_PROVIDER_KEY, filteredConfigs[0].id);\n        }\n        localStorage.setItem(STORAGE_KEY, JSON.stringify(filteredConfigs));\n        return true;\n    } catch (error) {\n        console.error(\"Error deleting provider config:\", error);\n        return false;\n    }\n};\n/**\r\n * Get the default provider configuration\r\n */ const getDefaultProviderConfig = ()=>{\n    // Ensure we're on the client side\n    if (true) {\n        return null;\n    }\n    try {\n        const defaultProviderId = localStorage.getItem(DEFAULT_PROVIDER_KEY);\n        if (defaultProviderId) {\n            return getProviderConfigById(defaultProviderId);\n        }\n        // If no default is set, use the first provider or return null\n        const configs = getProviderConfigs();\n        return configs.length > 0 ? configs[0] : null;\n    } catch (error) {\n        console.error(\"Error retrieving default provider config:\", error);\n        return null;\n    }\n};\n/**\r\n * Set a provider as the default\r\n */ const setDefaultProvider = (id)=>{\n    // Ensure we're on the client side\n    if (true) {\n        return false;\n    }\n    try {\n        const configs = getProviderConfigs();\n        const provider = configs.find((config)=>config.id === id);\n        if (!provider) {\n            return false;\n        }\n        // Update all providers\n        configs.forEach((config)=>{\n            config.isDefault = config.id === id;\n        });\n        localStorage.setItem(STORAGE_KEY, JSON.stringify(configs));\n        localStorage.setItem(DEFAULT_PROVIDER_KEY, id);\n        return true;\n    } catch (error) {\n        console.error(\"Error setting default provider:\", error);\n        return false;\n    }\n};\n/**\r\n * Initialize default providers if none exist\r\n */ const initializeDefaultProviders = ()=>{\n    // Ensure we're on the client side\n    if (true) {\n        return;\n    }\n    const configs = getProviderConfigs();\n    // If no configs exist, create default ones\n    if (configs.length === 0) {\n        // Create a default OpenAI config\n        const defaultOpenAIConfig = {\n            ..._types__WEBPACK_IMPORTED_MODULE_0__.DEFAULT_CONFIGS[_types__WEBPACK_IMPORTED_MODULE_0__.AIProvider.OPENAI],\n            id: (0,uuid__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(),\n            name: \"OpenAI\",\n            provider: _types__WEBPACK_IMPORTED_MODULE_0__.AIProvider.OPENAI,\n            apiKey: \"\",\n            isDefault: true,\n            createdAt: new Date(),\n            updatedAt: new Date()\n        };\n        // Create a default Anthropic config\n        const defaultAnthropicConfig = {\n            ..._types__WEBPACK_IMPORTED_MODULE_0__.DEFAULT_CONFIGS[_types__WEBPACK_IMPORTED_MODULE_0__.AIProvider.ANTHROPIC],\n            id: (0,uuid__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(),\n            name: \"Anthropic\",\n            provider: _types__WEBPACK_IMPORTED_MODULE_0__.AIProvider.ANTHROPIC,\n            apiKey: \"\",\n            isDefault: false,\n            createdAt: new Date(),\n            updatedAt: new Date()\n        };\n        // Save the configs\n        saveProviderConfig(defaultOpenAIConfig);\n        saveProviderConfig(defaultAnthropicConfig);\n        // Set the default provider\n        localStorage.setItem(DEFAULT_PROVIDER_KEY, defaultOpenAIConfig.id);\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/ai-config/storage.ts\n");

/***/ }),

/***/ "(ssr)/./lib/ai-config/types.ts":
/*!********************************!*\
  !*** ./lib/ai-config/types.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AIProvider: () => (/* binding */ AIProvider),\n/* harmony export */   AnthropicConfigSchema: () => (/* binding */ AnthropicConfigSchema),\n/* harmony export */   BaseProviderConfigSchema: () => (/* binding */ BaseProviderConfigSchema),\n/* harmony export */   CohereConfigSchema: () => (/* binding */ CohereConfigSchema),\n/* harmony export */   CustomOpenAIConfigSchema: () => (/* binding */ CustomOpenAIConfigSchema),\n/* harmony export */   DEFAULT_CONFIGS: () => (/* binding */ DEFAULT_CONFIGS),\n/* harmony export */   DEFAULT_MODELS: () => (/* binding */ DEFAULT_MODELS),\n/* harmony export */   GoogleConfigSchema: () => (/* binding */ GoogleConfigSchema),\n/* harmony export */   OpenAIConfigSchema: () => (/* binding */ OpenAIConfigSchema),\n/* harmony export */   ProviderConfigSchema: () => (/* binding */ ProviderConfigSchema)\n/* harmony export */ });\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zod */ \"(ssr)/./node_modules/zod/v3/types.js\");\n/**\r\n * AI Provider Configuration Types\r\n * \r\n * This file defines the types for configuring AI providers and models.\r\n */ \nvar AIProvider;\n(function(AIProvider) {\n    AIProvider[\"OPENAI\"] = \"openai\";\n    AIProvider[\"ANTHROPIC\"] = \"anthropic\";\n    AIProvider[\"GOOGLE\"] = \"google\";\n    AIProvider[\"COHERE\"] = \"cohere\";\n    AIProvider[\"CUSTOM_OPENAI\"] = \"custom_openai\";\n})(AIProvider || (AIProvider = {}));\n/**\r\n * Base configuration for all AI providers\r\n */ const BaseProviderConfigSchema = zod__WEBPACK_IMPORTED_MODULE_0__.object({\n    id: zod__WEBPACK_IMPORTED_MODULE_0__.string().uuid().optional(),\n    name: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(1, \"Name is required\"),\n    provider: zod__WEBPACK_IMPORTED_MODULE_0__.nativeEnum(AIProvider),\n    apiKey: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(1, \"API key is required\"),\n    isDefault: zod__WEBPACK_IMPORTED_MODULE_0__.boolean().default(false),\n    createdAt: zod__WEBPACK_IMPORTED_MODULE_0__.date().optional(),\n    updatedAt: zod__WEBPACK_IMPORTED_MODULE_0__.date().optional()\n});\n/**\r\n * OpenAI specific configuration\r\n */ const OpenAIConfigSchema = BaseProviderConfigSchema.extend({\n    provider: zod__WEBPACK_IMPORTED_MODULE_0__.literal(\"openai\"),\n    model: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(1, \"Model is required\"),\n    temperature: zod__WEBPACK_IMPORTED_MODULE_0__.number().min(0).max(2).default(0.7),\n    maxTokens: zod__WEBPACK_IMPORTED_MODULE_0__.number().min(1).max(100000).default(4000),\n    topP: zod__WEBPACK_IMPORTED_MODULE_0__.number().min(0).max(1).default(1),\n    frequencyPenalty: zod__WEBPACK_IMPORTED_MODULE_0__.number().min(-2).max(2).default(0),\n    presencePenalty: zod__WEBPACK_IMPORTED_MODULE_0__.number().min(-2).max(2).default(0)\n});\n/**\r\n * Anthropic specific configuration\r\n */ const AnthropicConfigSchema = BaseProviderConfigSchema.extend({\n    provider: zod__WEBPACK_IMPORTED_MODULE_0__.literal(\"anthropic\"),\n    model: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(1, \"Model is required\"),\n    temperature: zod__WEBPACK_IMPORTED_MODULE_0__.number().min(0).max(1).default(0.7),\n    maxTokens: zod__WEBPACK_IMPORTED_MODULE_0__.number().min(1).max(100000).default(4000),\n    topP: zod__WEBPACK_IMPORTED_MODULE_0__.number().min(0).max(1).default(1),\n    topK: zod__WEBPACK_IMPORTED_MODULE_0__.number().min(0).max(500).default(0)\n});\n/**\r\n * Google specific configuration\r\n */ const GoogleConfigSchema = BaseProviderConfigSchema.extend({\n    provider: zod__WEBPACK_IMPORTED_MODULE_0__.literal(\"google\"),\n    model: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(1, \"Model is required\"),\n    temperature: zod__WEBPACK_IMPORTED_MODULE_0__.number().min(0).max(1).default(0.7),\n    maxTokens: zod__WEBPACK_IMPORTED_MODULE_0__.number().min(1).max(100000).default(4000),\n    topP: zod__WEBPACK_IMPORTED_MODULE_0__.number().min(0).max(1).default(1),\n    topK: zod__WEBPACK_IMPORTED_MODULE_0__.number().min(0).max(500).default(0)\n});\n/**\r\n * Cohere specific configuration\r\n */ const CohereConfigSchema = BaseProviderConfigSchema.extend({\n    provider: zod__WEBPACK_IMPORTED_MODULE_0__.literal(\"cohere\"),\n    model: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(1, \"Model is required\"),\n    temperature: zod__WEBPACK_IMPORTED_MODULE_0__.number().min(0).max(2).default(0.7),\n    maxTokens: zod__WEBPACK_IMPORTED_MODULE_0__.number().min(1).max(100000).default(4000),\n    topP: zod__WEBPACK_IMPORTED_MODULE_0__.number().min(0).max(1).default(1),\n    frequencyPenalty: zod__WEBPACK_IMPORTED_MODULE_0__.number().min(0).max(1).default(0),\n    presencePenalty: zod__WEBPACK_IMPORTED_MODULE_0__.number().min(0).max(1).default(0)\n});\n/**\r\n * Custom OpenAI-compatible API configuration\r\n */ const CustomOpenAIConfigSchema = BaseProviderConfigSchema.extend({\n    provider: zod__WEBPACK_IMPORTED_MODULE_0__.literal(\"custom_openai\"),\n    baseUrl: zod__WEBPACK_IMPORTED_MODULE_0__.string().url(\"Must be a valid URL\"),\n    model: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(1, \"Model is required\"),\n    temperature: zod__WEBPACK_IMPORTED_MODULE_0__.number().min(0).max(2).default(0.7),\n    maxTokens: zod__WEBPACK_IMPORTED_MODULE_0__.number().min(1).max(100000).default(4000),\n    topP: zod__WEBPACK_IMPORTED_MODULE_0__.number().min(0).max(1).default(1),\n    frequencyPenalty: zod__WEBPACK_IMPORTED_MODULE_0__.number().min(-2).max(2).default(0),\n    presencePenalty: zod__WEBPACK_IMPORTED_MODULE_0__.number().min(-2).max(2).default(0)\n});\n/**\r\n * Union of all provider configurations\r\n */ const ProviderConfigSchema = zod__WEBPACK_IMPORTED_MODULE_0__.discriminatedUnion(\"provider\", [\n    OpenAIConfigSchema,\n    AnthropicConfigSchema,\n    GoogleConfigSchema,\n    CohereConfigSchema,\n    CustomOpenAIConfigSchema\n]);\n/**\r\n * Default models for each provider\r\n */ const DEFAULT_MODELS = {\n    [\"openai\"]: [\n        \"gpt-4o\",\n        \"gpt-4o-mini\",\n        \"gpt-4-turbo\",\n        \"gpt-4\",\n        \"gpt-3.5-turbo\"\n    ],\n    [\"anthropic\"]: [\n        \"claude-3-opus-20240229\",\n        \"claude-3-sonnet-20240229\",\n        \"claude-3-haiku-20240307\",\n        \"claude-2.1\",\n        \"claude-2.0\",\n        \"claude-instant-1.2\"\n    ],\n    [\"google\"]: [\n        \"gemini-1.5-pro\",\n        \"gemini-1.5-flash\",\n        \"gemini-1.0-pro\",\n        \"gemini-1.0-ultra\"\n    ],\n    [\"cohere\"]: [\n        \"command-r-plus\",\n        \"command-r\",\n        \"command\"\n    ],\n    [\"custom_openai\"]: []\n};\n/**\r\n * Default configurations for each provider\r\n */ const DEFAULT_CONFIGS = {\n    [\"openai\"]: {\n        provider: \"openai\",\n        model: \"gpt-4o\",\n        temperature: 0.7,\n        maxTokens: 4000,\n        topP: 1,\n        frequencyPenalty: 0,\n        presencePenalty: 0\n    },\n    [\"anthropic\"]: {\n        provider: \"anthropic\",\n        model: \"claude-3-sonnet-20240229\",\n        temperature: 0.7,\n        maxTokens: 4000,\n        topP: 1,\n        topK: 0\n    },\n    [\"google\"]: {\n        provider: \"google\",\n        model: \"gemini-1.5-pro\",\n        temperature: 0.7,\n        maxTokens: 4000,\n        topP: 1,\n        topK: 0\n    },\n    [\"cohere\"]: {\n        provider: \"cohere\",\n        model: \"command-r-plus\",\n        temperature: 0.7,\n        maxTokens: 4000,\n        topP: 1,\n        frequencyPenalty: 0,\n        presencePenalty: 0\n    },\n    [\"custom_openai\"]: {\n        provider: \"custom_openai\",\n        baseUrl: \"https://api.example.com/v1\",\n        model: \"custom-model\",\n        temperature: 0.7,\n        maxTokens: 4000,\n        topP: 1,\n        frequencyPenalty: 0,\n        presencePenalty: 0\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/ai-config/types.ts\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"ea662773702b\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9sb3ZhYmxlLXVpLy4vYXBwL2dsb2JhbHMuY3NzPzdjNTQiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCJlYTY2Mjc3MzcwMmJcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var _lib_ai_config_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/ai-config/context */ \"(rsc)/./lib/ai-config/context.tsx\");\n\n\n\n\nconst metadata = {\n    title: \"Lovable Clone - AI-Powered Code Generation\",\n    description: \"Build applications faster with AI-powered code generation\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lib_ai_config_context__WEBPACK_IMPORTED_MODULE_2__.AIConfigProvider, {\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\app\\\\layout.tsx\",\n                lineNumber: 21,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\app\\\\layout.tsx\",\n            lineNumber: 20,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\github\\\\lovable-clone\\\\lovable-ui\\\\app\\\\layout.tsx\",\n        lineNumber: 19,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbGF5b3V0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFLTUE7QUFIaUI7QUFDb0M7QUFJcEQsTUFBTUUsV0FBcUI7SUFDaENDLE9BQU87SUFDUEMsYUFBYTtBQUNmLEVBQUU7QUFFYSxTQUFTQyxXQUFXLEVBQ2pDQyxRQUFRLEVBR1I7SUFDQSxxQkFDRSw4REFBQ0M7UUFBS0MsTUFBSztrQkFDVCw0RUFBQ0M7WUFBS0MsV0FBV1YsMkpBQWU7c0JBQzlCLDRFQUFDQyxvRUFBZ0JBOzBCQUNkSzs7Ozs7Ozs7Ozs7Ozs7OztBQUtYIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbG92YWJsZS11aS8uL2FwcC9sYXlvdXQudHN4Pzk5ODgiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHR5cGUgeyBNZXRhZGF0YSB9IGZyb20gXCJuZXh0XCI7XHJcbmltcG9ydCB7IEludGVyIH0gZnJvbSBcIm5leHQvZm9udC9nb29nbGVcIjtcclxuaW1wb3J0IFwiLi9nbG9iYWxzLmNzc1wiO1xyXG5pbXBvcnQgeyBBSUNvbmZpZ1Byb3ZpZGVyIH0gZnJvbSBcIkAvbGliL2FpLWNvbmZpZy9jb250ZXh0XCI7XHJcblxyXG5jb25zdCBpbnRlciA9IEludGVyKHsgc3Vic2V0czogW1wibGF0aW5cIl0gfSk7XHJcblxyXG5leHBvcnQgY29uc3QgbWV0YWRhdGE6IE1ldGFkYXRhID0ge1xyXG4gIHRpdGxlOiBcIkxvdmFibGUgQ2xvbmUgLSBBSS1Qb3dlcmVkIENvZGUgR2VuZXJhdGlvblwiLFxyXG4gIGRlc2NyaXB0aW9uOiBcIkJ1aWxkIGFwcGxpY2F0aW9ucyBmYXN0ZXIgd2l0aCBBSS1wb3dlcmVkIGNvZGUgZ2VuZXJhdGlvblwiLFxyXG59O1xyXG5cclxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUm9vdExheW91dCh7XHJcbiAgY2hpbGRyZW4sXHJcbn06IFJlYWRvbmx5PHtcclxuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlO1xyXG59Pikge1xyXG4gIHJldHVybiAoXHJcbiAgICA8aHRtbCBsYW5nPVwiZW5cIj5cclxuICAgICAgPGJvZHkgY2xhc3NOYW1lPXtpbnRlci5jbGFzc05hbWV9PlxyXG4gICAgICAgIDxBSUNvbmZpZ1Byb3ZpZGVyPlxyXG4gICAgICAgICAge2NoaWxkcmVufVxyXG4gICAgICAgIDwvQUlDb25maWdQcm92aWRlcj5cclxuICAgICAgPC9ib2R5PlxyXG4gICAgPC9odG1sPlxyXG4gICk7XHJcbn0iXSwibmFtZXMiOlsiaW50ZXIiLCJBSUNvbmZpZ1Byb3ZpZGVyIiwibWV0YWRhdGEiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwiUm9vdExheW91dCIsImNoaWxkcmVuIiwiaHRtbCIsImxhbmciLCJib2R5IiwiY2xhc3NOYW1lIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\github\lovable-clone\lovable-ui\app\page.tsx#default`));


/***/ }),

/***/ "(rsc)/./lib/ai-config/context.tsx":
/*!***********************************!*\
  !*** ./lib/ai-config/context.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AIConfigProvider: () => (/* binding */ e0),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__),
/* harmony export */   useAIConfig: () => (/* binding */ e1)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\github\lovable-clone\lovable-ui\lib\ai-config\context.tsx#AIConfigProvider`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\github\lovable-clone\lovable-ui\lib\ai-config\context.tsx#useAIConfig`);
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\github\lovable-clone\lovable-ui\lib\ai-config\context.tsx#default`));


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@opentelemetry","vendor-chunks/zod-to-json-schema","vendor-chunks/zod","vendor-chunks/@ai-sdk","vendor-chunks/uuid","vendor-chunks/@swc","vendor-chunks/styled-jsx","vendor-chunks/nanoid","vendor-chunks/ai","vendor-chunks/secure-json-parse"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CUser%5CDocuments%5Cgithub%5Clovable-clone%5Clovable-ui%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CDocuments%5Cgithub%5Clovable-clone%5Clovable-ui&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();