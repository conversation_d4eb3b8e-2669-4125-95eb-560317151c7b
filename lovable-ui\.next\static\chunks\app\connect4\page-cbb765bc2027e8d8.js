(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[367],{3266:function(e,l,t){Promise.resolve().then(t.bind(t,4285))},4285:function(e,l,t){"use strict";t.r(l),t.d(l,{default:function(){return n}});var r=t(7437),a=t(2265);function n(){let[e,l]=(0,a.useState)(()=>Array(6).fill(null).map(()=>Array(7).fill(0))),[t,n]=(0,a.useState)(1),[s,i]=(0,a.useState)(null),[o,c]=(0,a.useState)(!1),u=(0,a.useCallback)((e,l,t,r)=>{let a=0;for(let t=0;t<7;t++)if(e[l][t]===r){if(4==++a)return!0}else a=0;a=0;for(let l=0;l<6;l++)if(e[l][t]===r){if(4==++a)return!0}else a=0;let n=Math.max(0,l-t),s=Math.max(0,t-l);a=0;for(let l=0;l<Math.min(6-n,7-s);l++)if(e[n+l][s+l]===r){if(4==++a)return!0}else a=0;let i=Math.max(0,l-(6-t)),o=Math.min(6,t+l);a=0;for(let l=0;l<Math.min(6-i,o+1);l++)if(e[i+l][o-l]===r){if(4==++a)return!0}else a=0;return!1},[]),d=(0,a.useCallback)(e=>e[0].every(e=>0!==e),[]),f=(0,a.useCallback)(r=>{if(s||o||0!==e[0][r])return;let a=e.map(e=>[...e]),f=5;for(;f>=0&&0!==a[f][r];)f--;f<0||(a[f][r]=t,l(a),u(a,f,r,t)?i(t):d(a)?c(!0):n(1===t?2:1))},[e,t,s,o,u,d]),h=(0,a.useCallback)(()=>{l(Array(6).fill(null).map(()=>Array(7).fill(0))),n(1),i(null),c(!1)},[]);return(0,r.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-gray-900 to-gray-800 flex flex-col items-center justify-center p-4",children:[(0,r.jsx)("h1",{className:"text-5xl font-bold text-white mb-8",children:"Connect 4"}),(0,r.jsx)("div",{className:"mb-6 text-center",children:s?(0,r.jsxs)("p",{className:"text-2xl font-semibold text-white",children:["Player ",s," (",1===s?"Red":"Yellow",") wins!"]}):o?(0,r.jsx)("p",{className:"text-2xl font-semibold text-white",children:"It's a draw!"}):(0,r.jsxs)("p",{className:"text-xl text-white",children:["Current player: ",1===t?"Red":"Yellow"]})}),(0,r.jsx)("div",{className:"bg-blue-600 p-4 rounded-xl shadow-2xl",children:(0,r.jsx)("div",{className:"grid grid-cols-7 gap-2",children:e.map((e,l)=>e.map((e,t)=>(0,r.jsx)("button",{onClick:()=>f(t),className:"w-16 h-16 bg-blue-800 rounded-full relative overflow-hidden hover:bg-blue-700 transition-colors",disabled:null!==s||o,children:(0,r.jsx)("div",{className:"absolute inset-2 rounded-full transition-all duration-300 ".concat(1===e?"bg-red-500 shadow-inner":2===e?"bg-yellow-400 shadow-inner":"bg-blue-900")})},"".concat(l,"-").concat(t))))})}),(0,r.jsx)("button",{onClick:h,className:"mt-8 px-6 py-3 bg-green-600 text-white font-semibold rounded-lg hover:bg-green-700 transition-colors shadow-lg",children:"New Game"}),(0,r.jsx)("a",{href:"/",className:"mt-4 text-blue-300 hover:text-blue-400 transition-colors",children:"← Back to Lovable UI"})]})}}},function(e){e.O(0,[971,23,744],function(){return e(e.s=3266)}),_N_E=e.O()}]);