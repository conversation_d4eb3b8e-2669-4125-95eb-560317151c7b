# Lovable Clone

Thank you so much for checking out this project! 🙏  
We appreciate your interest and hope you enjoy exploring and building with it.

## Getting Started

Before you begin, please make sure to **replace the API keys** in your `.env` file:

- Get your Anthropic API key from: [Anthropic Console](https://console.anthropic.com/dashboard)
- Get your Daytona API key from: [Daytona Dashboard](https://www.daytona.io/)

Add these keys to your `.env` file as follows:

``` .env
ANTHROPIC_API_KEY=your_anthropic_api_key
DAYTONA_API_KEY=your_daytona_api_key
```

## Install & Run

From the `lovable-ui` directory, install all dependencies and start the development server:


```bash
cd lovable-ui

npm install
npm run dev
```

This will launch the app locally (by default at http://localhost:3000).

