# Lovable Clone - Multi-Provider AI Code Generation

Thank you so much for checking out this project! 🙏
We appreciate your interest and hope you enjoy exploring and building with it.

## Features

✨ **Multi-Provider AI Support**: Choose from OpenAI, Anthropic, Google, Cohere, and custom OpenAI-compatible APIs
⚙️ **Dynamic Configuration**: Configure AI providers through the web interface
🔧 **Flexible Settings**: Customize model parameters like temperature, max tokens, and more
💾 **Persistent Storage**: Your configurations are saved locally in your browser
🧪 **Connection Testing**: Test your API connections before using them
📤 **Import/Export**: Backup and share your provider configurations
🪟 **Windows Compatible**: Uses OpenCoder instead of Claude Code for full Windows support

## Getting Started

### Option 1: Configure through the UI (Recommended)

1. Start the application (see installation steps below)
2. Click the "AI Settings" button in the top navigation
3. Add your AI providers with their respective API keys
4. Test the connections and set a default provider

### Option 2: Environment Variables (Optional)

You can also set up API keys via environment variables. Copy `.env.example` to `.env` and add your keys:

```bash
cp lovable-ui/.env.example lovable-ui/.env
```

Then edit the `.env` file with your API keys:

```env
# Choose the providers you want to use
ANTHROPIC_API_KEY=your_anthropic_api_key
OPENAI_API_KEY=your_openai_api_key
GOOGLE_AI_API_KEY=your_google_ai_api_key
COHERE_API_KEY=your_cohere_api_key
DAYTONA_API_KEY=your_daytona_api_key
```

### API Key Sources

- **OpenAI** (Recommended): [OpenAI Platform](https://platform.openai.com/api-keys)
- **Anthropic**: [Anthropic Console](https://console.anthropic.com/dashboard)
- **Google AI**: [Google AI Studio](https://aistudio.google.com/app/apikey)
- **Cohere**: [Cohere Dashboard](https://dashboard.cohere.ai/api-keys)
- **Daytona**: [Daytona Dashboard](https://www.daytona.io/)

### Windows Compatibility

This project uses [OpenCoder](https://github.com/ducan-ne/opencoder) instead of Claude Code for AI code generation, making it fully compatible with Windows. OpenCoder provides similar functionality to Claude Code but works across all platforms.

## Install & Run

From the `lovable-ui` directory, install all dependencies and start the development server:


```bash
cd lovable-ui

npm install
npm run dev
```

This will launch the app locally (by default at http://localhost:3000).

