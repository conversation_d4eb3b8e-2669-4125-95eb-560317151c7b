# Lovable Clone

Thank you so much for checking out this project! 🙏
We appreciate your interest and hope you enjoy exploring and building with it.

## ✨ New: Flexible AI Configuration System

This project now features a **comprehensive AI configuration system** that supports multiple AI providers:

- **OpenAI** (GPT-4o, GPT-4 Turbo, GPT-3.5 Turbo)
- **Anthropic** (Claude 3.5 Sonnet, Claude 3.5 Haiku, Claude 3 Opus)
- **Google** (Gemini 1.5 Pro, Gemini 1.5 Flash)
- **Cohere** (Command R+, Command R)
- **Mistral** (Mistral Large, Mistral Medium)
- **Custom OpenAI-compatible APIs**

### Quick Setup Options

#### Option 1: Use the Settings UI (Recommended)

1. Start the application (see installation steps below)
2. Navigate to `/settings` in your browser
3. Click "Add Provider" and configure your preferred AI provider
4. Test the connection and set as default

#### Option 2: Environment Variables (Legacy)

Create a `.env` file in the `lovable-ui` directory with your API keys:

```env
# Choose one or more providers
ANTHROPIC_API_KEY=your_anthropic_api_key
OPENAI_API_KEY=your_openai_api_key
GOOGLE_API_KEY=your_google_api_key

# Optional: Daytona integration
DAYTONA_API_KEY=your_daytona_api_key
```

**Get API Keys:**

- [Anthropic Console](https://console.anthropic.com/dashboard)
- [OpenAI Platform](https://platform.openai.com/api-keys)
- [Google AI Studio](https://aistudio.google.com/app/apikey)
- [Cohere Dashboard](https://dashboard.cohere.ai/api-keys)
- [Mistral Platform](https://console.mistral.ai/)
- [Daytona Dashboard](https://www.daytona.io/)

## Install & Run

From the `lovable-ui` directory, install all dependencies and start the development server:

```bash
cd lovable-ui

npm install
npm run dev
```

This will launch the app locally (by default at <http://localhost:3000>).

## Features

### 🤖 Multi-Provider AI Support
- Switch between different AI providers seamlessly
- Configure multiple providers simultaneously
- Test connections and validate configurations
- Persistent settings across sessions

### ⚙️ Flexible Configuration
- Dynamic model selection per provider
- Customizable parameters (temperature, max tokens, etc.)
- Support for custom OpenAI-compatible APIs
- Environment variable fallback support

### 🔧 Developer-Friendly
- TypeScript support throughout
- Comprehensive error handling and validation
- React hooks for easy integration
- Backward compatibility with existing code

### 📚 Documentation
- Complete API documentation
- Configuration examples
- Troubleshooting guides
- See `lovable-ui/docs/AI_CONFIGURATION.md` for detailed documentation

## Usage Examples

### Basic Code Generation
1. Enter your prompt on the home page
2. Click "Generate" to create your application
3. The system will use your configured default AI provider

### Advanced Configuration
1. Go to Settings (`/settings`)
2. Add multiple AI providers
3. Set different models for different use cases
4. Configure custom parameters per provider

## Architecture

The AI configuration system consists of:

- **Provider Management**: Support for multiple AI providers
- **Configuration Storage**: Persistent settings in localStorage
- **Validation System**: Comprehensive input validation
- **Error Handling**: User-friendly error messages
- **React Integration**: Context-based state management

## Contributing

We welcome contributions! Please see our contributing guidelines and feel free to submit issues or pull requests.

## License

This project is open source. Please check the license file for details.

