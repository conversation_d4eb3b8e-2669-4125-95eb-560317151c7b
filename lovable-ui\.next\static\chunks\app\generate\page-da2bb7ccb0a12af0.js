(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[811],{5062:function(e,r,t){Promise.resolve().then(t.bind(t,971))},971:function(e,r,t){"use strict";t.r(r),t.d(r,{default:function(){return l}});var s=t(7437),a=t(2265),n=t(6463),i=t(552);function l(){let e=(0,n.useSearchParams)(),r=(0,n.useRouter)(),t=e.get("prompt")||"",[l,o]=(0,a.useState)([]),[c,d]=(0,a.useState)(null),[h,x]=(0,a.useState)(!1),[u,m]=(0,a.useState)(null),f=(0,a.useRef)(null),p=(0,a.useRef)(!1),g=()=>{var e;null===(e=f.current)||void 0===e||e.scrollIntoView({behavior:"smooth"})};(0,a.useEffect)(()=>{g()},[l]),(0,a.useEffect)(()=>{if(!t){r.push("/");return}p.current||(p.current=!0,x(!0),v())},[t,r]);let v=async()=>{try{var e;let r=await fetch("/api/generate-daytona",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({prompt:t})});if(!r.ok){let e=await r.json();throw Error(e.error||"Failed to generate website")}let s=null===(e=r.body)||void 0===e?void 0:e.getReader(),a=new TextDecoder;if(!s)throw Error("No response body");for(;;){let{done:e,value:r}=await s.read();if(e)break;for(let e of a.decode(r).split("\n"))if(e.startsWith("data: ")){let r=e.slice(6);if("[DONE]"===r){x(!1);break}try{let e=JSON.parse(r);if("error"===e.type)throw Error(e.message);"complete"===e.type?(d(e.previewUrl||null),x(!1)):o(r=>[...r,e])}catch(e){}}}}catch(e){console.error("Error generating website:",e),m(e.message||"An error occurred"),x(!1)}},j=e=>{if(!e)return"";if(e.file_path)return"File: ".concat(e.file_path);if(e.command)return"Command: ".concat(e.command);if(e.pattern)return"Pattern: ".concat(e.pattern);if(e.prompt)return"Prompt: ".concat(e.prompt.substring(0,100),"...");let r=Object.keys(e);if(r.length>0){let t=r[0],s=e[t];return"string"==typeof s&&s.length>100?"".concat(t,": ").concat(s.substring(0,100),"..."):"".concat(t,": ").concat(s)}return JSON.stringify(e).substring(0,100)+"..."};return(0,s.jsxs)("main",{className:"h-screen bg-black flex flex-col overflow-hidden relative",children:[(0,s.jsx)(i.Z,{}),(0,s.jsx)("div",{className:"h-16"}),(0,s.jsxs)("div",{className:"flex-1 flex overflow-hidden",children:[(0,s.jsxs)("div",{className:"w-[30%] flex flex-col border-r border-gray-800",children:[(0,s.jsxs)("div",{className:"p-4 border-b border-gray-800",children:[(0,s.jsx)("h2",{className:"text-white font-semibold",children:"Lovable"}),(0,s.jsx)("p",{className:"text-gray-400 text-sm mt-1 break-words",children:t})]}),(0,s.jsxs)("div",{className:"flex-1 overflow-y-auto p-4 space-y-4 overflow-x-hidden",children:[l.map((e,r)=>(0,s.jsxs)("div",{children:["claude_message"===e.type&&(0,s.jsxs)("div",{className:"bg-gray-900 rounded-lg p-4",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,s.jsx)("div",{className:"w-6 h-6 bg-purple-600 rounded-full flex items-center justify-center",children:(0,s.jsx)("span",{className:"text-white text-xs",children:"L"})}),(0,s.jsx)("span",{className:"text-white font-medium",children:"Lovable"})]}),(0,s.jsx)("p",{className:"text-gray-300 whitespace-pre-wrap break-words",children:e.content})]}),"tool_use"===e.type&&(0,s.jsx)("div",{className:"bg-gray-900/50 rounded-lg p-3 border border-gray-800 overflow-hidden",children:(0,s.jsxs)("div",{className:"flex items-start gap-2 text-sm",children:[(0,s.jsxs)("span",{className:"text-blue-400 flex-shrink-0",children:["\uD83D\uDD27 ",e.name]}),(0,s.jsx)("span",{className:"text-gray-500 break-all",children:j(e.input)})]})}),"progress"===e.type&&(0,s.jsx)("div",{className:"text-gray-500 text-sm font-mono break-all",children:e.message})]},r)),h&&(0,s.jsxs)("div",{className:"flex items-center gap-2 text-gray-400",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-gray-400"}),(0,s.jsx)("span",{children:"Working..."})]}),u&&(0,s.jsx)("div",{className:"bg-red-900/20 border border-red-700 rounded-lg p-4",children:(0,s.jsx)("p",{className:"text-red-400",children:u})}),(0,s.jsx)("div",{ref:f})]}),(0,s.jsx)("div",{className:"p-4 border-t border-gray-800",children:(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("input",{type:"text",placeholder:"Ask Lovable...",className:"flex-1 px-4 py-2 bg-gray-900 text-white rounded-lg border border-gray-800 focus:outline-none focus:border-gray-700",disabled:h}),(0,s.jsx)("button",{className:"p-2 text-gray-400 hover:text-gray-300",children:(0,s.jsx)("svg",{className:"w-5 h-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 4v16m8-8H4"})})}),(0,s.jsx)("button",{className:"p-2 text-gray-400 hover:text-gray-300",children:(0,s.jsx)("svg",{className:"w-5 h-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"})})})]})})]}),(0,s.jsxs)("div",{className:"w-[70%] bg-gray-950 flex items-center justify-center",children:[!c&&h&&(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"w-16 h-16 bg-gray-800 rounded-2xl flex items-center justify-center mb-4",children:(0,s.jsx)("div",{className:"w-12 h-12 bg-gray-700 rounded-xl animate-pulse"})}),(0,s.jsx)("p",{className:"text-gray-400",children:"Spinning up preview..."})]}),c&&(0,s.jsx)("iframe",{src:c,className:"w-full h-full",title:"Website Preview"}),!c&&!h&&(0,s.jsx)("div",{className:"text-center",children:(0,s.jsx)("p",{className:"text-gray-400",children:"Preview will appear here"})})]})]})]})}},552:function(e,r,t){"use strict";t.d(r,{Z:function(){return a}});var s=t(7437);function a(){return(0,s.jsxs)("nav",{className:"absolute top-0 left-0 right-0 z-20 flex items-center justify-between px-6 py-4",children:[(0,s.jsxs)("div",{className:"flex items-center gap-10",children:[(0,s.jsxs)("a",{href:"/",className:"flex items-center gap-2 text-2xl font-semibold text-white hover:opacity-90 transition-opacity",children:[(0,s.jsx)("span",{className:"inline-block w-6 h-6 rounded-sm bg-gradient-to-br from-orange-400 via-pink-500 to-blue-500"}),"Lovable"]}),(0,s.jsxs)("div",{className:"hidden md:flex items-center gap-8 text-sm text-gray-300",children:[(0,s.jsx)("a",{href:"#",className:"hover:text-white transition-colors",children:"Community"}),(0,s.jsx)("a",{href:"#",className:"hover:text-white transition-colors",children:"Enterprise"}),(0,s.jsx)("a",{href:"#",className:"hover:text-white transition-colors",children:"Learn"}),(0,s.jsx)("a",{href:"#",className:"hover:text-white transition-colors",children:"Shipped"})]})]}),(0,s.jsxs)("div",{className:"flex items-center gap-4 text-sm",children:[(0,s.jsxs)("a",{href:"/settings",className:"text-gray-300 hover:text-white transition-colors flex items-center gap-1",title:"AI Settings",children:[(0,s.jsxs)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:[(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"}),(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"})]}),"Settings"]}),(0,s.jsx)("a",{href:"#",className:"text-gray-300 hover:text-white transition-colors",children:"Log in"}),(0,s.jsx)("a",{href:"#",className:"px-4 py-2 bg-white text-black rounded-lg font-semibold hover:bg-gray-100 transition-colors",children:"Get started"})]})]})}t(2265)},6463:function(e,r,t){"use strict";var s=t(1169);t.o(s,"useRouter")&&t.d(r,{useRouter:function(){return s.useRouter}}),t.o(s,"useSearchParams")&&t.d(r,{useSearchParams:function(){return s.useSearchParams}})}},function(e){e.O(0,[971,23,744],function(){return e(e.s=5062)}),_N_E=e.O()}]);