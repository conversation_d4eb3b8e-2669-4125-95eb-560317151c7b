'use client';

import React, { useState } from 'react';
import { useAIConfig } from '@/lib/ai-config/context';
import { ProviderConfig } from '@/lib/ai-config';
import Modal from './Modal';
import ProviderForm from './ProviderForm';

interface AISettingsProps {
  isOpen: boolean;
  onClose: () => void;
}

const AISettings: React.FC<AISettingsProps> = ({ isOpen, onClose }) => {
  const { 
    providers, 
    defaultProvider, 
    saveProvider, 
    deleteProvider, 
    setDefaultProvider,
    isLoading,
    error,
  } = useAIConfig();

  const [activeTab, setActiveTab] = useState<'providers' | 'import-export'>('providers');
  const [isAddingProvider, setIsAddingProvider] = useState(false);
  const [editingProvider, setEditingProvider] = useState<ProviderConfig | null>(null);
  const [confirmDelete, setConfirmDelete] = useState<string | null>(null);
  const [exportData, setExportData] = useState<string>('');
  const [importData, setImportData] = useState<string>('');
  const [importError, setImportError] = useState<string | null>(null);

  const handleAddProvider = () => {
    setEditingProvider(null);
    setIsAddingProvider(true);
  };

  const handleEditProvider = (provider: ProviderConfig) => {
    setEditingProvider(provider);
    setIsAddingProvider(true);
  };

  const handleSaveProvider = async (config: Partial<ProviderConfig>) => {
    try {
      const result = await saveProvider(config);
      if (result.success) {
        setIsAddingProvider(false);
        setEditingProvider(null);
      } else {
        throw new Error(result.errors?.join(', ') || 'Failed to save provider');
      }
    } catch (error) {
      console.error('Error saving provider:', error);
      throw error;
    }
  };

  const handleDeleteProvider = (id: string) => {
    setConfirmDelete(id);
  };

  const confirmDeleteProvider = () => {
    if (confirmDelete) {
      deleteProvider(confirmDelete);
      setConfirmDelete(null);
    }
  };

  const handleSetDefault = (id: string) => {
    setDefaultProvider(id);
  };

  const handleExport = () => {
    try {
      const data = JSON.stringify(providers, null, 2);
      setExportData(data);
    } catch (error) {
      console.error('Error exporting data:', error);
    }
  };

  const handleImport = () => {
    try {
      setImportError(null);
      const data = JSON.parse(importData);
      
      if (!Array.isArray(data)) {
        throw new Error('Import data must be an array of provider configurations');
      }
      
      // Import each provider
      data.forEach(config => {
        saveProvider(config);
      });
      
      setImportData('');
    } catch (error) {
      setImportError(error.message || 'Invalid import data');
    }
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title="AI Provider Settings"
      size="lg"
    >
      {/* Tabs */}
      <div className="border-b border-gray-200 mb-6">
        <nav className="-mb-px flex space-x-8">
          <button
            onClick={() => setActiveTab('providers')}
            className={`py-4 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'providers'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            Providers
          </button>
          <button
            onClick={() => setActiveTab('import-export')}
            className={`py-4 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'import-export'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            Import/Export
          </button>
        </nav>
      </div>

      {/* Content */}
      {activeTab === 'providers' && (
        <>
          {isAddingProvider ? (
            <ProviderForm
              provider={editingProvider || undefined}
              onSave={handleSaveProvider}
              onCancel={() => {
                setIsAddingProvider(false);
                setEditingProvider(null);
              }}
            />
          ) : (
            <div>
              <div className="flex justify-between items-center mb-6">
                <h3 className="text-lg font-medium text-gray-900">
                  AI Providers
                </h3>
                <button
                  onClick={handleAddProvider}
                  className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                >
                  Add Provider
                </button>
              </div>

              {isLoading ? (
                <div className="text-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-4"></div>
                  <p>Loading providers...</p>
                </div>
              ) : error ? (
                <div className="bg-red-50 border border-red-200 rounded-md p-4 mb-6">
                  <div className="text-red-700">{error}</div>
                </div>
              ) : providers.length === 0 ? (
                <div className="text-center py-8 bg-gray-50 rounded-lg">
                  <p className="text-gray-500 mb-4">No AI providers configured</p>
                  <button
                    onClick={handleAddProvider}
                    className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                  >
                    Add Your First Provider
                  </button>
                </div>
              ) : (
                <div className="space-y-4">
                  {providers.map((provider) => (
                    <div
                      key={provider.id}
                      className="border rounded-lg p-4 hover:shadow-md transition-shadow"
                    >
                      <div className="flex justify-between items-start">
                        <div>
                          <div className="flex items-center">
                            <h4 className="text-lg font-medium text-gray-900">
                              {provider.name}
                            </h4>
                            {provider.isDefault && (
                              <span className="ml-2 px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">
                                Default
                              </span>
                            )}
                          </div>
                          <p className="text-sm text-gray-500 mt-1">
                            {provider.provider} • {provider.model}
                          </p>
                        </div>
                        <div className="flex space-x-2">
                          {!provider.isDefault && (
                            <button
                              onClick={() => handleSetDefault(provider.id!)}
                              className="px-3 py-1 text-sm text-blue-600 border border-blue-600 rounded hover:bg-blue-50"
                            >
                              Set Default
                            </button>
                          )}
                          <button
                            onClick={() => handleEditProvider(provider)}
                            className="px-3 py-1 text-sm text-gray-600 border border-gray-300 rounded hover:bg-gray-50"
                          >
                            Edit
                          </button>
                          <button
                            onClick={() => handleDeleteProvider(provider.id!)}
                            className="px-3 py-1 text-sm text-red-600 border border-red-300 rounded hover:bg-red-50"
                          >
                            Delete
                          </button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}
        </>
      )}

      {activeTab === 'import-export' && (
        <div className="space-y-8">
          {/* Export */}
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-4">
              Export Providers
            </h3>
            <p className="text-sm text-gray-500 mb-4">
              Export your provider configurations to save or share them.
            </p>
            <div className="flex space-x-4">
              <button
                onClick={handleExport}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
              >
                Export
              </button>
              {exportData && (
                <button
                  onClick={() => {
                    navigator.clipboard.writeText(exportData);
                  }}
                  className="px-4 py-2 text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300"
                >
                  Copy to Clipboard
                </button>
              )}
            </div>
            {exportData && (
              <div className="mt-4">
                <textarea
                  value={exportData}
                  readOnly
                  className="w-full h-40 p-2 border border-gray-300 rounded-md font-mono text-sm"
                />
              </div>
            )}
          </div>

          {/* Import */}
          <div className="border-t pt-8">
            <h3 className="text-lg font-medium text-gray-900 mb-4">
              Import Providers
            </h3>
            <p className="text-sm text-gray-500 mb-4">
              Import provider configurations from JSON.
            </p>
            {importError && (
              <div className="bg-red-50 border border-red-200 rounded-md p-4 mb-4">
                <div className="text-red-700">{importError}</div>
              </div>
            )}
            <div className="space-y-4">
              <textarea
                value={importData}
                onChange={(e) => setImportData(e.target.value)}
                placeholder="Paste your provider configuration JSON here..."
                className="w-full h-40 p-2 border border-gray-300 rounded-md font-mono text-sm"
              />
              <button
                onClick={handleImport}
                disabled={!importData.trim()}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Import
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Delete Confirmation Modal */}
      {confirmDelete && (
        <div className="fixed inset-0 z-50 overflow-y-auto bg-gray-900 bg-opacity-75 flex items-center justify-center">
          <div className="bg-white rounded-lg p-6 max-w-md w-full">
            <h3 className="text-lg font-medium text-gray-900 mb-4">
              Confirm Delete
            </h3>
            <p className="text-gray-500 mb-6">
              Are you sure you want to delete this provider? This action cannot be undone.
            </p>
            <div className="flex justify-end space-x-4">
              <button
                onClick={() => setConfirmDelete(null)}
                className="px-4 py-2 text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300"
              >
                Cancel
              </button>
              <button
                onClick={confirmDeleteProvider}
                className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
              >
                Delete
              </button>
            </div>
          </div>
        </div>
      )}
    </Modal>
  );
};

export default AISettings;
