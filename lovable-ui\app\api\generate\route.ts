import { NextRequest } from "next/server";
import { query } from "@anthropic-ai/claude-code";
import { AIConfigService } from "@/lib/services/ai-config-service";
import { AIProviderService } from "@/lib/services/ai-provider-service";

export async function POST(req: NextRequest) {
  try {
    const { prompt, useNewSystem } = await req.json();

    if (!prompt) {
      return new Response(
        JSON.stringify({ error: "Prompt is required" }),
        { status: 400, headers: { "Content-Type": "application/json" } }
      );
    }

    console.log("[API] Starting code generation for prompt:", prompt);

    // Check if we should use the new AI configuration system
    const configService = AIConfigService.getInstance();
    const shouldUseNewSystem = useNewSystem && configService.hasEnabledProviders();

    if (shouldUseNewSystem) {
      return await handleNewSystemGeneration(prompt, configService);
    }
    
    // Create a streaming response
    const encoder = new TextEncoder();
    const stream = new TransformStream();
    const writer = stream.writable.getWriter();
    
    // Start the async generation
    (async () => {
      try {
        const abortController = new AbortController();
        let messageCount = 0;
        
        for await (const message of query({
          prompt: prompt,
          abortController: abortController,
          options: {
            maxTurns: 10,
            allowedTools: [
              "Read",
              "Write",
              "Edit",
              "MultiEdit",
              "Bash",
              "LS",
              "Glob",
              "Grep",
              "WebSearch",
              "WebFetch"
            ]
          }
        })) {
          messageCount++;
          console.log(`[API] Message ${messageCount} - Type: ${message.type}`);
          
          // Log specific details based on message type
          if (message.type === 'tool_use') {
            console.log(`[API] Tool use: ${(message as any).name}`);
          } else if (message.type === 'result') {
            console.log(`[API] Result: ${(message as any).subtype}`);
          }
          
          // Send the message to the client
          await writer.write(
            encoder.encode(`data: ${JSON.stringify(message)}\n\n`)
          );
        }
        
        console.log(`[API] Generation complete. Total messages: ${messageCount}`);
        
        // Send completion signal
        await writer.write(encoder.encode("data: [DONE]\n\n"));
      } catch (error: any) {
        console.error("[API] Error during generation:", error);
        await writer.write(
          encoder.encode(`data: ${JSON.stringify({ error: error.message })}\n\n`)
        );
      } finally {
        await writer.close();
      }
    })();
    
    return new Response(stream.readable, {
      headers: {
        "Content-Type": "text/event-stream",
        "Cache-Control": "no-cache",
        "Connection": "keep-alive",
      },
    });
    
  } catch (error: any) {
    console.error("[API] Error:", error);
    return new Response(
      JSON.stringify({ error: error.message || "Internal server error" }),
      { status: 500, headers: { "Content-Type": "application/json" } }
    );
  }
}

async function handleNewSystemGeneration(prompt: string, configService: AIConfigService) {
  try {
    const defaultProvider = configService.getDefaultProvider();
    const defaultModel = configService.getDefaultModel();

    if (!defaultProvider || !defaultModel) {
      return new Response(
        JSON.stringify({ error: "No default AI provider or model configured" }),
        { status: 400, headers: { "Content-Type": "application/json" } }
      );
    }

    console.log(`[API] Using new system with ${defaultProvider.name} - ${defaultModel}`);

    const aiService = AIProviderService.getInstance();
    const streamResult = await aiService.streamText(
      defaultProvider,
      defaultModel,
      prompt,
      configService.getConfig().defaultParameters
    );

    const encoder = new TextEncoder();
    const stream = new TransformStream();
    const writer = stream.writable.getWriter();

    // Start streaming in the background
    (async () => {
      try {
        for await (const chunk of streamResult.textStream) {
          await writer.write(
            encoder.encode(`data: ${JSON.stringify({
              type: 'text',
              content: chunk
            })}\n\n`)
          );
        }

        // Send completion signal
        await writer.write(encoder.encode("data: [DONE]\n\n"));
      } catch (error: any) {
        console.error("[API] New system streaming error:", error);
        await writer.write(
          encoder.encode(`data: ${JSON.stringify({ error: error.message })}\n\n`)
        );
      } finally {
        await writer.close();
      }
    })();

    return new Response(stream.readable, {
      headers: {
        "Content-Type": "text/event-stream",
        "Cache-Control": "no-cache",
        "Connection": "keep-alive",
      },
    });
  } catch (error: any) {
    console.error("[API] New system error:", error);
    return new Response(
      JSON.stringify({ error: error.message || "New system generation failed" }),
      { status: 500, headers: { "Content-Type": "application/json" } }
    );
  }
}