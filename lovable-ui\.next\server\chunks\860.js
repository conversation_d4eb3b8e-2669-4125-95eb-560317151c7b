exports.id=860,exports.ids=[860],exports.modules={5130:(e,o,r)=>{Promise.resolve().then(r.bind(r,6719))},2688:(e,o,r)=>{Promise.resolve().then(r.t.bind(r,2994,23)),Promise.resolve().then(r.t.bind(r,6114,23)),Promise.resolve().then(r.t.bind(r,9727,23)),Promise.resolve().then(r.t.bind(r,9671,23)),Promise.resolve().then(r.t.bind(r,1868,23)),Promise.resolve().then(r.t.bind(r,4759,23))},6361:(e,o,r)=>{"use strict";r.d(o,{Z:()=>s});var t=r(326);function s(){return(0,t.jsxs)("nav",{className:"absolute top-0 left-0 right-0 z-20 flex items-center justify-between px-6 py-4",children:[(0,t.jsxs)("div",{className:"flex items-center gap-10",children:[(0,t.jsxs)("a",{href:"/",className:"flex items-center gap-2 text-2xl font-semibold text-white hover:opacity-90 transition-opacity",children:[t.jsx("span",{className:"inline-block w-6 h-6 rounded-sm bg-gradient-to-br from-orange-400 via-pink-500 to-blue-500"}),"Lovable"]}),(0,t.jsxs)("div",{className:"hidden md:flex items-center gap-8 text-sm text-gray-300",children:[t.jsx("a",{href:"#",className:"hover:text-white transition-colors",children:"Community"}),t.jsx("a",{href:"#",className:"hover:text-white transition-colors",children:"Enterprise"}),t.jsx("a",{href:"#",className:"hover:text-white transition-colors",children:"Learn"}),t.jsx("a",{href:"#",className:"hover:text-white transition-colors",children:"Shipped"})]})]}),(0,t.jsxs)("div",{className:"flex items-center gap-4 text-sm",children:[(0,t.jsxs)("a",{href:"/settings",className:"text-gray-300 hover:text-white transition-colors flex items-center gap-1",title:"AI Settings",children:[(0,t.jsxs)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:[t.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"}),t.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"})]}),"Settings"]}),t.jsx("a",{href:"#",className:"text-gray-300 hover:text-white transition-colors",children:"Log in"}),t.jsx("a",{href:"#",className:"px-4 py-2 bg-white text-black rounded-lg font-semibold hover:bg-gray-100 transition-colors",children:"Get started"})]})]})}r(7577)},6719:(e,o,r)=>{"use strict";r.d(o,{AIConfigProvider:()=>p,m:()=>c});var t=r(326),s=r(7577),i=r(729);function n(e,o){switch(o.type){case"SET_CONFIG":return{...e,config:o.payload,error:null};case"ADD_PROVIDER":return{...e,config:{...e.config,providers:[...e.config.providers,o.payload]}};case"UPDATE_PROVIDER":return{...e,config:{...e.config,providers:e.config.providers.map(e=>e.id===o.payload.id?{...e,...o.payload.config}:e)}};case"REMOVE_PROVIDER":let r=e.config.providers.filter(e=>e.id!==o.payload);return{...e,config:{...e.config,providers:r,defaultProvider:e.config.defaultProvider===o.payload?r[0]?.id:e.config.defaultProvider}};case"SET_DEFAULT_PROVIDER":return{...e,config:{...e.config,defaultProvider:o.payload}};case"SET_DEFAULT_MODEL":return{...e,config:{...e.config,defaultModel:o.payload}};case"UPDATE_DEFAULT_PARAMETERS":return{...e,config:{...e.config,defaultParameters:{...e.config.defaultParameters,...o.payload}}};case"TOGGLE_PROVIDER":return{...e,config:{...e.config,providers:e.config.providers.map(e=>e.id===o.payload?{...e,enabled:!e.enabled}:e)}};case"RESET_CONFIG":return{...e,config:i.T,error:null};default:return e}}let a={config:i.T,isLoading:!1,error:null},d=(0,s.createContext)(void 0),l="ai-config";function p({children:e}){let[o,r]=(0,s.useReducer)(n,a),i=async()=>{try{localStorage.setItem(l,JSON.stringify(o.config))}catch(e){console.error("Failed to save AI config:",e)}},p=async()=>{try{let e=localStorage.getItem(l);if(e){let o=JSON.parse(e);r({type:"SET_CONFIG",payload:o})}}catch(e){console.error("Failed to load AI config:",e)}},c=async e=>{let r=o.config.providers.find(o=>o.id===e);if(!r)return{success:!1,error:"Provider not found"};try{let e=await fetch("/api/ai/test-connection",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({provider:r})});return await e.json()}catch(e){return{success:!1,error:e instanceof Error?e.message:"Connection test failed"}}};return t.jsx(d.Provider,{value:{state:o,actions:{setConfig:e=>r({type:"SET_CONFIG",payload:e}),addProvider:e=>r({type:"ADD_PROVIDER",payload:e}),updateProvider:(e,o)=>r({type:"UPDATE_PROVIDER",payload:{id:e,config:o}}),removeProvider:e=>r({type:"REMOVE_PROVIDER",payload:e}),setDefaultProvider:e=>r({type:"SET_DEFAULT_PROVIDER",payload:e}),setDefaultModel:e=>r({type:"SET_DEFAULT_MODEL",payload:e}),updateDefaultParameters:e=>r({type:"UPDATE_DEFAULT_PARAMETERS",payload:e}),toggleProvider:e=>r({type:"TOGGLE_PROVIDER",payload:e}),resetConfig:()=>r({type:"RESET_CONFIG"}),saveConfig:i,loadConfig:p,testConnection:c,validateProvider:e=>{let o=[];return e.name.trim()||o.push("Provider name is required"),e.apiKey.trim()||o.push("API key is required"),"custom"!==e.provider||(e.baseURL?.trim()||o.push("Base URL is required for custom providers"),e.modelName?.trim()||o.push("Model name is required for custom providers")),{isValid:0===o.length,errors:o}}}},children:e})}function c(){let e=(0,s.useContext)(d);if(void 0===e)throw Error("useAIConfig must be used within an AIConfigProvider");return e}},729:(e,o,r)=>{"use strict";r.d(o,{T:()=>s,k:()=>t});let t={openai:[{id:"gpt-4o",name:"GPT-4o",provider:"openai",maxTokens:4096,supportsStreaming:!0,supportsTools:!0,supportsVision:!0,contextWindow:128e3},{id:"gpt-4o-mini",name:"GPT-4o Mini",provider:"openai",maxTokens:16384,supportsStreaming:!0,supportsTools:!0,supportsVision:!0,contextWindow:128e3},{id:"gpt-4-turbo",name:"GPT-4 Turbo",provider:"openai",maxTokens:4096,supportsStreaming:!0,supportsTools:!0,supportsVision:!0,contextWindow:128e3},{id:"gpt-3.5-turbo",name:"GPT-3.5 Turbo",provider:"openai",maxTokens:4096,supportsStreaming:!0,supportsTools:!0,supportsVision:!1,contextWindow:16385}],anthropic:[{id:"claude-3-5-sonnet-20241022",name:"Claude 3.5 Sonnet",provider:"anthropic",maxTokens:8192,supportsStreaming:!0,supportsTools:!0,supportsVision:!0,contextWindow:2e5},{id:"claude-3-5-haiku-20241022",name:"Claude 3.5 Haiku",provider:"anthropic",maxTokens:8192,supportsStreaming:!0,supportsTools:!0,supportsVision:!0,contextWindow:2e5},{id:"claude-3-opus-20240229",name:"Claude 3 Opus",provider:"anthropic",maxTokens:4096,supportsStreaming:!0,supportsTools:!0,supportsVision:!0,contextWindow:2e5}],google:[{id:"gemini-1.5-pro",name:"Gemini 1.5 Pro",provider:"google",maxTokens:8192,supportsStreaming:!0,supportsTools:!0,supportsVision:!0,contextWindow:2e6},{id:"gemini-1.5-flash",name:"Gemini 1.5 Flash",provider:"google",maxTokens:8192,supportsStreaming:!0,supportsTools:!0,supportsVision:!0,contextWindow:1e6}],cohere:[{id:"command-r-plus",name:"Command R+",provider:"cohere",maxTokens:4096,supportsStreaming:!0,supportsTools:!0,supportsVision:!1,contextWindow:128e3},{id:"command-r",name:"Command R",provider:"cohere",maxTokens:4096,supportsStreaming:!0,supportsTools:!0,supportsVision:!1,contextWindow:128e3}],mistral:[{id:"mistral-large-latest",name:"Mistral Large",provider:"mistral",maxTokens:8192,supportsStreaming:!0,supportsTools:!0,supportsVision:!1,contextWindow:128e3},{id:"mistral-medium-latest",name:"Mistral Medium",provider:"mistral",maxTokens:8192,supportsStreaming:!0,supportsTools:!0,supportsVision:!1,contextWindow:32e3}],custom:[]},s={providers:[],defaultParameters:{temperature:.7,maxTokens:4096,topP:1,frequencyPenalty:0,presencePenalty:0}}},5047:(e,o,r)=>{"use strict";var t=r(7389);r.o(t,"useRouter")&&r.d(o,{useRouter:function(){return t.useRouter}}),r.o(t,"useSearchParams")&&r.d(o,{useSearchParams:function(){return t.useSearchParams}})},9058:(e,o,r)=>{"use strict";r.r(o),r.d(o,{default:()=>u,metadata:()=>c});var t=r(9510),s=r(7366),i=r.n(s);r(7272);var n=r(8570);let a=(0,n.createProxy)(String.raw`C:\Users\<USER>\Documents\github\lovable-clone\lovable-ui\lib\contexts\ai-config-context.tsx`),{__esModule:d,$$typeof:l}=a;a.default;let p=(0,n.createProxy)(String.raw`C:\Users\<USER>\Documents\github\lovable-clone\lovable-ui\lib\contexts\ai-config-context.tsx#AIConfigProvider`);(0,n.createProxy)(String.raw`C:\Users\<USER>\Documents\github\lovable-clone\lovable-ui\lib\contexts\ai-config-context.tsx#useAIConfig`);let c={title:"Lovable Clone - AI-Powered Code Generation",description:"Build applications faster with AI-powered code generation"};function u({children:e}){return t.jsx("html",{lang:"en",children:t.jsx("body",{className:i().className,children:t.jsx(p,{children:e})})})}},7272:()=>{}};