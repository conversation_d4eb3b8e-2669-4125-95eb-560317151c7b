/**
 * AI Provider Configuration Types
 * 
 * This file defines the types for configuring AI providers and models.
 */

import { z } from 'zod';

/**
 * Supported AI providers
 */
export enum AIProvider {
  OPENAI = 'openai',
  ANTHROPIC = 'anthropic',
  GOOGLE = 'google',
  COHERE = 'cohere',
  CUSTOM_OPENAI = 'custom_openai',
}

/**
 * Base configuration for all AI providers
 */
export const BaseProviderConfigSchema = z.object({
  id: z.string().uuid().optional(),
  name: z.string().min(1, "Name is required"),
  provider: z.nativeEnum(AIProvider),
  apiKey: z.string().min(1, "API key is required"),
  isDefault: z.boolean().default(false),
  createdAt: z.date().optional(),
  updatedAt: z.date().optional(),
});

/**
 * OpenAI specific configuration
 */
export const OpenAIConfigSchema = BaseProviderConfigSchema.extend({
  provider: z.literal(AIProvider.OPENAI),
  model: z.string().min(1, "Model is required"),
  temperature: z.number().min(0).max(2).default(0.7),
  maxTokens: z.number().min(1).max(100000).default(4000),
  topP: z.number().min(0).max(1).default(1),
  frequencyPenalty: z.number().min(-2).max(2).default(0),
  presencePenalty: z.number().min(-2).max(2).default(0),
});

/**
 * Anthropic specific configuration
 */
export const AnthropicConfigSchema = BaseProviderConfigSchema.extend({
  provider: z.literal(AIProvider.ANTHROPIC),
  model: z.string().min(1, "Model is required"),
  temperature: z.number().min(0).max(1).default(0.7),
  maxTokens: z.number().min(1).max(100000).default(4000),
  topP: z.number().min(0).max(1).default(1),
  topK: z.number().min(0).max(500).default(0),
});

/**
 * Google specific configuration
 */
export const GoogleConfigSchema = BaseProviderConfigSchema.extend({
  provider: z.literal(AIProvider.GOOGLE),
  model: z.string().min(1, "Model is required"),
  temperature: z.number().min(0).max(1).default(0.7),
  maxTokens: z.number().min(1).max(100000).default(4000),
  topP: z.number().min(0).max(1).default(1),
  topK: z.number().min(0).max(500).default(0),
});

/**
 * Cohere specific configuration
 */
export const CohereConfigSchema = BaseProviderConfigSchema.extend({
  provider: z.literal(AIProvider.COHERE),
  model: z.string().min(1, "Model is required"),
  temperature: z.number().min(0).max(2).default(0.7),
  maxTokens: z.number().min(1).max(100000).default(4000),
  topP: z.number().min(0).max(1).default(1),
  frequencyPenalty: z.number().min(0).max(1).default(0),
  presencePenalty: z.number().min(0).max(1).default(0),
});

/**
 * Custom OpenAI-compatible API configuration
 */
export const CustomOpenAIConfigSchema = BaseProviderConfigSchema.extend({
  provider: z.literal(AIProvider.CUSTOM_OPENAI),
  baseUrl: z.string().url("Must be a valid URL"),
  model: z.string().min(1, "Model is required"),
  temperature: z.number().min(0).max(2).default(0.7),
  maxTokens: z.number().min(1).max(100000).default(4000),
  topP: z.number().min(0).max(1).default(1),
  frequencyPenalty: z.number().min(-2).max(2).default(0),
  presencePenalty: z.number().min(-2).max(2).default(0),
});

/**
 * Union of all provider configurations
 */
export const ProviderConfigSchema = z.discriminatedUnion('provider', [
  OpenAIConfigSchema,
  AnthropicConfigSchema,
  GoogleConfigSchema,
  CohereConfigSchema,
  CustomOpenAIConfigSchema,
]);

/**
 * Type definitions based on the schemas
 */
export type BaseProviderConfig = z.infer<typeof BaseProviderConfigSchema>;
export type OpenAIConfig = z.infer<typeof OpenAIConfigSchema>;
export type AnthropicConfig = z.infer<typeof AnthropicConfigSchema>;
export type GoogleConfig = z.infer<typeof GoogleConfigSchema>;
export type CohereConfig = z.infer<typeof CohereConfigSchema>;
export type CustomOpenAIConfig = z.infer<typeof CustomOpenAIConfigSchema>;
export type ProviderConfig = z.infer<typeof ProviderConfigSchema>;

/**
 * Default models for each provider
 */
export const DEFAULT_MODELS = {
  [AIProvider.OPENAI]: [
    'gpt-4o',
    'gpt-4o-mini',
    'gpt-4-turbo',
    'gpt-4',
    'gpt-3.5-turbo',
  ],
  [AIProvider.ANTHROPIC]: [
    'claude-3-opus-20240229',
    'claude-3-sonnet-20240229',
    'claude-3-haiku-20240307',
    'claude-2.1',
    'claude-2.0',
    'claude-instant-1.2',
  ],
  [AIProvider.GOOGLE]: [
    'gemini-1.5-pro',
    'gemini-1.5-flash',
    'gemini-1.0-pro',
    'gemini-1.0-ultra',
  ],
  [AIProvider.COHERE]: [
    'command-r-plus',
    'command-r',
    'command',
  ],
  [AIProvider.CUSTOM_OPENAI]: [],
};

/**
 * Default configurations for each provider
 */
export const DEFAULT_CONFIGS: Record<AIProvider, Partial<ProviderConfig>> = {
  [AIProvider.OPENAI]: {
    provider: AIProvider.OPENAI,
    model: 'gpt-4o',
    temperature: 0.7,
    maxTokens: 4000,
    topP: 1,
    frequencyPenalty: 0,
    presencePenalty: 0,
  },
  [AIProvider.ANTHROPIC]: {
    provider: AIProvider.ANTHROPIC,
    model: 'claude-3-sonnet-20240229',
    temperature: 0.7,
    maxTokens: 4000,
    topP: 1,
    topK: 0,
  },
  [AIProvider.GOOGLE]: {
    provider: AIProvider.GOOGLE,
    model: 'gemini-1.5-pro',
    temperature: 0.7,
    maxTokens: 4000,
    topP: 1,
    topK: 0,
  },
  [AIProvider.COHERE]: {
    provider: AIProvider.COHERE,
    model: 'command-r-plus',
    temperature: 0.7,
    maxTokens: 4000,
    topP: 1,
    frequencyPenalty: 0,
    presencePenalty: 0,
  },
  [AIProvider.CUSTOM_OPENAI]: {
    provider: AIProvider.CUSTOM_OPENAI,
    baseUrl: 'https://api.example.com/v1',
    model: 'custom-model',
    temperature: 0.7,
    maxTokens: 4000,
    topP: 1,
    frequencyPenalty: 0,
    presencePenalty: 0,
  },
};
