# AI Provider API Keys
# Configure your AI provider API keys here

# Anthropic API Key (for Claude models)
# Get your key from: https://console.anthropic.com/dashboard
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# OpenAI API Key (for GPT models)
# Get your key from: https://platform.openai.com/api-keys
OPENAI_API_KEY=your_openai_api_key_here

# Google AI API Key (for Gemini models)
# Get your key from: https://aistudio.google.com/app/apikey
GOOGLE_AI_API_KEY=your_google_ai_api_key_here

# Cohere API Key (for Command models)
# Get your key from: https://dashboard.cohere.ai/api-keys
COHERE_API_KEY=your_cohere_api_key_here

# Daytona API Key (for sandbox environments)
# Get your key from: https://www.daytona.io/
DAYTONA_API_KEY=your_daytona_api_key_here

# Note: You don't need all API keys - only configure the providers you want to use
# The application will work with any combination of the above providers
