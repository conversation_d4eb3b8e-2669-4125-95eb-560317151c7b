/**
 * OpenCoder Adapter
 * 
 * This file provides an adapter to use opencoder as a replacement for claude-code.
 * It mimics the claude-code API to maintain compatibility with existing code.
 */

import { openai, anthropic, google, cohere } from 'opencoder';
import { aiConfigManager, AIProvider } from './ai-config';
import { v4 as uuidv4 } from 'uuid';

// Define the SDKMessage type to match claude-code's API
export type SDKMessage = {
  type: string;
  text?: string;
  name?: string;
  input?: any;
  result?: any;
  subtype?: string;
};

/**
 * Adapter function to mimic the Claude Code SDK's query function
 * This allows existing code to continue working with opencoder
 */
export async function* query(options: {
  prompt: string;
  abortController?: AbortController;
  options?: {
    maxTurns?: number;
    allowedTools?: string[];
  };
}): AsyncGenerator<SDKMessage> {
  // Get the default provider from our AI config system
  const defaultProvider = aiConfigManager.getDefaultProvider();
  
  if (!defaultProvider) {
    throw new Error('No AI provider configured. Please configure an AI provider in the settings.');
  }

  try {
    // Create a text message to simulate <PERSON>'s response
    yield {
      type: 'text',
      text: `I'll help you with that. I'm using the ${defaultProvider.name} AI provider.`,
    };
    
    // Create the appropriate model based on the provider
    let model;
    switch (defaultProvider.provider) {
      case AIProvider.OPENAI:
        model = openai({
          apiKey: defaultProvider.apiKey,
        })(defaultProvider.model);
        break;
      case AIProvider.ANTHROPIC:
        model = anthropic({
          apiKey: defaultProvider.apiKey,
        })(defaultProvider.model);
        break;
      case AIProvider.GOOGLE:
        model = google({
          apiKey: defaultProvider.apiKey,
        })(defaultProvider.model);
        break;
      case AIProvider.COHERE:
        model = cohere({
          apiKey: defaultProvider.apiKey,
        })(defaultProvider.model);
        break;
      case AIProvider.CUSTOM_OPENAI:
        model = openai({
          apiKey: defaultProvider.apiKey,
          baseURL: (defaultProvider as any).baseUrl,
        })(defaultProvider.model);
        break;
      default:
        throw new Error(`Unsupported provider: ${defaultProvider.provider}`);
    }
    
    // Start streaming the response
    const stream = await aiConfigManager.streamText(options.prompt);
    
    let accumulatedText = '';
    
    for await (const chunk of stream) {
      accumulatedText += chunk;
      
      // Yield the accumulated text as a message
      yield {
        type: 'text',
        text: accumulatedText,
      };
    }
    
    // Simulate tool usage for compatibility
    // This is a simplified version since opencoder handles tools differently
    if (options.options?.allowedTools?.includes('Read')) {
      yield {
        type: 'tool_use',
        name: 'Read',
        input: {
          file_path: 'README.md',
        },
      };
      
      yield {
        type: 'result',
        subtype: 'success',
        result: {
          content: 'File content would be here in a real implementation',
        },
      };
    }
    
    // Simulate a final message
    yield {
      type: 'text',
      text: 'I hope that helps! Let me know if you need anything else.',
    };
  } catch (error: any) {
    // Yield an error message
    yield {
      type: 'text',
      text: `Error: ${error.message || 'An unknown error occurred'}`,
    };
  }
}

/**
 * Initialize the opencoder adapter with an API key
 * This function creates a provider configuration if one doesn't exist
 */
export function initializeOpenCoderAdapter(apiKey: string, provider: AIProvider = AIProvider.OPENAI) {
  if (!apiKey) {
    console.warn('No API key provided for opencoder adapter');
    return;
  }
  
  // Check if a provider already exists
  const providers = aiConfigManager.getProviders();
  const existingProvider = providers.find(p => p.provider === provider);
  
  if (existingProvider) {
    // Update the API key if it's different
    if (existingProvider.apiKey !== apiKey) {
      aiConfigManager.saveProvider({
        ...existingProvider,
        apiKey,
      });
    }
    
    // Set as default if not already
    if (!existingProvider.isDefault) {
      aiConfigManager.setDefault(existingProvider.id!);
    }
  } else {
    // Create a new provider
    let defaultModel = '';
    switch (provider) {
      case AIProvider.OPENAI:
        defaultModel = 'gpt-4o';
        break;
      case AIProvider.ANTHROPIC:
        defaultModel = 'claude-3-sonnet-20240229';
        break;
      case AIProvider.GOOGLE:
        defaultModel = 'gemini-1.5-pro';
        break;
      case AIProvider.COHERE:
        defaultModel = 'command-r-plus';
        break;
      default:
        defaultModel = 'gpt-4o';
    }
    
    aiConfigManager.saveProvider({
      id: uuidv4(),
      name: provider === AIProvider.OPENAI ? 'OpenAI' : 
            provider === AIProvider.ANTHROPIC ? 'Anthropic' :
            provider === AIProvider.GOOGLE ? 'Google' :
            provider === AIProvider.COHERE ? 'Cohere' : 'Custom',
      provider,
      apiKey,
      model: defaultModel,
      temperature: 0.7,
      maxTokens: 4000,
      isDefault: true,
    });
  }
}
